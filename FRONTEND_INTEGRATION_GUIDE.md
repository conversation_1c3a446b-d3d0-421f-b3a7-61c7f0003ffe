# Frontend Integration Guide
## EllahAI Flutter App → Django Backend Integration

This document provides comprehensive guidance for integrating the Flutter frontend with the Django backend. It covers all aspects of the integration including authentication, user management, chat functionality, gamification, shop system, and Unity integration.

## 📋 Overview

The Django backend is now ready for integration with the Flutter frontend. The backend implements all the required functionality as specified in the requirements document, including:

- REST API endpoints for all required functionality
- JWT authentication for both HTTP and WebSocket connections
- Real-time chat with WebSocket support
- Gamification system with XP, levels, achievements, and rewards
- Shop system with virtual currency and item purchases
- Unity integration for avatar customization

## 🔑 Authentication

### Authentication Endpoints

```
POST /api/auth/register/                # Register a new user
POST /api/auth/login/                   # Login with email/password
POST /api/auth/refresh/                 # Refresh JWT token
POST /api/auth/logout/                  # Logout and invalidate token
POST /api/auth/google/                  # Login with Google OAuth
POST /api/auth/apple/                   # Login with Apple Sign In
```

### Authentication Flow

1. **Registration/Login**: Send credentials to the appropriate endpoint
2. **Token Reception**: Receive JWT access and refresh tokens
3. **Token Storage**: Store tokens securely in the Flutter app
4. **API Requests**: Include the access token in the Authorization header
5. **Token Refresh**: Use the refresh token to get a new access token when expired

### Example Authentication Request

```dart
// Example Flutter code for login
final response = await http.post(
  Uri.parse('https://api.example.com/api/auth/login/'),
  headers: {'Content-Type': 'application/json'},
  body: jsonEncode({
    'email': '<EMAIL>',
    'password': 'password123'
  }),
);

if (response.statusCode == 200) {
  final data = jsonDecode(response.body);
  final accessToken = data['tokens']['access'];
  final refreshToken = data['tokens']['refresh'];
  
  // Store tokens securely
  await secureStorage.write(key: 'access_token', value: accessToken);
  await secureStorage.write(key: 'refresh_token', value: refreshToken);
}
```

### WebSocket Authentication

WebSocket connections require JWT authentication. Include the token in the connection URL:

```dart
// Example WebSocket connection with JWT
final token = await secureStorage.read(key: 'access_token');
final ws = WebSocketChannel.connect(
  Uri.parse('ws://api.example.com/ws/chat/?token=$token'),
);
```

## 👤 User Management

### User Management Endpoints

```
GET /api/user/profile/                  # Get user profile
PUT /api/user/profile/                  # Update user profile
GET /api/user/settings/                 # Get user settings
PUT /api/user/settings/                 # Update user settings
GET /api/user/progress/                 # Get user progress
POST /api/user/progress/update/         # Update user progress
```

### User Model

The backend User model includes all fields required by the Flutter app:

```json
{
  "id": "uuid",
  "username": "username",
  "email": "<EMAIL>",
  "first_name": "First",
  "last_name": "Last",
  "selected_personality": "caringFriend",
  "selected_environment": "cozy_room",
  "owned_environments": ["cozy_room", "beach"],
  "owned_outfits": ["casual", "formal"],
  "owned_pets": ["cat", "dog"],
  "preferences": {
    "theme": "dark",
    "notifications": true
  },
  "progress": {
    "xp": 150,
    "level": 3,
    "hearts": 5,
    "messages_count": 42,
    "voice_messages_count": 12,
    "achievements": ["first_chat", "daily_streak_7"],
    "total_time_spent": 3600
  }
}
```

## 💬 Chat System

### Chat Endpoints

```
GET /api/conversations/                 # List conversations
POST /api/conversations/                # Create a new conversation
GET /api/conversations/{id}/messages/   # Get messages in a conversation
POST /api/conversations/{id}/messages/  # Send a message
DELETE /api/conversations/{id}/         # Delete a conversation
```

### WebSocket Chat Protocol

The WebSocket connection supports real-time chat with the following message types:

#### Connection

```
ws://api.example.com/ws/chat/?token=<jwt_token>
```

#### Sending a Text Message

```json
{
  "type": "text_message",
  "content": "Hello, how are you?",
  "conversation_id": "uuid-here"
}
```

#### Sending an Audio Message

```json
{
  "type": "audio_chunk",
  "data": "base64-encoded-audio-data",
  "chunk_id": "uuid-here",
  "is_final": true,
  "conversation_id": "uuid-here"
}
```

#### Typing Indicators

```json
{
  "type": "typing_start"
}
```

```json
{
  "type": "typing_stop"
}
```

#### Receiving AI Responses

The backend will send streaming responses:

```json
{
  "type": "llm_response_chunk",
  "content": "Hello! I'm",
  "chunk_id": "uuid-here",
  "is_final": false
}
```

```json
{
  "type": "llm_response_chunk",
  "content": " doing well. How are you?",
  "chunk_id": "uuid-here",
  "is_final": true
}
```

#### Receiving Transcriptions

```json
{
  "type": "transcription_partial",
  "text": "Hello how are",
  "confidence": 0.95,
  "chunk_id": "uuid-here",
  "is_partial": true
}
```

```json
{
  "type": "transcription_partial",
  "text": "Hello how are you today?",
  "confidence": 0.98,
  "chunk_id": "uuid-here",
  "is_partial": false
}
```

#### Receiving Emotion Detection

```json
{
  "type": "emotion_detected",
  "emotions": [
    {"name": "happy", "score": 0.8},
    {"name": "neutral", "score": 0.15},
    {"name": "sad", "score": 0.05}
  ],
  "confidence_score": 0.9,
  "chunk_id": "uuid-here"
}
```

## 🎮 Gamification System

### Gamification Endpoints

```
GET /api/user/level/                    # Get user level info
GET /api/user/achievements/             # Get user achievements
POST /api/achievements/unlock/          # Unlock an achievement
GET /api/user/wallet/                   # Get user wallet
POST /api/user/wallet/add-currency/     # Add currency to wallet
GET /api/daily-rewards/                 # Get daily rewards
POST /api/daily-rewards/claim/          # Claim daily reward
```

### XP and Level System

The backend implements a progressive XP system:

- XP is awarded for various actions (chatting, completing achievements, etc.)
- Levels are calculated based on accumulated XP
- Each level requires more XP than the previous one
- Level-ups may unlock new features or items

### Achievement System

Achievements are tracked and can be unlocked based on various criteria:

```json
{
  "id": "uuid",
  "name": "Chatty Friend",
  "description": "Send 100 messages",
  "category": "chat",
  "rarity": "rare",
  "xp_reward": 50,
  "currency_reward": 20,
  "requirement_type": "message_count",
  "requirement_value": 100,
  "is_hidden": false
}
```

### Daily Rewards

The backend supports a daily reward system:

```json
{
  "day": 7,
  "xp_reward": 50,
  "currency_reward": 100,
  "is_bonus_day": true
}
```

## 🛒 Shop System

### Shop Endpoints

```
GET /api/shop/items/                    # List all shop items
GET /api/shop/items/{category}/         # List items by category
POST /api/shop/purchase/                # Purchase an item
GET /api/user/inventory/                # Get user inventory
POST /api/shop/preview/                 # Preview an item
```

### Shop Items

Shop items include environments, outfits, accessories, companions, and pets:

```json
{
  "id": "uuid",
  "name": "Beach Environment",
  "description": "A relaxing beach setting",
  "price": 500,
  "item_type": "environment",
  "image_url": "https://example.com/beach.png",
  "preview_data": {
    "unity_asset_id": "beach_env_01",
    "preview_image": "https://example.com/beach_preview.png"
  },
  "is_active": true,
  "is_featured": true,
  "level_requirement": 2,
  "relationship_level_requirement": 1
}
```

### Purchase Flow

1. Check if the user can purchase the item (`can_purchase` method)
2. Process the purchase (deduct currency, add to inventory)
3. Update the user's owned items list
4. Return the updated inventory and wallet

### Relationship Gates

Some items require specific relationship levels to unlock:

```json
{
  "level": 2,
  "unlocked_items": ["romantic_outfit", "special_environment"]
}
```

## 🤖 Agent System

### Agent System Overview

The backend includes a sophisticated agent system with specialized AI agents for different domains and comprehensive task management capabilities.

### Agent Domains

The system supports multiple specialized agents:

- **General Agent**: Warm, playful, emotionally intelligent companion
  - Natural conversation and emotional support
  - Personality adaptation based on user mood
  - Memory of previous conversations
  - Supportive and encouraging responses

- **Dev Agent**: Programming and technology expertise
  - Code examples and explanations
  - Debugging assistance
  - Best practices guidance
  - Multiple programming languages
  - Architecture and design patterns

- **Business Agent**: Professional and business topics
  - Business strategy and planning
  - Finance and investment advice
  - Marketing and growth strategies
  - Professional development guidance
  - Workplace communication tips

- **Learning Agent**: Education and knowledge sharing
  - Educational content and explanations
  - Learning path recommendations
  - Study techniques and strategies
  - Knowledge assessment and quizzes
  - Personalized learning experiences

- **Music Agent**: Music theory, recommendations, and analysis
  - Music theory explanations
  - Song recommendations
  - Genre exploration
  - Music analysis and insights
  - Playlist creation assistance

- **Trivia Agent**: Fun facts and knowledge quizzes
  - Interactive trivia games
  - Educational facts
  - Knowledge challenges
  - Fun learning experiences
  - Customizable difficulty levels

### Agent Endpoints

```
POST /api/agents/process-query/          # Process query with domain routing
GET /api/agents/domains/                 # Get available agent domains
POST /api/agents/switch-domain/          # Switch to specific agent domain
```

### Agent Query Processing

```json
{
  "query": "How do I implement authentication in Flutter?",
  "user_id": "uuid",
  "conversation_history": [...],
  "emotion_context": {
    "primary_emotion": "curious",
    "intensity": 0.7
  },
  "memory_context": {
    "relevant_memories": [...],
    "user_preferences": {...}
  },
  "streaming": true,
  "auto_create_task": true
}
```

### Agent Response Format

```json
{
  "type": "agent_response",
  "content": "To implement authentication in Flutter...",
  "domain": "dev",
  "confidence": 0.95,
  "suggested_actions": [
    "create_task",
    "show_code_example"
  ],
  "emotion_adaptation": {
    "detected_emotion": "curious",
    "response_tone": "helpful_and_encouraging"
  }
}
```

## 📋 Task Management System

### Task Management Overview

The backend includes a comprehensive task management system that tracks complex tasks with multiple phases, progress monitoring, and intervention handling.

### Task Model

Tasks support detailed tracking with phases, progress, and status management:

```json
{
  "id": "uuid",
  "user": "user-uuid",
  "title": "Build Flutter Authentication System",
  "description": "Implement secure authentication with JWT tokens",
  "status": "running",
  "progress": 65.5,
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T11:45:00Z",
  "phases": [
    {
      "phase": "initialization",
      "title": "Project Setup",
      "description": "Initialize Flutter project and dependencies",
      "is_completed": true,
      "is_current": false,
      "sub_tasks": [
        {"title": "Create new Flutter project", "completed": true},
        {"title": "Add authentication dependencies", "completed": true}
      ]
    },
    {
      "phase": "analysis",
      "title": "Requirements Analysis",
      "description": "Analyze authentication requirements",
      "is_completed": true,
      "is_current": false,
      "sub_tasks": [...]
    },
    {
      "phase": "execution",
      "title": "Implementation",
      "description": "Implement authentication logic",
      "is_completed": false,
      "is_current": true,
      "sub_tasks": [...]
    }
  ],
  "error_message": null,
  "intervention_message": null
}
```

### Task Status Options

- `pending`: Task is created but not started
- `running`: Task is currently being executed
- `completed`: Task has been successfully completed
- `failed`: Task encountered an error and failed
- `needs_intervention`: Task requires user intervention
- `cancelled`: Task was cancelled by user

### Task Phases

- `initialization`: Project setup and preparation
- `analysis`: Requirements and planning analysis
- `planning`: Detailed planning and design
- `execution`: Main implementation work
- `debugging`: Error fixing and optimization
- `testing`: Testing and validation
- `finalization`: Final touches and documentation

### Task Management Endpoints

```
GET /api/agents/tasks/                   # List all user tasks
POST /api/agents/tasks/                  # Create a new task
GET /api/agents/tasks/{id}/              # Get specific task details
PUT /api/agents/tasks/{id}/              # Update task
DELETE /api/agents/tasks/{id}/           # Delete task
PATCH /api/agents/tasks/{id}/update_status/ # Update task status
PATCH /api/agents/tasks/{id}/update_progress/ # Update task progress
POST /api/agents/tasks/{id}/add_phase/   # Add new phase to task
GET /api/agents/tasks/sync_with_orchestrator/ # Sync with agent orchestrator
```

### Creating a Task

```json
{
  "title": "Build Flutter Authentication System",
  "description": "Implement secure authentication with JWT tokens and biometric login",
  "phases": [
    {
      "phase": "initialization",
      "title": "Project Setup",
      "description": "Initialize Flutter project and add authentication dependencies",
      "sub_tasks": [
        {"title": "Create new Flutter project", "completed": false},
        {"title": "Add http and shared_preferences packages", "completed": false},
        {"title": "Set up project structure", "completed": false}
      ]
    },
    {
      "phase": "analysis",
      "title": "Requirements Analysis",
      "description": "Analyze authentication requirements and design API integration",
      "sub_tasks": [
        {"title": "Define authentication flow", "completed": false},
        {"title": "Design API integration", "completed": false}
      ]
    }
  ]
}
```

### Updating Task Progress

```json
{
  "progress": 75.0,
  "current_phase": "execution"
}
```

### Updating Task Status

```json
{
  "status": "needs_intervention",
  "intervention_message": "Need clarification on biometric authentication requirements"
}
```

### Adding a Phase to Task

```json
{
  "phase": "testing",
  "title": "Testing and Validation",
  "description": "Test authentication flow and validate security",
  "sub_tasks": [
    {"title": "Unit tests for auth service", "completed": false},
    {"title": "Integration tests", "completed": false},
    {"title": "Security validation", "completed": false}
  ]
}
```

### Task Error Handling and Intervention

Tasks can encounter errors or require user intervention:

#### Error Response
```json
{
  "type": "task_error",
  "task_id": "uuid",
  "error_message": "Failed to connect to authentication service",
  "error_code": "AUTH_SERVICE_UNAVAILABLE",
  "suggested_resolution": "Check network connection and retry",
  "can_retry": true,
  "retry_count": 2
}
```

#### Intervention Required
```json
{
  "type": "task_intervention",
  "task_id": "uuid",
  "intervention_message": "Need clarification on biometric authentication requirements",
  "intervention_type": "user_input_required",
  "required_fields": [
    "biometric_type",
    "security_level",
    "fallback_method"
  ],
  "options": {
    "biometric_type": ["fingerprint", "face_id", "touch_id"],
    "security_level": ["basic", "enhanced", "maximum"]
  }
}
```

#### Task Recovery
```json
{
  "type": "task_recovery",
  "task_id": "uuid",
  "recovery_action": "retry_with_fallback",
  "fallback_method": "password_only",
  "estimated_completion_time": "5 minutes",
  "progress_after_recovery": 85.0
}
```

### Task Orchestration Integration

Tasks can be automatically created from agent queries and synchronized with the agent orchestrator:

```json
{
  "type": "task_creation",
  "task_id": "uuid",
  "title": "Build Flutter Authentication System",
  "description": "Auto-generated from user query",
  "domain": "dev",
  "estimated_complexity": "medium",
  "suggested_phases": [...]
}
```

### Agent Orchestrator Integration

The backend includes a sophisticated agent orchestrator that manages:

- **Domain Classification**: Automatically routes queries to appropriate specialized agents
- **Emotion Context**: Adapts responses based on user emotional state
- **Memory Integration**: Uses conversation history and user preferences
- **Task Creation**: Automatically creates tasks from complex queries
- **Streaming Responses**: Provides real-time streaming responses

### Memory System Integration

The agent system integrates with a comprehensive memory system:

```json
{
  "memory_context": {
    "relevant_memories": [
      {
        "id": "memory-uuid",
        "content": "User prefers detailed code examples",
        "salience_score": 0.85,
        "created_at": "2024-01-15T10:30:00Z"
      }
    ],
    "user_preferences": {
      "technical_level": "intermediate",
      "preferred_language": "python",
      "learning_style": "hands_on"
    },
    "conversation_context": {
      "recent_topics": ["flutter", "authentication"],
      "ongoing_tasks": ["task-uuid-1", "task-uuid-2"]
    }
  }
}
```

## 🧠 Memory Management System

### Memory Management Overview

The backend includes a comprehensive memory management system that allows users to store, retrieve, and manage their personal memories and preferences.

### Memory Types

The system supports multiple memory types:

- **semantic_profile**: User-specific stable preferences and characteristics
- **episodic_summary**: Summaries of conversations or interactions
- **general_knowledge**: General facts or domain knowledge
- **explicit_memory**: Specific facts or notes explicitly saved
- **personal_fact**: Personal information about the user
- **preference**: User preferences and choices
- **goal**: User goals and aspirations
- **relationship**: Information about user relationships
- **skill**: User skills and abilities
- **interest**: User interests and hobbies

### Memory Model

Memories include comprehensive tracking with salience scoring:

```json
{
  "id": "uuid",
  "user": "user-uuid",
  "content": "User prefers dark theme for UI",
  "memory_type": "preference",
  "memory_type_display": "User preferences and choices",
  "importance_score": 0.8,
  "personalness_score": 0.9,
  "actionability_score": 0.7,
  "salience_score": 0.79,
  "vector_id": "mem_abc123def456",
  "source_conversation": "conversation-uuid",
  "source_message": "message-uuid",
  "metadata": {
    "context": "UI discussion",
    "tags": ["theme", "preference"]
  },
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T11:45:00Z",
  "last_accessed": "2024-01-15T11:45:00Z",
  "is_active": true,
  "is_verified": false
}
```

### Memory Management Endpoints

```
GET /api/agents/memories/                    # List all user memories
POST /api/agents/memories/                   # Create a new memory
GET /api/agents/memories/{id}/               # Get specific memory details
PUT /api/agents/memories/{id}/               # Update memory
DELETE /api/agents/memories/{id}/            # Delete memory
GET /api/agents/memories/types/              # Get available memory types
GET /api/agents/memories/stats/              # Get memory statistics
GET /api/agents/memories/search/             # Search memories
POST /api/agents/memories/{id}/verify/       # Mark memory as verified
POST /api/agents/memories/{id}/deactivate/   # Deactivate memory
POST /api/agents/memories/{id}/reactivate/   # Reactivate memory
```

### Creating a Memory

```json
{
  "content": "User prefers dark theme for UI",
  "memory_type": "preference",
  "importance_score": 0.8,
  "personalness_score": 0.9,
  "actionability_score": 0.7,
  "metadata": {
    "context": "UI discussion",
    "tags": ["theme", "preference"]
  }
}
```

### Memory Search

Search memories with various filters:

```json
{
  "q": "dark theme",
  "type": "preference",
  "active": "true"
}
```

### Memory Statistics

Get comprehensive memory statistics:

```json
{
  "total_memories": 25,
  "active_memories": 23,
  "verified_memories": 15,
  "memory_types": {
    "preference": 8,
    "skill": 5,
    "personal_fact": 4,
    "goal": 3,
    "interest": 3,
    "relationship": 2
  },
  "average_salience_score": 0.72,
  "recent_memories_count": 5
}
```

### Memory Clusters

Organize memories into clusters for better management:

```json
{
  "id": "uuid",
  "user": "user-uuid",
  "name": "Programming Preferences",
  "description": "User's programming-related preferences and skills",
  "cluster_type": "topic",
  "memory_count": 5,
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T11:45:00Z"
}
```

### Memory Cluster Endpoints

```
GET /api/agents/memory-clusters/             # List all user clusters
POST /api/agents/memory-clusters/            # Create a new cluster
GET /api/agents/memory-clusters/{id}/        # Get specific cluster details
PUT /api/agents/memory-clusters/{id}/        # Update cluster
DELETE /api/agents/memory-clusters/{id}/     # Delete cluster
POST /api/agents/memory-clusters/{id}/add-memory/    # Add memory to cluster
POST /api/agents/memory-clusters/{id}/remove-memory/ # Remove memory from cluster
GET /api/agents/memory-clusters/{id}/memories/       # Get memories in cluster
```

### Memory Configuration

Configure memory system behavior:

```json
{
  "min_importance_threshold": 0.3,
  "min_personalness_threshold": 0.2,
  "min_actionability_threshold": 0.2,
  "max_memories_per_retrieval": 5,
  "similarity_threshold": 0.7,
  "allow_memory_storage": true,
  "allow_cross_conversation_memory": true,
  "auto_cleanup_enabled": true,
  "memory_retention_days": 365
}
```

### Memory Configuration Endpoints

```
GET /api/agents/memory/configuration/        # Get memory configuration
PUT /api/agents/memory/configuration/        # Update memory configuration
```

### Bulk Memory Operations

Perform bulk operations on memories:

```json
{
  "memories": [
    {
      "content": "User prefers dark theme",
      "memory_type": "preference",
      "importance_score": 0.8,
      "personalness_score": 0.9,
      "actionability_score": 0.7
    },
    {
      "content": "User knows Python programming",
      "memory_type": "skill",
      "importance_score": 0.9,
      "personalness_score": 0.8,
      "actionability_score": 0.9
    }
  ]
}
```

### Bulk Memory Endpoints

```
POST /api/agents/memory/bulk/               # Bulk create memories
DELETE /api/agents/memory/bulk/              # Bulk delete memories
```

### Memory Verification and Status

Manage memory verification and activation status:

```json
{
  "type": "memory_verification",
  "memory_id": "uuid",
  "is_verified": true,
  "verification_date": "2024-01-15T11:45:00Z"
}
```

```json
{
  "type": "memory_status_change",
  "memory_id": "uuid",
  "is_active": false,
  "deactivation_reason": "outdated_information"
}
```

### Agent Response Streaming

The agent system supports real-time streaming responses:

```json
{
  "type": "agent_response_chunk",
  "content": "To implement authentication in Flutter...",
  "domain": "dev",
  "confidence": 0.95,
  "is_final": false,
  "chunk_id": "uuid-here"
}
```

```json
{
  "type": "agent_response_chunk",
  "content": " you'll need to set up JWT tokens.",
  "domain": "dev",
  "confidence": 0.95,
  "is_final": true,
  "chunk_id": "uuid-here",
  "suggested_actions": [
    "create_task",
    "show_code_example"
  ]
}
```

## 🎭 Unity Integration

### Unity Integration Endpoints

```
POST /api/agents/avatar/outfit/          # Change avatar outfit
POST /api/agents/avatar/environment/     # Change environment
POST /api/agents/avatar/animation/       # Play an animation
GET /api/agents/avatar/settings/         # Get avatar settings
POST /api/agents/avatar/voice-command/   # Process voice command
```

### Avatar Customization

The backend supports changing avatar appearance:

```json
{
  "outfit_id": "casual_outfit",
  "accessories": ["glasses", "hat"]
}
```

### Environment Management

Change the 3D environment:

```json
{
  "environment_id": "beach",
  "time_of_day": "sunset"
}
```

### Animation Control

Trigger animations:

```json
{
  "animation_id": "wave",
  "intensity": 0.8,
  "duration": 2.5
}
```

## 📁 File Upload System

### File Upload Endpoints

```
POST /api/upload/avatar/                # Upload user avatar
POST /api/upload/audio/                 # Upload audio file
GET /api/media/{file_id}/               # Get uploaded file
```

### Avatar Upload

```dart
// Example Flutter code for avatar upload
final request = http.MultipartRequest(
  'POST',
  Uri.parse('https://api.example.com/api/upload/avatar/'),
);

request.files.add(await http.MultipartFile.fromPath(
  'avatar',
  imagePath,
));

request.headers['Authorization'] = 'Bearer $token';

final response = await request.send();
```

### Audio Upload

```dart
// Example Flutter code for audio upload
final request = http.MultipartRequest(
  'POST',
  Uri.parse('https://api.example.com/api/upload/audio/'),
);

request.files.add(await http.MultipartFile.fromPath(
  'audio',
  audioPath,
));

request.headers['Authorization'] = 'Bearer $token';

final response = await request.send();
```

## 🔄 Data Synchronization

### Initial Data Load

When the app starts, fetch the following data:

1. User profile and settings
2. User progress and achievements
3. Conversations list
4. Shop items
5. User inventory

### Real-time Updates

Use WebSocket for real-time updates:

1. New messages
2. Typing indicators
3. XP and level changes
4. Achievement unlocks

### Offline Support

The backend supports reconnection with session restoration:

```json
{
  "type": "reconnection_request",
  "previous_session_id": "uuid-here",
  "previous_connection_id": "uuid-here"
}
```

## 🔒 Security Considerations

### Token Security

- Store JWT tokens securely using Flutter's secure storage
- Implement token refresh mechanism
- Clear tokens on logout

### Input Validation

- Validate all user inputs before sending to the backend
- Handle error responses appropriately

### Error Handling

The backend returns standardized error responses:

```json
{
  "error": {
    "code": "validation_error",
    "message": "Invalid input data",
    "details": {
      "field": "error description"
    }
  }
}
```

## 📊 Performance Considerations

### Caching

- Cache frequently accessed data (user profile, conversations)
- Implement efficient image caching
- Use pagination for large data sets

### WebSocket Optimization

- Implement reconnection with exponential backoff
- Handle connection state changes gracefully
- Monitor connection health with heartbeats

```json
{
  "type": "connection_heartbeat"
}
```

## 🧪 Testing

### API Testing

Test all API endpoints with different scenarios:

1. Valid inputs
2. Invalid inputs
3. Edge cases
4. Authentication failures

### WebSocket Testing

Test WebSocket functionality:

1. Connection establishment
2. Message sending and receiving
3. Streaming responses
4. Connection loss and reconnection

## 📱 Integration Checklist

- [ ] Set up API client with JWT authentication
- [ ] Implement WebSocket connection with authentication
- [ ] Create models matching backend data structures
- [ ] Implement user authentication flow
- [ ] Set up real-time chat with streaming
- [ ] Integrate gamification features
- [ ] Implement shop and inventory system
- [ ] Connect Unity integration endpoints
- [ ] Set up file upload functionality
- [ ] Implement error handling and retry logic
- [ ] Implement WebSocket TTS streaming handler
- [ ] Support chunk-based audio playback
- [ ] Handle emotion-based voice modulation
- [ ] Manage voice configuration dynamically
- [ ] Implement error recovery strategies
- [ ] Track and log TTS performance metrics
- [ ] **Agent System Integration**
  - [ ] Implement agent domain routing
  - [ ] Create agent query processing
  - [ ] Handle multi-agent responses
  - [ ] Support emotion-aware agent responses
  - [ ] Implement agent switching functionality
- [ ] **Task Management Integration**
  - [ ] Implement task model in Flutter
  - [ ] Create task creation UI
  - [ ] Implement task status update functionality
  - [ ] Add progress tracking UI
  - [ ] Create phase management interface
  - [ ] Handle task error and intervention scenarios
  - [ ] Implement task list and detail views
  - [ ] Add task filtering and sorting
  - [ ] Support task orchestration integration
  - [ ] Implement automatic task creation from queries
- [ ] **Memory Management Integration**
  - [ ] Implement memory model in Flutter
  - [ ] Create memory creation and editing UI
  - [ ] Implement memory search and filtering
  - [ ] Add memory statistics dashboard
  - [ ] Create memory cluster management
  - [ ] Implement memory configuration settings
  - [ ] Add bulk memory operations
  - [ ] Support memory verification workflow
  - [ ] Create memory type selection interface
  - [ ] Implement memory salience score display
- [ ] Test all agent features
- [ ] Test all task tracking features
- [ ] Test all features with the backend

## 🚀 Next Steps

1. **Set up API client**: Create a Flutter service for API communication
2. **Implement authentication**: Connect login/registration to backend
3. **Set up WebSocket**: Implement real-time chat functionality
4. **Integrate gamification**: Connect XP, levels, and achievements
5. **Implement shop**: Connect virtual shop and inventory
6. **Connect Unity**: Integrate avatar customization and animations

## 📞 Support

For any questions or issues during integration, please contact the backend team.

## 🎙️ Hume TTS Streaming

### TTS Streaming Protocol

The backend supports advanced Text-to-Speech (TTS) streaming with emotion-aware voice modulation using Hume AI. This allows for dynamic, expressive audio generation.

#### TTS Streaming Message Types

##### Audio Chunk Streaming
```json
{
  "type": "audio_chunk",
  "data": "base64-encoded-audio-data",
  "chunk_id": "unique-chunk-identifier",
  "is_final": false,
  "voice_settings": {
    "voice_name": "nova",
    "emotion_context": {
      "primary_emotion": "happy",
      "intensity": 0.7
    },
    "speed": 1.0,
    "acting_instructions": "Speak with gentle excitement"
  },
  "timestamp": 1623456789000,
  "request_id": "optional-request-tracking-id"
}
```

##### Voice Configuration Options
- `voice_name`: Select from available voices (e.g., 'nova', 'shimmer', 'onyx')
- `emotion_context`: Modulate voice based on detected emotions
  - `primary_emotion`: Emotion to influence voice tone
  - `intensity`: Strength of emotional modulation
- `speed`: Speech rate (0.25 to 4.0)
- `acting_instructions`: Custom voice styling guidance

#### Emotion-Aware TTS Example

```dart
// Flutter WebSocket TTS Streaming Example
void streamTTSResponse(String text, EmotionContext emotionContext) {
  final socket = WebSocketChannel.connect(Uri.parse('ws://api.example.com/ws/tts'));
  
  socket.sink.add(jsonEncode({
    'type': 'tts_request',
    'text': text,
    'voice_settings': {
      'voice_name': 'nova',
      'emotion_context': {
        'primary_emotion': emotionContext.primaryEmotion,
        'intensity': emotionContext.emotionIntensity
      },
      'speed': 1.0
    }
  }));

  socket.stream.listen((chunk) {
    final audioChunk = AudioChunk.fromJson(jsonDecode(chunk));
    
    // Process audio chunk
    if (!audioChunk.isFinal) {
      playAudioChunk(audioChunk.audioData);
    } else {
      // Final chunk - complete audio playback
      finalizeAudioPlayback();
    }
  });
}
```

#### Performance and Error Handling

- Streaming supports partial audio generation
- Handles network interruptions
- Provides fallback mechanisms
- Tracks performance metrics (first chunk time, total generation time)

#### Integration Considerations

1. Implement chunk-based audio playback
2. Handle streaming interruptions
3. Manage voice settings dynamically
4. Use emotion context for voice modulation

### Voice Settings API

```json
{
  "available_voices": [
    {
      "name": "nova",
      "description": "Warm, friendly tone",
      "gender": "neutral",
      "languages": ["en"],
      "emotion_range": ["happy", "calm", "excited"]
    },
    {
      "name": "shimmer",
      "description": "Energetic, youthful voice",
      "gender": "female",
      "languages": ["en"],
      "emotion_range": ["playful", "enthusiastic"]
    }
  ]
}
```

### Error Handling

```json
{
  "type": "tts_error",
  "error_code": "STREAM_INTERRUPTED",
  "message": "TTS streaming was interrupted",
  "recovery_suggestion": "Retry the request or use text-only fallback"
}
```


Websocket Implementation Reference:

import json
import logging
import base64
import uuid
import time
import random
from typing import Dict, Any, Optional, List
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import AnonymousUser
from django.utils import timezone
from .models import ChatSession, Conversation, Message, StreamingSession, EmotionContext
from .services import (
    ChatService, 
    AudioProcessingService, 
    AudioChunk,
    get_audio_service
)
from .services.performance_monitor import performance_monitor
from .services.cache_service import cache_service_instance
from .services.error_recovery import get_fallback_response
import asyncio

# Import agent services
try:
    from agents.services.agent_coordinator import get_agent_coordinator
    from agents.services.memory_manager import get_memory_manager
    from agents.services.langgraph_orchestrator import LangGraphOrchestrator
    AGENTS_AVAILABLE = True
except ImportError:
    AGENTS_AVAILABLE = False
    logging.warning("Agents app not available. Using fallback response generation.")

logger = logging.getLogger(__name__)


class ChatConsumer(AsyncWebsocketConsumer):
    """Enhanced WebSocket consumer for real-time AI companion with streaming support."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user = None
        self.chat_session = None
        self.conversation = None
        self.chat_service = None
        
        # Real-time streaming components
        self.streaming_session = None
        self.audio_service = None
        self.session_id = None
        self.is_processing_audio = False
        self.current_audio_chunks = []
        self.reconnection_attempts = 0
        self.max_reconnection_attempts = 5
        
        # Connection management
        self.connection_state = 'disconnected'
        self.last_heartbeat = None
        self.heartbeat_interval = 30  # seconds
        self.heartbeat_task = None
        self.message_queue = []
        self.max_queue_size = 100
        self.session_state = {}
        self.connection_id = None
    
    async def connect(self):
        """Handle WebSocket connection with streaming session setup."""
        # Get user from scope (set by AuthMiddleware)
        self.user = self.scope.get("user")
        
        if not self.user or isinstance(self.user, AnonymousUser):
            logger.warning("Anonymous or missing user attempted to connect to chat WebSocket")
            await self.close()
            return
        
        # Accept the connection
        await self.accept()
        
        # Generate session and connection IDs
        self.session_id = str(uuid.uuid4())
        self.connection_id = str(uuid.uuid4())
        
        # Set connection state
        self.connection_state = 'connecting'
        
        # Create chat session and streaming session
        self.chat_session = await self.create_chat_session()
        self.streaming_session = await self.create_streaming_session()
        
        # Initialize services
        self.chat_service = ChatService(user=self.user)
        self.audio_service = get_audio_service()
        
        # Start heartbeat monitoring
        self.heartbeat_task = asyncio.create_task(self.heartbeat_monitor())
        
        # Set connection state to active
        self.connection_state = 'active'
        self.last_heartbeat = time.time()
        
        logger.info(f"Enhanced chat WebSocket connected for user: {self.user.email}")
        
        # Check agents availability
        agents_available = await self.check_agents_availability()
        
        # Send welcome message with streaming capabilities
        await self.send(text_data=json.dumps({
            'type': 'connection_established',
            'message': 'Connected to real-time AI companion',
            'session_id': self.session_id,
            'connection_id': self.connection_id,
            'chat_session_id': str(self.chat_session.id),
            'streaming_enabled': True,
            'heartbeat_interval': self.heartbeat_interval,
            'agents_available': agents_available,
            'supported_message_types': [
                'text_message',
                'audio_chunk', 
                'emotion_feedback',
                'typing_start',
                'typing_stop',
                'conversation_switch',
                'connection_heartbeat',
                'reconnection_request'
            ]
        }))
        
        # Process any queued messages from previous connection
        await self.process_queued_messages()
    
    async def disconnect(self, close_code):
        """Handle WebSocket disconnection with cleanup."""
        try:
            # Set connection state
            self.connection_state = 'disconnecting'
            
            # Cancel heartbeat monitoring
            if self.heartbeat_task:
                self.heartbeat_task.cancel()
                try:
                    await self.heartbeat_task
                except asyncio.CancelledError:
                    pass
            
            # Save session state for potential reconnection
            await self.save_session_state()
            
            # Clean up streaming session
            if self.streaming_session:
                await self.disconnect_streaming_session()
            
            # Clean up chat session
            if self.chat_session:
                await self.disconnect_chat_session()
            
            # Remove any active typing indicators
            if self.conversation:
                await self.remove_typing_indicators(self.conversation, 'user')
            
            # Cancel any ongoing audio processing
            self.is_processing_audio = False
            
            # Set final connection state
            self.connection_state = 'disconnected'
            
            logger.info(f"Enhanced chat WebSocket disconnected for user: {self.user.email if self.user else 'Unknown'} (code: {close_code})")
            
        except Exception as e:
            logger.error(f"Error during WebSocket disconnect cleanup: {e}")
    
    async def receive(self, text_data):
        """Handle incoming WebSocket messages with enhanced streaming support."""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            # Update streaming session activity
            if self.streaming_session:
                await self.update_streaming_session_activity()
            
            # Route messages to appropriate handlers with validation
            if message_type == 'text_message':
                if await self.handle_message_with_validation(data, message_type):
                    await self.handle_text_message(data)
            elif message_type == 'audio_chunk':
                if await self.handle_message_with_validation(data, message_type):
                    await self.handle_audio_chunk(data)
            elif message_type == 'emotion_feedback':
                if await self.handle_message_with_validation(data, message_type):
                    await self.handle_emotion_feedback(data)
            elif message_type == 'typing_start':
                await self.handle_typing_start()
            elif message_type == 'typing_stop':
                await self.handle_typing_stop()
            elif message_type == 'conversation_switch':
                await self.handle_conversation_switch(data)
            elif message_type == 'connection_heartbeat':
                await self.handle_heartbeat(data)
            elif message_type == 'reconnection_request':
                await self.handle_reconnection_request(data)
            # Legacy support
            elif message_type == 'chat_message':
                await self.handle_text_message(data)
            elif message_type == 'voice_message':
                await self.handle_legacy_voice_message(data)
            else:
                logger.warning(f"Unknown message type: {message_type}")
                await self.send_error(f"Unknown message type: {message_type}")
        
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON received in WebSocket: {e}")
            await self.send_error("Invalid JSON format")
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {e}")
            await self.send_error("Internal server error")
    
    async def handle_text_message(self, data):
        """Handle text messages with enhanced processing."""
        content = data.get('content', '').strip()
        conversation_id = data.get('conversation_id')
        timestamp = data.get('timestamp')
        
        if not content:
            await self.send_error("Message content cannot be empty")
            return
        
        # Generate a unique request ID for tracking performance
        request_id = str(uuid.uuid4())
        
        # Start timing the total response
        performance_monitor.start_timer(request_id, 'total_response_time')
        
        try:
            # Get or create conversation
            conversation = await self.get_or_create_conversation(conversation_id)
            
            # Check if we have a cached response for this query
            cached_response = None
            if self.user:
                cached_response = cache_service_instance.get_cached_query_response(
                    self.user.id, content
                )
            
            # Save user message
            user_message = await self.save_message(
                conversation=conversation,
                sender_type='user',
                content=content,
                message_type='text'
            )
            
            # Send acknowledgment
            await self.send(text_data=json.dumps({
                'type': 'message_received',
                'message_id': str(user_message.id),
                'timestamp': user_message.created_at.isoformat(),
                'processing_started': True,
                'request_id': request_id
            }))
            
            # Generate AI response with streaming
            if cached_response:
                logger.info(f"Using cached response for user {self.user.id}, request {request_id}")
                # Send cached response with minimal delay to simulate streaming
                await self.send_cached_response(cached_response, user_message)
                
                # Record performance metrics for cached response
                response_time = performance_monitor.end_timer(request_id, 'total_response_time')
                if response_time and self.streaming_session and self.user:
                    performance_monitor.record_metric(
                        request_id, self.streaming_session, self.user, 
                        'total_response_time', response_time
                    )
                    performance_monitor.record_metric(
                        request_id, self.streaming_session, self.user,
                        'cache_hit', 1.0
                    )
            else:
                # Start memory retrieval timer
                performance_monitor.start_timer(request_id, 'memory_retrieval_time')
                
                # Send typing indicator before generating response
                await self.send(text_data=json.dumps({
                    'type': 'ai_typing',
                    'status': 'started',
                    'conversation_id': str(conversation.id),
                    'timestamp': timezone.now().isoformat()
                }))
                
                # Save typing indicator message
                await self.save_message(
                    conversation=conversation,
                    sender_type='assistant',
                    content='',
                    message_type='typing'
                )
                
                # Generate streaming response
                await self.generate_streaming_ai_response(conversation, content, request_id)
            
        except Exception as e:
            logger.error(f"Error handling text message: {e}")
            await self.send_error("Failed to process message")
            
            # Record error
            if hasattr(self, 'streaming_session') and self.streaming_session and self.user:
                performance_monitor.record_error(
                    request_id, 'text_processing_error', str(e)
                )
    
    async def handle_audio_chunk(self, data):
        """Handle real-time audio chunks for streaming transcription and emotion detection."""
        # Generate a unique request ID for tracking performance
        request_id = str(uuid.uuid4())
        
        # Start timing the audio processing
        performance_monitor.start_timer(request_id, 'audio_processing_time')
        
        try:
            # Extract audio chunk data
            audio_data_b64 = data.get('data')
            chunk_id = data.get('chunk_id', str(uuid.uuid4()))
            is_final = data.get('is_final', False)
            timestamp = data.get('timestamp', time.time() * 1000)
            
            if not audio_data_b64:
                await self.send_error("Audio data is required")
                return
            
            # Decode base64 audio data
            try:
                audio_data = base64.b64decode(audio_data_b64)
            except Exception as e:
                logger.error(f"Failed to decode audio data: {e}")
                await self.send_error("Invalid audio data format")
                return
            
            # Create audio chunk
            audio_chunk = AudioChunk(
                data=audio_data,
                chunk_id=chunk_id,
                timestamp_ms=timestamp,
                sample_rate=48000,  # Default, could be specified in message
                channels=1,
                is_final=is_final,
                user_id=str(self.user.id)
            )
            
            # Process audio chunk with streaming results
            self.is_processing_audio = True
            
            # Start timing for transcription and emotion detection
            performance_monitor.start_timer(request_id, 'transcription_time')
            performance_monitor.start_timer(request_id, 'emotion_detection_time')
            
            async for result in self.audio_service.process_audio_chunk(
                audio_chunk,
                enable_transcription=True,
                enable_emotion_detection=True,
                parallel_processing=True,
                enable_preprocessing=True
            ):
                # Send transcription results
                if result.transcription:
                    # End transcription timer if this is the first result
                    if not result.transcription.is_partial or is_final:
                        transcription_time = performance_monitor.end_timer(request_id, 'transcription_time')
                        if transcription_time and self.streaming_session and self.user:
                            performance_monitor.record_metric(
                                request_id, self.streaming_session, self.user,
                                'transcription_time', transcription_time
                            )
                    
                    await self.send(text_data=json.dumps({
                        'type': 'transcription_partial',
                        'text': result.transcription.text,
                        'confidence': result.transcription.confidence,
                        'chunk_id': chunk_id,
                        'is_partial': result.transcription.is_partial,
                        'processing_time_ms': result.transcription.processing_time_ms,
                        'timestamp': timestamp,
                        'request_id': request_id
                    }))
                
                # Send emotion detection results
                if result.emotion_analysis:
                    # End emotion detection timer
                    emotion_time = performance_monitor.end_timer(request_id, 'emotion_detection_time')
                    if emotion_time and self.streaming_session and self.user:
                        performance_monitor.record_metric(
                            request_id, self.streaming_session, self.user,
                            'emotion_detection_time', emotion_time
                        )
                    
                    await self.send(text_data=json.dumps({
                        'type': 'emotion_detected',
                        'emotions': [
                            {
                                'name': emotion.name,
                                'score': emotion.score
                            }
                            for emotion in result.emotion_analysis.emotions
                        ],
                        'confidence_score': result.emotion_analysis.confidence_score,
                        'chunk_id': chunk_id,
                        'timestamp': timestamp,
                        'request_id': request_id
                    }))
                    
                    # Store emotion context
                    await self.store_emotion_context(result.emotion_analysis, chunk_id)
                    
                    # Cache emotion context for quick access
                    if self.user:
                        emotion_data = {
                            'primary_emotion': result.emotion_analysis.primary_emotion,
                            'emotions': [
                                {'name': e.name, 'score': e.score}
                                for e in result.emotion_analysis.emotions
                            ],
                            'confidence_score': result.emotion_analysis.confidence_score
                        }
                        cache_service_instance.cache_emotion_context(
                            self.user.id, self.session_id, emotion_data
                        )
                
                # If this is the final chunk and we have complete transcription
                if is_final and result.transcription and not result.transcription.is_partial:
                    # End audio processing timer
                    audio_time = performance_monitor.end_timer(request_id, 'audio_processing_time')
                    if audio_time and self.streaming_session and self.user:
                        performance_monitor.record_metric(
                            request_id, self.streaming_session, self.user,
                            'audio_processing_time', audio_time
                        )
                    
                    # Generate AI response based on transcription
                    conversation = await self.get_or_create_conversation(
                        data.get('conversation_id')
                    )
                    
                    # Save transcribed message
                    user_message = await self.save_message(
                        conversation=conversation,
                        sender_type='user',
                        content=result.transcription.text,
                        message_type='audio'
                    )
                    
                    # Start total response timer for voice interaction
                    performance_monitor.start_timer(request_id, 'total_response_time')
                    
                    # Generate streaming AI response
                    await self.generate_streaming_ai_response(
                        conversation, 
                        result.transcription.text,
                        emotion_context=result.emotion_analysis,
                        request_id=request_id
                    )
            
            self.is_processing_audio = False
            
        except Exception as e:
            logger.error(f"Error handling audio chunk: {e}")
            await self.send_error("Failed to process audio chunk")
            self.is_processing_audio = False
            
            # Record error
            if hasattr(self, 'streaming_session') and self.streaming_session and self.user:
                performance_monitor.record_error(
                    request_id, 'audio_processing_error', str(e)
                )
    
    async def handle_emotion_feedback(self, data):
        """Handle user feedback on emotion detection accuracy."""
        try:
            emotion_scores = data.get('emotion_scores', {})
            user_validation = data.get('user_validation', True)
            chunk_id = data.get('chunk_id')
            
            # Store emotion feedback for model improvement
            await self.store_emotion_feedback(emotion_scores, user_validation, chunk_id)
            
            # Send acknowledgment
            await self.send(text_data=json.dumps({
                'type': 'emotion_feedback_received',
                'chunk_id': chunk_id,
                'timestamp': time.time() * 1000
            }))
            
        except Exception as e:
            logger.error(f"Error handling emotion feedback: {e}")
            await self.send_error("Failed to process emotion feedback")
    
    async def send_cached_response(self, cached_response, user_message):
        """Send a cached response with simulated streaming."""
        try:
            # Send typing indicator
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'started'
            }))
            
            # Simulate streaming by sending the response in chunks
            chunk_id = str(uuid.uuid4())
            words = cached_response.split()
            chunks = []
            
            # Send in small chunks to simulate streaming
            current_chunk = []
            for i, word in enumerate(words):
                current_chunk.append(word)
                
                # Send every few words or at the end
                if len(current_chunk) >= 3 or i == len(words) - 1:
                    chunk_text = ' '.join(current_chunk)
                    chunks.append(chunk_text)
                    
                    await self.send(text_data=json.dumps({
                        'type': 'llm_response_chunk',
                        'content': chunk_text,
                        'chunk_id': chunk_id,
                        'is_final': i == len(words) - 1,
                        'timestamp': time.time() * 1000,
                        'is_cached': True
                    }))
                    
                    current_chunk = []
                    await asyncio.sleep(0.05)  # Small delay between chunks
            
            # Save AI message
            ai_message = await self.save_message(
                conversation=user_message.conversation,
                sender_type='assistant',
                content=cached_response,
                message_type='text',
                model_used='cached',
                tokens_used=0,
                processing_time=0
            )
            
            # Stop typing indicator
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'stopped'
            }))
            
        except Exception as e:
            logger.error(f"Error sending cached response: {e}")
            await self.send_error("Failed to send cached response")
            
            # Stop typing indicator on error
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'stopped'
            }))
    
    async def handle_heartbeat(self, data):
        """Handle connection heartbeat for monitoring."""
        self.last_heartbeat = time.time()
        
        await self.send(text_data=json.dumps({
            'type': 'heartbeat_response',
            'timestamp': time.time() * 1000,
            'session_id': self.session_id,
            'connection_id': self.connection_id,
            'connection_state': self.connection_state
        }))
    
    async def handle_reconnection_request(self, data):
        """Handle client reconnection requests with exponential backoff."""
        try:
            previous_session_id = data.get('previous_session_id')
            previous_connection_id = data.get('previous_connection_id')
            
            # Check if this is a valid reconnection
            if previous_session_id and previous_session_id != self.session_id:
                # Try to restore session state
                restored = await self.restore_session_state(previous_session_id)
                if restored:
                    logger.info(f"Restored session state for {self.user.email}")
                else:
                    logger.warning(f"Could not restore session state for {previous_session_id}")
            
            self.reconnection_attempts += 1
            
            if self.reconnection_attempts > self.max_reconnection_attempts:
                await self.send_error("Maximum reconnection attempts exceeded")
                await self.close()
                return
            
            # Calculate backoff delay
            backoff_delay = min(2 ** self.reconnection_attempts, 30)  # Max 30 seconds
            
            await self.send(text_data=json.dumps({
                'type': 'reconnection_accepted',
                'session_restored': True if previous_session_id == self.session_id else False,
                'backoff_delay': backoff_delay,
                'backoff_delay_seconds': backoff_delay,
                'attempt': self.reconnection_attempts,
                'max_attempts': self.max_reconnection_attempts,
                'session_id': self.session_id,
                'connection_id': self.connection_id
            }))
        except Exception as e:
            logger.error(f"Error handling reconnection request: {e}")

    async def handle_legacy_voice_message(self, data):
        """Handle legacy voice messages (for backward compatibility)."""
        audio_data = data.get('audio_data')  # Base64 encoded audio
        conversation_id = data.get('conversation_id')
        
        if not audio_data:
            await self.send_error("Audio data is required")
            return
        
        try:
            # Get or create conversation
            conversation = await self.get_or_create_conversation(conversation_id)
            
            # Process audio with speech-to-text
            transcription = await self.chat_service.transcribe_audio(audio_data)
            
            if not transcription:
                await self.send_error("Could not transcribe audio")
                return
            
            # Save user message with transcription
            user_message = await self.save_message(
                conversation=conversation,
                sender_type='user',
                content=transcription,
                message_type='audio'
            )
            
            # Send transcription back to user
            await self.send(text_data=json.dumps({
                'type': 'transcription_complete',
                'message_id': str(user_message.id),
                'transcription': transcription,
                'timestamp': user_message.created_at.isoformat()
            }))
            
            # Generate AI response
            await self.generate_ai_response(conversation, transcription)
            
        except Exception as e:
            logger.error(f"Error handling voice message: {e}")
            await self.send_error("Failed to process voice message")
    
    async def handle_voice_message(self, data):
        """Handle voice messages (audio data)."""
        audio_data = data.get('audio_data')  # Base64 encoded audio
        conversation_id = data.get('conversation_id')
        
        if not audio_data:
            await self.send_error("Audio data is required")
            return
        
        try:
            # Get or create conversation
            conversation = await self.get_or_create_conversation(conversation_id)
            
            # Process audio with speech-to-text
            transcription = await self.chat_service.transcribe_audio(audio_data)
            
            if not transcription:
                await self.send_error("Could not transcribe audio")
                return
            
            # Save user message with transcription
            user_message = await self.save_message(
                conversation=conversation,
                sender_type='user',
                content=transcription,
                message_type='audio'
            )
            
            # Send transcription back to user
            await self.send(text_data=json.dumps({
                'type': 'transcription_complete',
                'message_id': str(user_message.id),
                'transcription': transcription,
                'timestamp': user_message.created_at.isoformat()
            }))
            
            # Generate AI response
            await self.generate_ai_response(conversation, transcription)
            
        except Exception as e:
            logger.error(f"Error handling voice message: {e}")
            await self.send_error("Failed to process voice message")
    
    async def handle_typing_start(self):
        """Handle typing indicator start."""
        try:
            # Get current conversation
            conversation = self.conversation
            if not conversation:
                # If no active conversation, try to get the most recent one
                conversation = await self.get_most_recent_conversation()
                if not conversation:
                    await self.send_error("No active conversation for typing indicator")
                    return
            
            # Save typing indicator to database
            typing_message = await self.save_message(
                conversation=conversation,
                sender_type='user',
                content='',
                message_type='typing'
            )
            
            # Broadcast typing indicator to all connected clients for this conversation
            await self.send(text_data=json.dumps({
                'type': 'typing_acknowledged',
                'status': 'started',
                'conversation_id': str(conversation.id),
                'user_id': str(self.user.id),
                'timestamp': timezone.now().isoformat()
            }))
            
            # Update relationship metrics for active engagement
            await self.update_relationship_metrics(conversation, 'typing_engagement')
            
        except Exception as e:
            logger.error(f"Error handling typing start: {e}")
            await self.send_error("Failed to process typing indicator")
    
    async def handle_typing_stop(self):
        """Handle typing indicator stop."""
        try:
            # Get current conversation
            conversation = self.conversation
            if not conversation:
                # If no active conversation, try to get the most recent one
                conversation = await self.get_most_recent_conversation()
                if not conversation:
                    return
            
            # Remove typing indicator from database (optional)
            await self.remove_typing_indicators(conversation, 'user')
            
            # Broadcast typing stopped to all connected clients for this conversation
            await self.send(text_data=json.dumps({
                'type': 'typing_acknowledged',
                'status': 'stopped',
                'conversation_id': str(conversation.id),
                'user_id': str(self.user.id),
                'timestamp': timezone.now().isoformat()
            }))
            
        except Exception as e:
            logger.error(f"Error handling typing stop: {e}")
            # Don't send error for typing stop failures to avoid disrupting the UI
    
    async def handle_conversation_switch(self, data):
        """Handle switching to a different conversation."""
        conversation_id = data.get('conversation_id')
        
        try:
            if conversation_id:
                conversation = await self.get_conversation(conversation_id)
                if conversation:
                    self.conversation = conversation
                    await self.update_chat_session_conversation(conversation)
                    
                    await self.send(text_data=json.dumps({
                        'type': 'conversation_switched',
                        'conversation_id': str(conversation.id),
                        'conversation_title': conversation.title
                    }))
                else:
                    await self.send_error("Conversation not found")
            else:
                # Create new conversation
                self.conversation = None
                await self.send(text_data=json.dumps({
                    'type': 'new_conversation_ready'
                }))
        
        except Exception as e:
            logger.error(f"Error switching conversation: {e}")
            await self.send_error("Failed to switch conversation")
    
    async def generate_streaming_ai_response(self, conversation, user_input, emotion_context=None, request_id=None):
        """Generate and stream AI response with real-time delivery."""
        if not request_id:
            request_id = str(uuid.uuid4())
        
        try:
            # Get conversation history for context
            conversation_history = await self.get_conversation_history(conversation, limit=10)
            
            # Get emotion context if not provided
            if not emotion_context and self.user:
                emotion_context = cache_service_instance.get_emotion_context(
                    self.user.id, self.session_id
                )
            
            # Start memory retrieval timer
            performance_monitor.start_timer(request_id, 'memory_retrieval_time')
            
            # Get memory context (if available)
            memory_context = await self.get_memory_context(user_input)
            
            # End memory retrieval timer
            memory_retrieval_time = performance_monitor.end_timer(request_id, 'memory_retrieval_time')
            if memory_retrieval_time and self.streaming_session and self.user:
                performance_monitor.record_metric(
                    request_id, self.streaming_session, self.user,
                    'memory_retrieval_time', memory_retrieval_time
                )
            
            # Start LLM processing timer
            performance_monitor.start_timer(request_id, 'llm_processing_time')
            
            # Check if agents app is available
            if not AGENTS_AVAILABLE:
                # Use fallback response generation
                logger.warning("Agents app not available. Using fallback response generation.")
                return await self.generate_fallback_response(conversation, user_input, request_id)
            
            # Get agent coordinator instance
            agent_coordinator = get_agent_coordinator()
            
            # Format conversation history for agent
            formatted_history = []
            for msg in conversation_history:
                formatted_history.append({
                    'role': 'user' if msg['sender_type'] == 'user' else 'assistant',
                    'content': msg['content']
                })
            
            # Initialize response content
            full_response_content = ""
            
            # Process query through agent coordinator with streaming
            async for response_chunk in agent_coordinator.process_user_query(
                user_input=user_input,
                user_id=str(self.user.id),
                conversation_history=formatted_history,
                emotion_context=emotion_context,
                memory_context=memory_context,
                streaming=True
            ):
                # Handle different response types
                if response_chunk.get('type') == 'domain_classification':
                    # Domain classification result
                    domain = response_chunk.get('domain', 'general')
                    confidence = response_chunk.get('confidence', 0.0)
                    
                    # Log domain classification
                    logger.info(f"Query classified as domain: {domain} (confidence: {confidence:.2f})")
                    
                elif response_chunk.get('type') == 'response_chunk':
                    # Content chunk from streaming response
                    content = response_chunk.get('content', '')
                    if content:
                        full_response_content += content
                        
                        # Send chunk to client
                        await self.send(text_data=json.dumps({
                            'type': 'llm_response_chunk',
                            'content': content,
                            'chunk_id': request_id,
                            'is_final': False,
                            'timestamp': timezone.now().isoformat()
                        }))
                
                elif response_chunk.get('type') == 'response_complete':
                    # Complete response
                    if response_chunk.get('full_content'):
                        full_response_content = response_chunk.get('full_content')
                
                elif response_chunk.get('type') == 'error':
                    # Error in processing
                    error_msg = response_chunk.get('error', 'Unknown error in AI processing')
                    logger.error(f"Error in AI response generation: {error_msg}")
                    
                    # Send error to client
                    await self.send_error(f"AI processing error: {error_msg}")
                    
                    # End LLM processing timer
                    llm_time = performance_monitor.end_timer(request_id, 'llm_processing_time')
                    if llm_time and self.streaming_session and self.user:
                        performance_monitor.record_metric(
                            request_id, self.streaming_session, self.user,
                            'llm_processing_time', llm_time
                        )
                    
                    # Record error
                    if self.streaming_session and self.user:
                        performance_monitor.record_error(
                            request_id, 'ai_processing_error', error_msg
                        )
                    
                    return
            
            # End LLM processing timer
            llm_time = performance_monitor.end_timer(request_id, 'llm_processing_time')
            if llm_time and self.streaming_session and self.user:
                performance_monitor.record_metric(
                    request_id, self.streaming_session, self.user,
                    'llm_processing_time', llm_time
                )
            
            # Send final chunk with is_final=True
            await self.send(text_data=json.dumps({
                'type': 'llm_response_chunk',
                'content': '',  # Empty content for final chunk
                'chunk_id': request_id,
                'is_final': True,
                'timestamp': timezone.now().isoformat()
            }))
            
            # Stop typing indicator
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'stopped',
                'conversation_id': str(conversation.id),
                'timestamp': timezone.now().isoformat()
            }))
            
            # Remove typing indicator messages
            await self.remove_typing_indicators(conversation, 'assistant')
            
            # Save complete AI response to database
            ai_message = await self.save_message(
                conversation=conversation,
                sender_type='assistant',
                content=full_response_content,
                message_type='text',
                model_used='agent_coordinator',
                tokens_used=len(full_response_content.split()) * 1.3,  # Rough estimate
                processing_time=llm_time if llm_time else None
            )
            
            # Cache response for future use
            if self.user and full_response_content:
                cache_service_instance.cache_query_response(
                    self.user.id, user_input, full_response_content
                )
            
            # End total response timer
            total_time = performance_monitor.end_timer(request_id, 'total_response_time')
            if total_time and self.streaming_session and self.user:
                performance_monitor.record_metric(
                    request_id, self.streaming_session, self.user,
                    'total_response_time', total_time
                )
            
            # Update relationship metrics
            await self.update_relationship_metrics(conversation, 'conversation')
            
        except Exception as e:
            logger.error(f"Error generating streaming AI response: {e}")
            
            # Stop typing indicator on error
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'stopped'
            }))
            
            # Send error to client
            await self.send_error(f"Failed to generate AI response: {str(e)}")
            
            # Record error
            if self.streaming_session and self.user:
                performance_monitor.record_error(
                    request_id, 'ai_generation_error', str(e)
                )
                id = str(uuid.uuid4())
            
        # Start LLM timer
        performance_monitor.start_timer(request_id, 'llm_first_token_time')
        performance_monitor.start_timer(request_id, 'llm_total_time')
        
        try:
            # Send typing indicator
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'started',
                'request_id': request_id
            }))
            
            # End memory retrieval timer if it was started
            memory_time = performance_monitor.end_timer(request_id, 'memory_retrieval_time')
            if memory_time and self.streaming_session and self.user:
                performance_monitor.record_metric(
                    request_id, self.streaming_session, self.user,
                    'memory_retrieval_time', memory_time
                )
            
            # Generate streaming response using chat service with emotion context
            response_chunks = []
            chunk_id = str(uuid.uuid4())
            first_token_received = False
            
            async for response_chunk in self.chat_service.generate_streaming_response(
                conversation_id=str(conversation.id),
                user_input=user_input,
                emotion_context=emotion_context
            ):
                # Record first token time
                if not first_token_received:
                    first_token_received = True
                    llm_first_token_time = performance_monitor.end_timer(request_id, 'llm_first_token_time')
                    if llm_first_token_time and self.streaming_session and self.user:
                        performance_monitor.record_metric(
                            request_id, self.streaming_session, self.user,
                            'llm_first_token_time', llm_first_token_time
                        )
                
                # Send LLM response chunk
                await self.send(text_data=json.dumps({
                    'type': 'llm_response_chunk',
                    'content': response_chunk.get('text', ''),
                    'chunk_id': chunk_id,
                    'is_final': response_chunk.get('is_final', False),
                    'timestamp': time.time() * 1000,
                    'request_id': request_id
                }))
                
                response_chunks.append(response_chunk.get('text', ''))
                
                # If we have complete response, start TTS streaming
                if response_chunk.get('is_final', False):
                    # End LLM total time timer
                    llm_total_time = performance_monitor.end_timer(request_id, 'llm_total_time')
                    if llm_total_time and self.streaming_session and self.user:
                        performance_monitor.record_metric(
                            request_id, self.streaming_session, self.user,
                            'llm_total_time', llm_total_time
                        )
                    
                    full_response = ''.join(response_chunks)
                    
                    # Save AI message
                    ai_message = await self.save_message(
                        conversation=conversation,
                        sender_type='assistant',
                        content=full_response,
                        message_type='text',
                        model_used=response_chunk.get('model_used'),
                        tokens_used=response_chunk.get('tokens_used'),
                        processing_time=response_chunk.get('processing_time')
                    )
                    
                    # Record API usage
                    if self.user and response_chunk.get('tokens_used'):
                        performance_monitor.record_api_usage(
                            request_id, 'groq', {'tokens': response_chunk.get('tokens_used', 0)}
                        )
                    
                    # Cache the response for future use if it's not too long
                    if self.user and len(user_input) < 200 and len(full_response) < 1000:
                        cache_service_instance.cache_query_response(
                            self.user.id, user_input, full_response
                        )
                    
                    # Start TTS streaming
                    await self.stream_tts_response(full_response, emotion_context, request_id)
            
            # Stop typing indicator
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'stopped',
                'request_id': request_id
            }))
            
        except Exception as e:
            logger.error(f"Error generating streaming AI response: {e}")
            await self.send_error("Failed to generate response")
            
            # Record error
            if hasattr(self, 'streaming_session') and self.streaming_session and self.user:
                performance_monitor.record_error(
                    request_id, 'llm_generation_error', str(e)
                )
            
            # Stop typing indicator on error
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'stopped',
                'request_id': request_id
            }))
    
    async def stream_tts_response(self, text, emotion_context=None, request_id=None):
        """Stream TTS audio chunks to client."""
        if not request_id:
            request_id = str(uuid.uuid4())
            
        # Start TTS timer
        performance_monitor.start_timer(request_id, 'tts_first_chunk_time')
        performance_monitor.start_timer(request_id, 'tts_total_time')
        
        try:
            # Use chat service to generate streaming TTS
            first_chunk_sent = False
            
            async for audio_chunk in self.chat_service.generate_streaming_tts(
                text=text,
                emotion_context=emotion_context
            ):
                # Record first chunk time
                if not first_chunk_sent:
                    first_chunk_sent = True
                    tts_first_chunk_time = performance_monitor.end_timer(request_id, 'tts_first_chunk_time')
                    if tts_first_chunk_time and self.streaming_session and self.user:
                        performance_monitor.record_metric(
                            request_id, self.streaming_session, self.user,
                            'tts_first_chunk_time', tts_first_chunk_time
                        )
                
                # Send audio chunk
                await self.send(text_data=json.dumps({
                    'type': 'audio_chunk',
                    'data': base64.b64encode(audio_chunk.get('audio_data', b'')).decode('utf-8'),
                    'chunk_id': audio_chunk.get('chunk_id', str(uuid.uuid4())),
                    'is_final': audio_chunk.get('is_final', False),
                    'voice_settings': audio_chunk.get('voice_settings', {}),
                    'timestamp': time.time() * 1000,
                    'request_id': request_id
                }))
                
                # If this is the final chunk, record total TTS time and total response time
                if audio_chunk.get('is_final', False):
                    # End TTS total time timer
                    tts_total_time = performance_monitor.end_timer(request_id, 'tts_total_time')
                    if tts_total_time and self.streaming_session and self.user:
                        performance_monitor.record_metric(
                            request_id, self.streaming_session, self.user,
                            'tts_total_time', tts_total_time
                        )
                    
                    # End total response time timer
                    total_response_time = performance_monitor.end_timer(request_id, 'total_response_time')
                    if total_response_time and self.streaming_session and self.user:
                        performance_monitor.record_metric(
                            request_id, self.streaming_session, self.user,
                            'total_response_time', total_response_time
                        )
                        
                        # Record API usage for Hume
                        performance_monitor.record_api_usage(
                            request_id, 'hume', {'calls': 1}
                        )
                
        except Exception as e:
            logger.error(f"Error streaming TTS response: {e}")
            
            # Record error
            if hasattr(self, 'streaming_session') and self.streaming_session and self.user:
                performance_monitor.record_error(
                    request_id, 'tts_streaming_error', str(e)
                )
                
            # End total response time timer even on error
            total_response_time = performance_monitor.end_timer(request_id, 'total_response_time')
            if total_response_time and self.streaming_session and self.user:
                performance_monitor.record_metric(
                    request_id, self.streaming_session, self.user,
                    'total_response_time', total_response_time
                )
                
            # Continue without TTS if it fails
    
    async def generate_ai_response(self, conversation, user_input):
        """Legacy AI response generation (for backward compatibility)."""
        try:
            # Send typing indicator
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'started'
            }))
            
            # Generate response using chat service
            ai_response = await self.chat_service.generate_response(
                conversation_id=str(conversation.id),
                user_input=user_input
            )
            
            # Save AI message
            ai_message = await self.save_message(
                conversation=conversation,
                sender_type='assistant',
                content=ai_response['text'],
                message_type='text',
                model_used=ai_response.get('model_used'),
                tokens_used=ai_response.get('tokens_used'),
                processing_time=ai_response.get('processing_time')
            )
            
            # Send AI response
            await self.send(text_data=json.dumps({
                'type': 'ai_message',
                'message_id': str(ai_message.id),
                'content': ai_response['text'],
                'timestamp': ai_message.created_at.isoformat(),
                'has_audio': ai_response.get('has_audio', False),
                'audio_url': ai_response.get('audio_url')
            }))
            
            # Stop typing indicator
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'stopped'
            }))
            
        except Exception as e:
            logger.error(f"Error generating AI response: {e}")
            await self.send_error("Failed to generate response")
            
            # Stop typing indicator on error
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'stopped'
            }))
    
    async def send_error(self, message, error_code=None, details=None):
        """Send enhanced error message to client."""
        error_data = {
            'type': 'error',
            'message': message,
            'timestamp': time.time() * 1000
        }
        
        if error_code:
            error_data['error_code'] = error_code
        
        if details:
            error_data['details'] = details
        
        await self.send(text_data=json.dumps(error_data))
    
    def validate_message_data(self, data: Dict[str, Any], message_type: str) -> tuple[bool, str]:
        """
        Validate incoming message data based on message type.
        
        Args:
            data: Message data to validate
            message_type: Type of message being validated
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            if message_type == 'text_message':
                content = data.get('content', '').strip()
                if not content:
                    return False, "Message content cannot be empty"
                if len(content) > 10000:  # 10KB limit
                    return False, "Message content too long (max 10,000 characters)"
                
            elif message_type == 'audio_chunk':
                audio_data = data.get('data')
                if not audio_data:
                    return False, "Audio data is required"
                
                # Validate base64 format
                try:
                    base64.b64decode(audio_data)
                except Exception:
                    return False, "Invalid base64 audio data"
                
                # Check chunk size (max 1MB)
                if len(audio_data) > 1024 * 1024:
                    return False, "Audio chunk too large (max 1MB)"
                
                chunk_id = data.get('chunk_id')
                if not chunk_id:
                    return False, "Chunk ID is required"
                
            elif message_type == 'emotion_feedback':
                emotion_scores = data.get('emotion_scores')
                if not isinstance(emotion_scores, dict):
                    return False, "Emotion scores must be a dictionary"
                
                user_validation = data.get('user_validation')
                if not isinstance(user_validation, bool):
                    return False, "User validation must be a boolean"
                
            elif message_type == 'conversation_switch':
                conversation_id = data.get('conversation_id')
                if conversation_id and not isinstance(conversation_id, str):
                    return False, "Conversation ID must be a string"
            
            return True, ""
            
        except Exception as e:
            return False, f"Validation error: {str(e)}"
    
    async def handle_message_with_validation(self, data: Dict[str, Any], message_type: str):
        """Handle message with validation and error handling."""
        # Validate message data
        is_valid, error_message = self.validate_message_data(data, message_type)
        if not is_valid:
            await self.send_error(error_message, error_code='VALIDATION_ERROR')
            return False
        
        # Check rate limiting
        if not await self.check_rate_limit(message_type):
            await self.send_error(
                "Rate limit exceeded. Please slow down.",
                error_code='RATE_LIMIT_EXCEEDED'
            )
            return False
        
        return True
    
    async def check_rate_limit(self, message_type: str) -> bool:
        """
        Check if user is within rate limits for message type.
        
        Args:
            message_type: Type of message to check
            
        Returns:
            True if within limits, False if rate limited
        """
        # Simple in-memory rate limiting (in production, use Redis)
        current_time = time.time()
        
        if not hasattr(self, '_rate_limit_data'):
            self._rate_limit_data = {}
        
        user_id = str(self.user.id)
        if user_id not in self._rate_limit_data:
            self._rate_limit_data[user_id] = {}
        
        user_limits = self._rate_limit_data[user_id]
        
        # Define rate limits (requests per minute)
        limits = {
            'text_message': 60,      # 60 text messages per minute
            'audio_chunk': 600,      # 600 audio chunks per minute (10 per second)
            'emotion_feedback': 30,  # 30 emotion feedback per minute
        }
        
        limit = limits.get(message_type, 60)
        window = 60  # 1 minute window
        
        # Clean old entries
        if message_type in user_limits:
            user_limits[message_type] = [
                timestamp for timestamp in user_limits[message_type]
                if current_time - timestamp < window
            ]
        else:
            user_limits[message_type] = []
        
        # Check if within limit
        if len(user_limits[message_type]) >= limit:
            return False
        
        # Add current request
        user_limits[message_type].append(current_time)
        return True
    
    # Database operations (async wrappers)
    
    @database_sync_to_async
    def create_chat_session(self):
        """Create a new chat session."""
        return ChatSession.objects.create(
            user=self.user,
            channel_name=self.channel_name,
            ip_address=self.scope.get('client', ['', ''])[0],
            user_agent=dict(self.scope.get('headers', {})).get(b'user-agent', b'').decode()
        )
    
    @database_sync_to_async
    def disconnect_chat_session(self):
        """Mark chat session as disconnected."""
        self.chat_session.disconnect()
    
    @database_sync_to_async
    def get_or_create_conversation(self, conversation_id=None):
        """Get existing conversation or create new one."""
        if conversation_id:
            try:
                conversation = Conversation.objects.get(
                    id=conversation_id,
                    user=self.user
                )
                return conversation
            except Conversation.DoesNotExist:
                pass
        
        # Create new conversation
        conversation = Conversation.objects.create(
            user=self.user,
            title=f"Chat {timezone.now().strftime('%Y-%m-%d %H:%M')}"
        )
        return conversation
    
    @database_sync_to_async
    def get_conversation(self, conversation_id):
        """Get conversation by ID."""
        try:
            return Conversation.objects.get(
                id=conversation_id,
                user=self.user
            )
        except Conversation.DoesNotExist:
            return None
    
    @database_sync_to_async
    def update_chat_session_conversation(self, conversation):
        """Update chat session with current conversation."""
        self.chat_session.conversation = conversation
        self.chat_session.save(update_fields=['conversation'])
    
    @database_sync_to_async
    def save_message(self, conversation, sender_type, content, message_type='text', **kwargs):
        """Save message to database."""
        return Message.objects.create(
            conversation=conversation,
            sender_type=sender_type,
            content=content,
            message_type=message_type,
            **kwargs
        )
    
    @database_sync_to_async
    def create_streaming_session(self):
        """Create a new streaming session."""
        return StreamingSession.objects.create(
            user=self.user,
            session_id=self.session_id,
            websocket_id=self.channel_name,
            is_active=True,
            performance_metrics={}
        )
    
    @database_sync_to_async
    def disconnect_streaming_session(self):
        """Mark streaming session as disconnected."""
        if self.streaming_session:
            self.streaming_session.end_session(reason='user_disconnect')
    
    @database_sync_to_async
    def update_streaming_session_activity(self):
        """Update streaming session last activity."""
        if self.streaming_session:
            self.streaming_session.last_activity = timezone.now()
            self.streaming_session.save(update_fields=['last_activity'])
    
    @database_sync_to_async
    def store_emotion_context(self, emotion_analysis, chunk_id):
        """Store emotion context from audio analysis."""
        try:
            return EmotionContext.objects.create(
                user=self.user,
                session_id=self.session_id,
                audio_emotions={
                    'emotions': [
                        {'name': emotion.name, 'score': emotion.score}
                        for emotion in emotion_analysis.emotions
                    ],
                    'confidence_score': emotion_analysis.confidence_score,
                    'chunk_id': chunk_id
                },
                text_emotions={},  # Will be filled by text analysis
                context_emotions={},  # Will be filled by context builder
                confidence_score=emotion_analysis.confidence_score
            )
        except Exception as e:
            logger.error(f"Error storing emotion context: {e}")
            return None
    
    @database_sync_to_async
    def store_emotion_feedback(self, emotion_scores, user_validation, chunk_id):
        """Store user feedback on emotion detection."""
        try:
            # Find the corresponding emotion context
            emotion_context = EmotionContext.objects.filter(
                user=self.user,
                session_id=self.session_id,
                audio_emotions__chunk_id=chunk_id
            ).first()
            
            if emotion_context:
                # Update with user feedback
                emotion_context.audio_emotions['user_feedback'] = {
                    'emotion_scores': emotion_scores,
                    'validation': user_validation,
                    'timestamp': time.time() * 1000
                }
                emotion_context.save(update_fields=['audio_emotions'])
                return emotion_context
            
        except Exception as e:
            logger.error(f"Error storing emotion feedback: {e}")
            return None
    
    async def reset_session_state(self):
        """Reset session state for reconnection."""
        try:
            self.is_processing_audio = False
            self.current_audio_chunks = []
            
            # Update streaming session
            if self.streaming_session:
                await self.update_streaming_session_activity()
            
            logger.info(f"Session state reset for user: {self.user.email}")
            
        except Exception as e:
            logger.error(f"Error resetting session state: {e}")
    
    # Connection Management Methods
    
    async def heartbeat_monitor(self):
        """Monitor connection health with periodic heartbeats."""
        try:
            while self.connection_state in ['active', 'connecting']:
                await asyncio.sleep(self.heartbeat_interval)
                
                if self.connection_state != 'active':
                    break
                
                current_time = time.time()
                
                # Check if we've received a heartbeat recently
                if self.last_heartbeat and (current_time - self.last_heartbeat) > (self.heartbeat_interval * 2):
                    logger.warning(f"Heartbeat timeout for user {self.user.email}")
                    await self.handle_connection_timeout()
                    break
                
                # Send heartbeat request to client
                await self.send(text_data=json.dumps({
                    'type': 'heartbeat_request',
                    'timestamp': current_time * 1000,
                    'session_id': self.session_id
                }))
                
        except asyncio.CancelledError:
            logger.debug("Heartbeat monitor cancelled")
        except Exception as e:
            logger.error(f"Error in heartbeat monitor: {e}")
    
    async def handle_connection_timeout(self):
        """Handle connection timeout."""
        try:
            logger.info(f"Connection timeout for user {self.user.email}")
            
            # Save current state
            await self.save_session_state()
            
            # Notify client of timeout
            await self.send(text_data=json.dumps({
                'type': 'connection_timeout',
                'message': 'Connection timeout detected',
                'session_id': self.session_id,
                'timestamp': time.time() * 1000
            }))
            
            # Close connection
            await self.close()
            
        except Exception as e:
            logger.error(f"Error handling connection timeout: {e}")
    
    async def save_session_state(self):
        """Save current session state for reconnection."""
        try:
            if not self.streaming_session:
                return
            
            # Save session state to database or cache
            session_state = {
                'conversation_id': str(self.conversation.id) if self.conversation else None,
                'is_processing_audio': self.is_processing_audio,
                'current_audio_chunks': len(self.current_audio_chunks),
                'last_activity': time.time(),
                'connection_state': self.connection_state,
                'queued_messages': len(self.message_queue)
            }
            
            # Update streaming session with state
            self.streaming_session.performance_metrics['session_state'] = session_state
            await database_sync_to_async(lambda: self.streaming_session.save(
                update_fields=['performance_metrics']
            ))()
            
            logger.debug(f"Saved session state for {self.user.email}")
            
        except Exception as e:
            logger.error(f"Error saving session state: {e}")
    
    async def restore_session_state(self, previous_session_id):
        """Restore session state from previous connection."""
        try:
            # Find previous streaming session
            previous_session = await database_sync_to_async(
                StreamingSession.objects.filter(
                    user=self.user,
                    session_id=previous_session_id
                ).first
            )()
            
            if not previous_session:
                return False
            
            # Restore state from previous session
            session_state = previous_session.performance_metrics.get('session_state', {})
            
            if session_state.get('conversation_id'):
                self.conversation = await self.get_conversation(session_state['conversation_id'])
            
            self.is_processing_audio = session_state.get('is_processing_audio', False)
            
            # Reset reconnection attempts on successful restore
            self.reconnection_attempts = 0
            
            logger.info(f"Restored session state for {self.user.email}")
            return True
            
        except Exception as e:
            logger.error(f"Error restoring session state: {e}")
            return False
    
    async def reset_connection_state(self):
        """Reset connection state after reconnection."""
        try:
            self.connection_state = 'active'
            self.last_heartbeat = time.time()
            
            # Process any queued messages
            await self.process_queued_messages()
            
            logger.debug(f"Reset connection state for {self.user.email}")
            
        except Exception as e:
            logger.error(f"Error resetting connection state: {e}")
    
    async def queue_message(self, message_data):
        """Queue message for delivery when connection is restored."""
        try:
            if len(self.message_queue) >= self.max_queue_size:
                # Remove oldest message
                self.message_queue.pop(0)
            
            message_data['queued_at'] = time.time() * 1000
            self.message_queue.append(message_data)
            
            logger.debug(f"Queued message for {self.user.email}")
            
        except Exception as e:
            logger.error(f"Error queuing message: {e}")
    
    async def process_queued_messages(self):
        """Process any queued messages after reconnection."""
        try:
            if not self.message_queue:
                return
            
            logger.info(f"Processing {len(self.message_queue)} queued messages for {self.user.email}")
            
            # Send queued messages
            for message_data in self.message_queue:
                message_data['type'] = 'queued_message'
                message_data['delivered_at'] = time.time() * 1000
                
                await self.send(text_data=json.dumps(message_data))
            
            # Clear queue
            self.message_queue.clear()
            
        except Exception as e:
            logger.error(f"Error processing queued messages: {e}")
    
    async def send_with_queue_fallback(self, message_data):
        """Send message with fallback to queuing if connection is down."""
        try:
            if self.connection_state == 'active':
                await self.send(text_data=json.dumps(message_data))
            else:
                await self.queue_message(message_data)
                
        except Exception as e:
            logger.error(f"Error sending message, queuing instead: {e}")
            await self.queue_message(message_data)
    @database_sync_to_async
    def get_most_recent_conversation(self):
        """Get the most recent conversation for the current user."""
        if not self.user:
            return None
        
        try:
            return Conversation.objects.filter(
                user=self.user,
                is_active=True
            ).order_by('-last_message_at').first()
        except Exception as e:
            logger.error(f"Error getting most recent conversation: {e}")
            return None
    
    @database_sync_to_async
    def remove_typing_indicators(self, conversation, sender_type):
        """Remove typing indicators from the database."""
        try:
            Message.objects.filter(
                conversation=conversation,
                sender_type=sender_type,
                message_type='typing'
            ).delete()
            return True
        except Exception as e:
            logger.error(f"Error removing typing indicators: {e}")
            return False
    
    @database_sync_to_async
    def update_relationship_metrics(self, conversation, interaction_type):
        """Update relationship metrics based on user interaction."""
        try:
            from chat.models_realtime import UserRelationship
            
            # Get or create relationship
            relationship, created = UserRelationship.objects.get_or_create(
                user=self.user
            )
            
            # Update interaction metrics
            relationship.total_interactions += 1
            
            # Update emotional intimacy based on interaction type
            if interaction_type == 'typing_engagement':
                # Small boost for active typing engagement
                intimacy_boost = 0.01
            elif interaction_type == 'voice_message':
                # Larger boost for voice messages (more personal)
                intimacy_boost = 0.02
            elif interaction_type == 'emotion_sharing':
                # Significant boost for sharing emotions
                intimacy_boost = 0.03
            else:
                # Default small boost for any interaction
                intimacy_boost = 0.005
            
            # Apply the boost with a cap at 1.0
            relationship.emotional_intimacy_score = min(
                1.0, 
                relationship.emotional_intimacy_score + intimacy_boost
            )
            
            # Save the relationship
            relationship.save(update_fields=[
                'total_interactions', 
                'emotional_intimacy_score',
                'last_interaction'
            ])
            
            return True
        except Exception as e:
            logger.error(f"Error updating relationship metrics: {e}")
            return False    

    @database_sync_to_async
    def get_conversation_history(self, conversation, limit=10):
        """Get recent conversation history."""
        try:
            messages = Message.objects.filter(
                conversation=conversation,
                is_deleted=False,
                message_type__in=['text', 'audio']  # Only include actual messages
            ).order_by('-created_at')[:limit]
            
            # Convert to list and reverse to get chronological order
            history = []
            for msg in reversed(list(messages)):
                history.append({
                    'sender_type': msg.sender_type,
                    'content': msg.content,
                    'message_type': msg.message_type,
                    'created_at': msg.created_at.isoformat()
                })
            
            return history
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []
    
    async def get_memory_context(self, query):
        """Get memory context for the current user and query."""
        try:
            # Import memory manager from agents app
            from agents.services.memory_manager import get_memory_manager
            
            # Get memory manager instance
            memory_manager = get_memory_manager()
            
            if not memory_manager or not self.user:
                return None
            
            # Search for relevant memories
            memories = await memory_manager.async_search_memories(
                query=query,
                user_id=str(self.user.id),
                k=5,
                min_importance=0.3
            )
            
            return {'memories': memories} if memories else None
            
        except Exception as e:
            logger.error(f"Error getting memory context: {e}")
            return None   

    async def generate_fallback_response(self, conversation, user_input, request_id):
        """Generate a fallback response when agent services are not available."""
        try:
            # Get fallback response
            fallback_response = get_fallback_response('api_failure')
            
            # Send typing indicator
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'started',
                'conversation_id': str(conversation.id),
                'timestamp': timezone.now().isoformat()
            }))
            
            # Simulate thinking time
            await asyncio.sleep(0.5)
            
            # Send response in chunks to simulate streaming
            words = fallback_response.split()
            chunks = []
            
            # Send in small chunks to simulate streaming
            current_chunk = []
            for i, word in enumerate(words):
                current_chunk.append(word)
                
                # Send every few words or at the end
                if len(current_chunk) >= 3 or i == len(words) - 1:
                    chunk_text = ' '.join(current_chunk)
                    chunks.append(chunk_text)
                    
                    chunk_message = {
                        'type': 'llm_response_chunk',
                        'content': chunk_text,
                        'chunk_id': request_id,
                        'is_final': i == len(words) - 1,
                        'timestamp': timezone.now().isoformat()
                    }
                    await self.send(text_data=json.dumps(chunk_message))
                    
                    current_chunk = []
                    await asyncio.sleep(0.1)  # Small delay between chunks
            
            # Send final chunk with is_final=True
            await self.send(text_data=json.dumps({
                'type': 'llm_response_chunk',
                'content': '',  # Empty content for final chunk
                'chunk_id': request_id,
                'is_final': True,
                'timestamp': timezone.now().isoformat()
            }))
            
            # Stop typing indicator
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'stopped',
                'conversation_id': str(conversation.id),
                'timestamp': timezone.now().isoformat()
            }))
            
            # Remove typing indicator messages
            await self.remove_typing_indicators(conversation, 'assistant')
            
            # Save fallback response to database
            ai_message = await self.save_message(
                conversation=conversation,
                sender_type='assistant',
                content=fallback_response,
                message_type='text',
                model_used='fallback',
                tokens_used=len(fallback_response.split()),
                processing_time=None
            )
            
            # End total response timer
            total_time = performance_monitor.end_timer(request_id, 'total_response_time')
            if total_time and self.streaming_session and self.user:
                performance_monitor.record_metric(
                    request_id, self.streaming_session, self.user,
                    'total_response_time', total_time
                )
            
        except Exception as e:
            logger.error(f"Error generating fallback response: {e}")
            
            # Stop typing indicator on error
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'stopped'
            }))
            
            # Send error to client
            await self.send_error(f"Failed to generate response: {str(e)}")
            
            # Record error
            if self.streaming_session and self.user:
                performance_monitor.record_error(
                    request_id, 'fallback_error', str(e)
                )
    
    async def check_agents_availability(self):
        """Check if the agents app is available and properly configured."""
        if not AGENTS_AVAILABLE:
            return False
        
        try:
            # Try to get agent coordinator
            agent_coordinator = get_agent_coordinator()
            
            # Perform a health check
            health_status = await agent_coordinator.health_check()
            
            # Check if overall status is healthy
            return health_status.get('overall_status') == 'healthy'
        except Exception as e:
            logger.error(f"Error checking agents availability: {e}")
            return False