import json
import logging
import base64
import uuid
import time
import random
from typing import Dict, Any, Optional, List
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from django.contrib.auth.models import AnonymousUser
from django.utils import timezone
from .models import ChatSession, Conversation, Message, StreamingSession, EmotionContext
from .services import (
    ChatService, 
    AudioProcessingService, 
    AudioChunk,
    get_audio_service
)
from .services.performance_monitor import performance_monitor
from .services.cache_service import cache_service_instance
from .services.error_recovery import get_fallback_response
import asyncio

# Import agent services
try:
    from agents.services.agent_coordinator import get_agent_coordinator
    from agents.services.memory_manager import get_memory_manager
    from agents.services.langgraph_orchestrator import LangGraphOrchestrator
    AGENTS_AVAILABLE = True
except ImportError:
    AGENTS_AVAILABLE = False
    logging.warning("Agents app not available. Using fallback response generation.")

logger = logging.getLogger(__name__)


class ChatConsumer(AsyncWebsocketConsumer):
    """Enhanced WebSocket consumer for real-time AI companion with streaming support."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.user = None
        self.chat_session = None
        self.conversation = None
        self.chat_service = None
        
        # Real-time streaming components
        self.streaming_session = None
        self.audio_service = None
        self.session_id = None
        self.is_processing_audio = False
        self.current_audio_chunks = []
        self.reconnection_attempts = 0
        self.max_reconnection_attempts = 5
        
        # Connection management
        self.connection_state = 'disconnected'
        self.last_heartbeat = None
        self.heartbeat_interval = 30  # seconds
        self.heartbeat_task = None
        self.message_queue = []
        self.max_queue_size = 100
        self.session_state = {}
        self.connection_id = None
    
    async def connect(self):
        """Handle WebSocket connection with streaming session setup."""
        # Get user from scope (set by AuthMiddleware)
        self.user = self.scope.get("user")

        if not self.user or isinstance(self.user, AnonymousUser):
            logger.warning("Anonymous or missing user attempted to connect to chat WebSocket")
            await self.close()
            return
        
        # Accept the connection
        await self.accept()
        
        # Generate session and connection IDs
        self.session_id = str(uuid.uuid4())
        self.connection_id = str(uuid.uuid4())
        
        # Set connection state
        self.connection_state = 'connecting'
        
        # Create chat session and streaming session
        self.chat_session = await self.create_chat_session()
        self.streaming_session = await self.create_streaming_session()
        
        # Initialize services
        self.chat_service = ChatService(user=self.user)
        self.audio_service = get_audio_service()

        # Initialize memory cache service
        from chat.services.memory_cache_service import memory_cache_service
        self.memory_cache_service = memory_cache_service

        # Initialize expression measurement service
        from chat.services.expression_measurement_service import expression_measurement_service
        self.expression_service = expression_measurement_service

        # Preload user memories for fast access
        await self.memory_cache_service.preload_user_memories(
            user_id=str(self.user.id),
            session_id=self.session_id
        )
        
        # Start heartbeat monitoring
        self.heartbeat_task = asyncio.create_task(self.heartbeat_monitor())
        
        # Set connection state to active
        self.connection_state = 'active'
        self.last_heartbeat = time.time()
        
        logger.info(f"Enhanced chat WebSocket connected for user: {self.user.email}")

        # Check agents availability
        agents_available = await self.check_agents_availability()



        # Send welcome message with streaming capabilities
        await self.send(text_data=json.dumps({
            'type': 'connection_established',
            'message': 'Connected to real-time AI companion',
            'session_id': self.session_id,
            'connection_id': self.connection_id,
            'chat_session_id': str(self.chat_session.id),
            'streaming_enabled': True,
            'heartbeat_interval': self.heartbeat_interval,
            'agents_available': agents_available,
            'supported_message_types': [
                'text_message',
                'audio_chunk', 
                'emotion_feedback',
                'typing_start',
                'typing_stop',
                'conversation_switch',
                'connection_heartbeat',
                'reconnection_request'
            ]
        }))
        
        # Process any queued messages from previous connection
        await self.process_queued_messages()


    async def disconnect(self, close_code):
        """Handle WebSocket disconnection with cleanup."""
        try:
            # Set connection state
            self.connection_state = 'disconnecting'
            
            # Cancel heartbeat monitoring
            if self.heartbeat_task:
                self.heartbeat_task.cancel()
                try:
                    await self.heartbeat_task
                except asyncio.CancelledError:
                    pass
            
            # Save session state for potential reconnection
            await self.save_session_state()
            
            # Clean up streaming session
            if self.streaming_session:
                await self.disconnect_streaming_session()
            
            # Clean up chat session
            if self.chat_session:
                await self.disconnect_chat_session()
            
            # Remove any active typing indicators
            if self.conversation:
                await self.remove_typing_indicators(self.conversation, 'user')
            
            # Cancel any ongoing audio processing
            self.is_processing_audio = False

            # Clean up memory cache for this session
            if hasattr(self, 'memory_cache_service') and hasattr(self, 'session_id') and self.user:
                await self.memory_cache_service.invalidate_user_cache(
                    user_id=str(self.user.id),
                    session_id=self.session_id
                )

                # Log cache performance stats
                stats = self.memory_cache_service.get_cache_stats()
                logger.info(f"Memory cache stats for session {self.session_id}: {stats}")

            # Set final connection state
            self.connection_state = 'disconnected'

            logger.info(f"Enhanced chat WebSocket disconnected for user: {self.user.email if self.user else 'Unknown'} (code: {close_code})")
            
        except Exception as e:
            logger.error(f"Error during WebSocket disconnect cleanup: {e}")
    
    async def receive(self, text_data):
        """Handle incoming WebSocket messages with enhanced streaming support."""
        try:
            data = json.loads(text_data)
            message_type = data.get('type')
            
            # Update streaming session activity
            if self.streaming_session:
                await self.update_streaming_session_activity()
            
            # Route messages to appropriate handlers with validation
            if message_type == 'text_message':
                if await self.handle_message_with_validation(data, message_type):
                    await self.handle_text_message(data)
            elif message_type == 'audio_chunk':
                if await self.handle_message_with_validation(data, message_type):
                    await self.handle_audio_chunk(data)
            elif message_type == 'emotion_feedback':
                if await self.handle_message_with_validation(data, message_type):
                    await self.handle_emotion_feedback(data)
            elif message_type == 'typing_start':
                await self.handle_typing_start()
            elif message_type == 'typing_stop':
                await self.handle_typing_stop()
            elif message_type == 'conversation_switch':
                await self.handle_conversation_switch(data)
            elif message_type == 'connection_heartbeat':
                await self.handle_heartbeat(data)
            elif message_type == 'reconnection_request':
                await self.handle_reconnection_request(data)
            # Legacy support
            elif message_type == 'chat_message':
                await self.handle_text_message(data)
            elif message_type == 'voice_message':
                await self.handle_legacy_voice_message(data)
            else:
                logger.warning(f"Unknown message type: {message_type}")
                await self.send_error(f"Unknown message type: {message_type}")
        
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON received in WebSocket: {e}")
            await self.send_error("Invalid JSON format")
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {e}")
            await self.send_error("Internal server error")
    
    async def handle_text_message(self, data):
        """Handle text messages with enhanced processing."""
        content = data.get('content', '').strip()
        conversation_id = data.get('conversation_id')
        timestamp = data.get('timestamp')
        
        if not content:
            await self.send_error("Message content cannot be empty")
            return
        
        # Generate a unique request ID for tracking performance
        request_id = str(uuid.uuid4())
        
        # Start timing the total response
        performance_monitor.start_timer(request_id, 'total_response_time')
        
        try:
            # Get or create conversation
            conversation = await self.get_or_create_conversation(conversation_id)
            
            # Check if we have a cached response for this query
            cached_response = None
            if self.user:
                cached_response = cache_service_instance.get_cached_query_response(
                    self.user.id, content
                )
            
            # Save user message
            user_message = await self.save_message(
                conversation=conversation,
                sender_type='user',
                content=content,
                message_type='text'
            )
            
            # Send acknowledgment
            await self.send(text_data=json.dumps({
                'type': 'message_received',
                'message_id': str(user_message.id),
                'timestamp': user_message.created_at.isoformat(),
                'processing_started': True,
                'request_id': request_id
            }))
            
            # Generate AI response with streaming
            if cached_response:
                logger.info(f"Using cached response for user {self.user.id}, request {request_id}")
                # Send cached response with minimal delay to simulate streaming
                await self.send_cached_response(cached_response, user_message)
                
                # Record performance metrics for cached response
                response_time = performance_monitor.end_timer(request_id, 'total_response_time')
                if response_time and self.streaming_session and self.user:
                    performance_monitor.record_metric(
                        request_id, self.streaming_session, self.user, 
                        'total_response_time', response_time
                    )
                    performance_monitor.record_metric(
                        request_id, self.streaming_session, self.user,
                        'cache_hit', 1.0
                    )
            else:
                # Start memory retrieval timer
                performance_monitor.start_timer(request_id, 'memory_retrieval_time')
                
                # Send typing indicator before generating response
                await self.send(text_data=json.dumps({
                    'type': 'ai_typing',
                    'status': 'started',
                    'conversation_id': str(conversation.id),
                    'timestamp': timezone.now().isoformat()
                }))
                
                # Save typing indicator message
                await self.save_message(
                    conversation=conversation,
                    sender_type='assistant',
                    content='',
                    message_type='typing'
                )
                
                # Generate streaming response
                await self.generate_streaming_ai_response(conversation, content, request_id)
            
        except Exception as e:
            logger.error(f"Error handling text message: {e}")
            await self.send_error("Failed to process message")
            
            # Record error
            if hasattr(self, 'streaming_session') and self.streaming_session and self.user:
                performance_monitor.record_error(
                    request_id, 'text_processing_error', str(e)
                )
    
    async def handle_audio_chunk(self, data):
        """Handle real-time audio chunks for streaming transcription and emotion detection."""
        # Generate a unique request ID for tracking performance
        request_id = str(uuid.uuid4())
        
        # Start timing the audio processing
        performance_monitor.start_timer(request_id, 'audio_processing_time')
        
        try:
            # Extract audio chunk data
            audio_data_b64 = data.get('data')
            chunk_id = data.get('chunk_id', str(uuid.uuid4()))
            is_final = data.get('is_final', False)
            timestamp = data.get('timestamp', time.time() * 1000)
            
            if not audio_data_b64:
                await self.send_error("Audio data is required")
                return
            
            # Decode base64 audio data
            try:
                audio_data = base64.b64decode(audio_data_b64)

                # Save audio chunk to file for debugging
                await self._save_audio_chunk_for_debugging(audio_data, chunk_id, is_final)

            except Exception as e:
                logger.error(f"Failed to decode audio data: {e}")
                await self.send_error("Invalid audio data format")
                return
            
            # Create audio chunk
            audio_chunk = AudioChunk(
                data=audio_data,
                chunk_id=chunk_id,
                timestamp_ms=timestamp,
                sample_rate=16000,  # Default 16kHz to match frontend
                channels=1,
                is_final=is_final,
                user_id=str(self.user.id)
            )
            
            # Process audio chunk with streaming results
            self.is_processing_audio = True
            
            # Start timing for transcription and emotion detection
            performance_monitor.start_timer(request_id, 'transcription_time')
            performance_monitor.start_timer(request_id, 'emotion_detection_time')

            # Store audio data for fast response service
            self._last_audio_data = audio_data

            # Start expression measurement analysis in parallel
            expression_task = None
            if hasattr(self, 'expression_service') and self.expression_service:
                expression_task = asyncio.create_task(
                    self.expression_service.analyze_audio_chunk(audio_data, chunk_id)
                )
            
            async for result in self.audio_service.process_audio_chunk(
                audio_chunk,
                enable_transcription=True,
                enable_emotion_detection=True,
                parallel_processing=True,
                enable_preprocessing=True
            ):
                # Send transcription results
                if result.transcription:
                    # Enhanced debugging for transcription results
                    logger.info(f"🎤 Received transcription result: '{result.transcription.text}' (confidence: {result.transcription.confidence:.3f}, is_partial: {result.transcription.is_partial})")

                    # Only send non-empty transcriptions to frontend
                    if result.transcription.text.strip():
                        # End transcription timer if this is the first result
                        if not result.transcription.is_partial or is_final:
                            transcription_time = performance_monitor.end_timer(request_id, 'transcription_time')
                            if transcription_time and self.streaming_session and self.user:
                                performance_monitor.record_metric(
                                    request_id, self.streaming_session, self.user,
                                    'transcription_time', transcription_time
                                )

                        await self.send(text_data=json.dumps({
                            'type': 'transcription_partial',
                            'text': result.transcription.text,
                            'confidence': result.transcription.confidence,
                            'chunk_id': chunk_id,
                            'is_partial': result.transcription.is_partial,
                            'processing_time_ms': result.transcription.processing_time_ms,
                            'timestamp': timestamp,
                            'request_id': request_id
                        }))
                    else:
                        logger.info(f"🎤 Skipping empty transcription result for chunk {chunk_id}")
                else:
                    logger.warning(f"🎤 No transcription result for chunk {chunk_id} (transcription failed or skipped)")
                
                # Send emotion detection results
                if result.emotion_analysis:
                    # End emotion detection timer
                    emotion_time = performance_monitor.end_timer(request_id, 'emotion_detection_time')
                    if emotion_time and self.streaming_session and self.user:
                        performance_monitor.record_metric(
                            request_id, self.streaming_session, self.user,
                            'emotion_detection_time', emotion_time
                        )
                    
                    await self.send(text_data=json.dumps({
                        'type': 'emotion_detected',
                        'emotions': [
                            {
                                'name': emotion.name,
                                'score': emotion.score
                            }
                            for emotion in result.emotion_analysis.emotions
                        ],
                        'confidence_score': result.emotion_analysis.confidence_score,
                        'chunk_id': chunk_id,
                        'timestamp': timestamp,
                        'request_id': request_id
                    }))
                    
                    # Store emotion context
                    await self.store_emotion_context(result.emotion_analysis, chunk_id)
                    
                    # Cache emotion context for quick access
                    if self.user:
                        emotion_data = {
                            'primary_emotion': result.emotion_analysis.primary_emotion,
                            'emotions': [
                                {'name': e.name, 'score': e.score}
                                for e in result.emotion_analysis.emotions
                            ],
                            'confidence_score': result.emotion_analysis.confidence_score
                        }
                        cache_service_instance.cache_emotion_context(
                            self.user.id, self.session_id, emotion_data
                        )
                
                # If this is the final chunk and we have complete transcription
                if is_final and result.transcription and not result.transcription.is_partial:
                    # Check if transcription has meaningful content
                    transcription_text = result.transcription.text.strip()

                    if not transcription_text:
                        logger.warning(f"🎤 Final chunk has empty transcription, skipping AI response generation")
                        continue

                    logger.info(f"🎤 Processing final transcription: '{transcription_text}'")

                    # End audio processing timer
                    audio_time = performance_monitor.end_timer(request_id, 'audio_processing_time')
                    if audio_time and self.streaming_session and self.user:
                        performance_monitor.record_metric(
                            request_id, self.streaming_session, self.user,
                            'audio_processing_time', audio_time
                        )

                    # Generate AI response based on transcription
                    conversation = await self.get_or_create_conversation(
                        data.get('conversation_id')
                    )

                    # Save transcribed message
                    user_message = await self.save_message(
                        conversation=conversation,
                        sender_type='user',
                        content=transcription_text,
                        message_type='audio'
                    )
                    
                    # Start total response timer for voice interaction
                    performance_monitor.start_timer(request_id, 'total_response_time')
                    
                    # Get expression measurement result if available
                    expression_result = None
                    if expression_task:
                        try:
                            expression_result = await expression_task
                            if expression_result:
                                # Record expression measurement timing
                                expression_time = performance_monitor.end_timer(request_id, 'emotion_detection_time')
                                if expression_time and self.streaming_session and self.user:
                                    performance_monitor.record_metric(
                                        request_id, self.streaming_session, self.user,
                                        'emotion_detection_time', expression_time
                                    )

                                # Merge expression measurement with existing emotion context
                                expression_context = self.expression_service.get_emotion_context(expression_result)
                                if result.emotion_analysis:
                                    # If we already have emotion analysis, keep the existing one
                                    # Expression measurement is supplementary
                                    logger.debug(f"Keeping existing emotion analysis: {result.emotion_analysis.primary_emotion}")
                                else:
                                    result.emotion_analysis = expression_context

                                logger.info(f"Expression measurement completed: {expression_result.dominant_emotion} ({expression_result.confidence:.2f})")
                        except Exception as e:
                            logger.error(f"Error getting expression measurement result: {e}")

                    # Generate streaming AI response
                    await self.generate_streaming_ai_response(
                        conversation,
                        transcription_text,
                        emotion_context=result.emotion_analysis,
                        request_id=request_id
                    )

            self.is_processing_audio = False
            
        except Exception as e:
            logger.error(f"Error handling audio chunk: {e}")
            await self.send_error("Failed to process audio chunk")
            self.is_processing_audio = False
            
            # Record error
            if hasattr(self, 'streaming_session') and self.streaming_session and self.user:
                performance_monitor.record_error(
                    request_id, 'audio_processing_error', str(e)
                )
    
    async def handle_emotion_feedback(self, data):
        """Handle user feedback on emotion detection accuracy."""
        try:
            emotion_scores = data.get('emotion_scores', {})
            user_validation = data.get('user_validation', True)
            chunk_id = data.get('chunk_id')
            
            # Store emotion feedback for model improvement
            await self.store_emotion_feedback(emotion_scores, user_validation, chunk_id)
            
            # Send acknowledgment
            await self.send(text_data=json.dumps({
                'type': 'emotion_feedback_received',
                'chunk_id': chunk_id,
                'timestamp': time.time() * 1000
            }))
            
        except Exception as e:
            logger.error(f"Error handling emotion feedback: {e}")
            await self.send_error("Failed to process emotion feedback")
    
    async def send_cached_response(self, cached_response, user_message):
        """Send a cached response with simulated streaming."""
        try:
            # Send typing indicator
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'started'
            }))
            
            # Simulate streaming by sending the response in chunks
            chunk_id = str(uuid.uuid4())
            words = cached_response.split()
            chunks = []
            
            # Send in small chunks to simulate streaming
            current_chunk = []
            for i, word in enumerate(words):
                current_chunk.append(word)
                
                # Send every few words or at the end
                if len(current_chunk) >= 3 or i == len(words) - 1:
                    chunk_text = ' '.join(current_chunk)
                    chunks.append(chunk_text)
                    
                    await self.send(text_data=json.dumps({
                        'type': 'llm_response_chunk',
                        'content': chunk_text,
                        'chunk_id': chunk_id,
                        'is_final': i == len(words) - 1,
                        'timestamp': time.time() * 1000,
                        'is_cached': True
                    }))
                    
                    current_chunk = []
                    await asyncio.sleep(0.05)  # Small delay between chunks
            
            # Save AI message
            ai_message = await self.save_message(
                conversation=user_message.conversation,
                sender_type='assistant',
                content=cached_response,
                message_type='text',
                model_used='cached',
                tokens_used=0,
                processing_time=0
            )
            
            # Stop typing indicator
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'stopped'
            }))
            
        except Exception as e:
            logger.error(f"Error sending cached response: {e}")
            await self.send_error("Failed to send cached response")
            
            # Stop typing indicator on error
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'stopped'
            }))
    
    async def handle_heartbeat(self, data):
        """Handle connection heartbeat for monitoring."""
        self.last_heartbeat = time.time()
        
        await self.send(text_data=json.dumps({
            'type': 'heartbeat_response',
            'timestamp': time.time() * 1000,
            'session_id': self.session_id,
            'connection_id': self.connection_id,
            'connection_state': self.connection_state
        }))
    
    async def handle_reconnection_request(self, data):
        """Handle client reconnection requests with exponential backoff."""
        try:
            previous_session_id = data.get('previous_session_id')
            previous_connection_id = data.get('previous_connection_id')
            
            # Check if this is a valid reconnection
            if previous_session_id and previous_session_id != self.session_id:
                # Try to restore session state
                restored = await self.restore_session_state(previous_session_id)
                if restored:
                    logger.info(f"Restored session state for {self.user.email}")
                else:
                    logger.warning(f"Could not restore session state for {previous_session_id}")
            
            self.reconnection_attempts += 1
            
            if self.reconnection_attempts > self.max_reconnection_attempts:
                await self.send_error("Maximum reconnection attempts exceeded")
                await self.close()
                return
            
            # Calculate backoff delay
            backoff_delay = min(2 ** self.reconnection_attempts, 30)  # Max 30 seconds
            
            await self.send(text_data=json.dumps({
                'type': 'reconnection_accepted',
                'session_restored': True if previous_session_id == self.session_id else False,
                'backoff_delay': backoff_delay,
                'backoff_delay_seconds': backoff_delay,
                'attempt': self.reconnection_attempts,
                'max_attempts': self.max_reconnection_attempts,
                'session_id': self.session_id,
                'connection_id': self.connection_id
            }))
        except Exception as e:
            logger.error(f"Error handling reconnection request: {e}")

    async def handle_legacy_voice_message(self, data):
        """Handle legacy voice messages (for backward compatibility)."""
        audio_data = data.get('audio_data')  # Base64 encoded audio
        conversation_id = data.get('conversation_id')
        
        if not audio_data:
            await self.send_error("Audio data is required")
            return
        
        try:
            # Get or create conversation
            conversation = await self.get_or_create_conversation(conversation_id)
            
            # Process audio with speech-to-text
            transcription = await self.chat_service.transcribe_audio(audio_data)
            
            if not transcription:
                await self.send_error("Could not transcribe audio")
                return
            
            # Save user message with transcription
            user_message = await self.save_message(
                conversation=conversation,
                sender_type='user',
                content=transcription,
                message_type='audio'
            )
            
            # Send transcription back to user
            await self.send(text_data=json.dumps({
                'type': 'transcription_complete',
                'message_id': str(user_message.id),
                'transcription': transcription,
                'timestamp': user_message.created_at.isoformat()
            }))
            
            # Generate AI response
            await self.generate_ai_response(conversation, transcription)
            
        except Exception as e:
            logger.error(f"Error handling voice message: {e}")
            await self.send_error("Failed to process voice message")
    
    async def handle_voice_message(self, data):
        """Handle voice messages (audio data)."""
        audio_data = data.get('audio_data')  # Base64 encoded audio
        conversation_id = data.get('conversation_id')
        
        if not audio_data:
            await self.send_error("Audio data is required")
            return
        
        try:
            # Get or create conversation
            conversation = await self.get_or_create_conversation(conversation_id)
            
            # Process audio with speech-to-text
            transcription = await self.chat_service.transcribe_audio(audio_data)
            
            if not transcription:
                await self.send_error("Could not transcribe audio")
                return
            
            # Save user message with transcription
            user_message = await self.save_message(
                conversation=conversation,
                sender_type='user',
                content=transcription,
                message_type='audio'
            )
            
            # Send transcription back to user
            await self.send(text_data=json.dumps({
                'type': 'transcription_complete',
                'message_id': str(user_message.id),
                'transcription': transcription,
                'timestamp': user_message.created_at.isoformat()
            }))
            
            # Generate AI response
            await self.generate_ai_response(conversation, transcription)
            
        except Exception as e:
            logger.error(f"Error handling voice message: {e}")
            await self.send_error("Failed to process voice message")
    
    async def handle_typing_start(self):
        """Handle typing indicator start."""
        try:
            # Get current conversation
            conversation = self.conversation
            if not conversation:
                # If no active conversation, try to get the most recent one
                conversation = await self.get_most_recent_conversation()
                if not conversation:
                    await self.send_error("No active conversation for typing indicator")
                    return
            
            # Save typing indicator to database
            typing_message = await self.save_message(
                conversation=conversation,
                sender_type='user',
                content='',
                message_type='typing'
            )
            
            # Broadcast typing indicator to all connected clients for this conversation
            await self.send(text_data=json.dumps({
                'type': 'typing_acknowledged',
                'status': 'started',
                'conversation_id': str(conversation.id),
                'user_id': str(self.user.id),
                'timestamp': timezone.now().isoformat()
            }))
            
            # Update relationship metrics for active engagement
            await self.update_relationship_metrics(conversation, 'typing_engagement')
            
        except Exception as e:
            logger.error(f"Error handling typing start: {e}")
            await self.send_error("Failed to process typing indicator")
    
    async def handle_typing_stop(self):
        """Handle typing indicator stop."""
        try:
            # Get current conversation
            conversation = self.conversation
            if not conversation:
                # If no active conversation, try to get the most recent one
                conversation = await self.get_most_recent_conversation()
                if not conversation:
                    return
            
            # Remove typing indicator from database (optional)
            await self.remove_typing_indicators(conversation, 'user')
            
            # Broadcast typing stopped to all connected clients for this conversation
            await self.send(text_data=json.dumps({
                'type': 'typing_acknowledged',
                'status': 'stopped',
                'conversation_id': str(conversation.id),
                'user_id': str(self.user.id),
                'timestamp': timezone.now().isoformat()
            }))
            
        except Exception as e:
            logger.error(f"Error handling typing stop: {e}")
            # Don't send error for typing stop failures to avoid disrupting the UI
    
    async def handle_conversation_switch(self, data):
        """Handle switching to a different conversation."""
        conversation_id = data.get('conversation_id')
        
        try:
            if conversation_id:
                conversation = await self.get_conversation(conversation_id)
                if conversation:
                    self.conversation = conversation
                    await self.update_chat_session_conversation(conversation)
                    
                    await self.send(text_data=json.dumps({
                        'type': 'conversation_switched',
                        'conversation_id': str(conversation.id),
                        'conversation_title': conversation.title
                    }))
                else:
                    await self.send_error("Conversation not found")
            else:
                # Create new conversation
                self.conversation = None
                await self.send(text_data=json.dumps({
                    'type': 'new_conversation_ready'
                }))
        
        except Exception as e:
            logger.error(f"Error switching conversation: {e}")
            await self.send_error("Failed to switch conversation")
    
    async def generate_streaming_ai_response(self, conversation, user_input, emotion_context=None, request_id=None):
        """Generate and stream AI response with real-time delivery."""
        if not request_id:
            request_id = str(uuid.uuid4())
        
        try:
            # Get conversation history for context
            conversation_history = await self.get_conversation_history(conversation, limit=10)
            
            # Get emotion context if not provided
            if not emotion_context and self.user:
                emotion_context = cache_service_instance.get_cached_emotion_context(
                    self.user.id, self.session_id
                )
            
            # Start memory retrieval timer
            performance_monitor.start_timer(request_id, 'memory_retrieval_time')
            
            # Get memory context (if available)
            memory_context = await self.get_memory_context(user_input)
            
            # End memory retrieval timer
            memory_retrieval_time = performance_monitor.end_timer(request_id, 'memory_retrieval_time')
            if memory_retrieval_time and self.streaming_session and self.user:
                performance_monitor.record_metric(
                    request_id, self.streaming_session, self.user,
                    'memory_retrieval_time', memory_retrieval_time
                )
            
            # Start LLM processing timer
            performance_monitor.start_timer(request_id, 'llm_processing_time')
            
            # Use fast two-tier response system for ≤450ms first response
            from chat.services.fast_response_service import get_fast_response_service
            fast_service = get_fast_response_service(user=self.user)

            # Set audio data for background expression analysis
            if hasattr(self, '_last_audio_data') and self._last_audio_data:
                fast_service.set_audio_data(self._last_audio_data)
                logger.debug(f"Set audio data for expression analysis: {len(self._last_audio_data)} bytes")

            # Format conversation history for fast service
            formatted_history = []
            for msg in conversation_history:
                formatted_history.append({
                    'role': 'user' if msg['sender_type'] == 'user' else 'assistant',
                    'content': msg['content']
                })

            # Initialize response content
            full_response_content = ""

            # Process query through fast response service with immediate TTS target
            async for response_chunk in fast_service.process_query_fast(
                user_input=user_input,
                user_id=str(self.user.id),
                conversation_history=formatted_history,
                emotion_context=emotion_context,
                streaming=True,
                enable_tts=True  # Enable TTS for sub-450ms audio delivery
            ):
                # Handle different response types from fast service
                if response_chunk.get('type') == 'domain_classification':
                    # Domain classification result (legacy support)
                    domain = response_chunk.get('domain', 'general')
                    confidence = response_chunk.get('confidence', 0.0)

                    # Log domain classification
                    logger.info(f"Query classified as domain: {domain} (confidence: {confidence:.2f})")

                elif response_chunk.get('type') == 'response_chunk':
                    # Content chunk from streaming response
                    content = response_chunk.get('content', '')
                    if content:
                        full_response_content += content

                        # Send chunk to client
                        await self.send(text_data=json.dumps({
                            'type': 'llm_response_chunk',
                            'content': content,
                            'chunk_id': request_id,
                            'is_final': False,
                            'timestamp': timezone.now().isoformat()
                        }))

                elif response_chunk.get('type') == 'tts_chunk':
                    # TTS audio chunk from fast response service
                    audio_data = response_chunk.get('audio_data', '')
                    chunk_id = response_chunk.get('chunk_id', '')
                    is_final = response_chunk.get('is_final', False)

                    logger.info(f"🎵 Sending TTS chunk to frontend: chunk_id={chunk_id}, audio_bytes={len(audio_data)}, is_final={is_final}")

                    await self.send(text_data=json.dumps({
                        'type': 'audio_chunk',
                        'data': audio_data,
                        'chunk_id': chunk_id,
                        'chunk_index': response_chunk.get('chunk_index', 0),
                        'is_final': is_final,
                        'voice_settings': response_chunk.get('voice_settings', {}),
                        'metadata': response_chunk.get('metadata', {}),
                        'timestamp': timezone.now().isoformat(),
                        'request_id': request_id
                    }))

                    logger.info(f"🎵 TTS chunk sent successfully to frontend")

                elif response_chunk.get('type') == 'tts_error':
                    # TTS error from fast response service
                    logger.error(f"TTS error: {response_chunk.get('error', 'Unknown TTS error')}")
                    await self.send(text_data=json.dumps({
                        'type': 'tts_error',
                        'error': response_chunk.get('error', 'TTS processing failed'),
                        'timestamp': timezone.now().isoformat(),
                        'request_id': request_id
                    }))
                
                elif response_chunk.get('type') == 'response_complete':
                    # Complete response
                    if response_chunk.get('full_content'):
                        full_response_content = response_chunk.get('full_content')
                
                elif response_chunk.get('type') == 'agent_processing_started':
                    # Background agent processing started
                    message = response_chunk.get('message', 'Starting detailed analysis...')
                    logger.info(f"Background agent processing started: {message}")

                    # Optionally notify client about background processing
                    await self.send(text_data=json.dumps({
                        'type': 'background_processing',
                        'message': message,
                        'timestamp': timezone.now().isoformat()
                    }))

                elif response_chunk.get('type') == 'error':
                    # Error in processing
                    error_msg = response_chunk.get('error', 'Unknown error in AI processing')
                    logger.error(f"Error in AI response generation: {error_msg}")

                    # Send error to client
                    await self.send_error(f"AI processing error: {error_msg}")

                    # End LLM processing timer
                    llm_time = performance_monitor.end_timer(request_id, 'llm_processing_time')
                    if llm_time and self.streaming_session and self.user:
                        performance_monitor.record_metric(
                            request_id, self.streaming_session, self.user,
                            'llm_processing_time', llm_time
                        )

                    # Record error
                    if self.streaming_session and self.user:
                        performance_monitor.record_error(
                            request_id, 'ai_processing_error', error_msg
                        )
                    
                    return
            
            # End LLM processing timer
            llm_time = performance_monitor.end_timer(request_id, 'llm_processing_time')
            if llm_time and self.streaming_session and self.user:
                performance_monitor.record_metric(
                    request_id, self.streaming_session, self.user,
                    'llm_processing_time', llm_time
                )
            
            # Send final chunk with is_final=True
            await self.send(text_data=json.dumps({
                'type': 'llm_response_chunk',
                'content': '',  # Empty content for final chunk
                'chunk_id': request_id,
                'is_final': True,
                'timestamp': timezone.now().isoformat()
            }))
            
            # Stop typing indicator
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'stopped',
                'conversation_id': str(conversation.id),
                'timestamp': timezone.now().isoformat()
            }))
            
            # Remove typing indicator messages
            await self.remove_typing_indicators(conversation, 'assistant')
            
            # Save complete AI response to database
            ai_message = await self.save_message(
                conversation=conversation,
                sender_type='assistant',
                content=full_response_content,
                message_type='text',
                model_used='agent_coordinator',
                tokens_used=len(full_response_content.split()) * 1.3,  # Rough estimate
                processing_time=llm_time if llm_time else None
            )
            
            # Cache response for future use
            if self.user and full_response_content:
                cache_service_instance.cache_query_response(
                    self.user.id, user_input, full_response_content
                )
            
            # End total response timer
            total_time = performance_monitor.end_timer(request_id, 'total_response_time')
            if total_time and self.streaming_session and self.user:
                performance_monitor.record_metric(
                    request_id, self.streaming_session, self.user,
                    'total_response_time', total_time
                )
            
            # Update relationship metrics
            await self.update_relationship_metrics(conversation, 'conversation')
            
        except Exception as e:
            logger.error(f"Error generating streaming AI response: {e}")
            
            # Stop typing indicator on error
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'stopped'
            }))
            
            # Send error to client
            await self.send_error(f"Failed to generate AI response: {str(e)}")
            
            # Record error
            if self.streaming_session and self.user:
                performance_monitor.record_error(
                    request_id, 'ai_generation_error', str(e)
                )
                id = str(uuid.uuid4())
            
        # Start LLM timer
        performance_monitor.start_timer(request_id, 'llm_first_token_time')
        performance_monitor.start_timer(request_id, 'llm_total_time')
        
        try:
            # Send typing indicator
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'started',
                'request_id': request_id
            }))
            
            # End memory retrieval timer if it was started
            memory_time = performance_monitor.end_timer(request_id, 'memory_retrieval_time')
            if memory_time and self.streaming_session and self.user:
                performance_monitor.record_metric(
                    request_id, self.streaming_session, self.user,
                    'memory_retrieval_time', memory_time
                )
            
            # Generate streaming response using chat service with emotion context
            response_chunks = []
            chunk_id = str(uuid.uuid4())
            first_token_received = False
            
            async for response_chunk in self.chat_service.generate_streaming_response(
                conversation_id=str(conversation.id),
                user_input=user_input,
                emotion_context=emotion_context
            ):
                # Record first token time
                if not first_token_received:
                    first_token_received = True
                    llm_first_token_time = performance_monitor.end_timer(request_id, 'llm_first_token_time')
                    if llm_first_token_time and self.streaming_session and self.user:
                        performance_monitor.record_metric(
                            request_id, self.streaming_session, self.user,
                            'llm_first_token_time', llm_first_token_time
                        )
                
                # Send LLM response chunk
                await self.send(text_data=json.dumps({
                    'type': 'llm_response_chunk',
                    'content': response_chunk.get('text', ''),
                    'chunk_id': chunk_id,
                    'is_final': response_chunk.get('is_final', False),
                    'timestamp': time.time() * 1000,
                    'request_id': request_id
                }))
                
                response_chunks.append(response_chunk.get('text', ''))
                
                # If we have complete response, start TTS streaming
                if response_chunk.get('is_final', False):
                    # End LLM total time timer
                    llm_total_time = performance_monitor.end_timer(request_id, 'llm_total_time')
                    if llm_total_time and self.streaming_session and self.user:
                        performance_monitor.record_metric(
                            request_id, self.streaming_session, self.user,
                            'llm_total_time', llm_total_time
                        )
                    
                    full_response = ''.join(response_chunks)
                    
                    # Save AI message
                    ai_message = await self.save_message(
                        conversation=conversation,
                        sender_type='assistant',
                        content=full_response,
                        message_type='text',
                        model_used=response_chunk.get('model_used'),
                        tokens_used=response_chunk.get('tokens_used'),
                        processing_time=response_chunk.get('processing_time')
                    )
                    
                    # Record API usage
                    if self.user and response_chunk.get('tokens_used'):
                        performance_monitor.record_api_usage(
                            request_id, 'groq', {'tokens': response_chunk.get('tokens_used', 0)}
                        )
                    
                    # Cache the response for future use if it's not too long
                    if self.user and len(user_input) < 200 and len(full_response) < 1000:
                        cache_service_instance.cache_query_response(
                            self.user.id, user_input, full_response
                        )
                    
                    # Start TTS streaming
                    await self.stream_tts_response(full_response, emotion_context, request_id)
            
            # Stop typing indicator
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'stopped',
                'request_id': request_id
            }))
            
        except Exception as e:
            logger.error(f"Error generating streaming AI response: {e}")
            await self.send_error("Failed to generate response")
            
            # Record error
            if hasattr(self, 'streaming_session') and self.streaming_session and self.user:
                performance_monitor.record_error(
                    request_id, 'llm_generation_error', str(e)
                )
            
            # Stop typing indicator on error
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'stopped',
                'request_id': request_id
            }))
    
    async def stream_tts_response(self, text, emotion_context=None, request_id=None):
        """Stream TTS audio chunks to client."""
        if not request_id:
            request_id = str(uuid.uuid4())
            
        # Start TTS timer
        performance_monitor.start_timer(request_id, 'tts_first_chunk_time')
        performance_monitor.start_timer(request_id, 'tts_total_time')
        
        try:
            # Use chat service to generate streaming TTS
            first_chunk_sent = False
            
            async for audio_chunk in self.chat_service.generate_streaming_tts(
                text=text,
                emotion_context=emotion_context
            ):
                # Record first chunk time
                if not first_chunk_sent:
                    first_chunk_sent = True
                    tts_first_chunk_time = performance_monitor.end_timer(request_id, 'tts_first_chunk_time')
                    if tts_first_chunk_time and self.streaming_session and self.user:
                        performance_monitor.record_metric(
                            request_id, self.streaming_session, self.user,
                            'tts_first_chunk_time', tts_first_chunk_time
                        )
                
                # Send audio chunk
                await self.send(text_data=json.dumps({
                    'type': 'audio_chunk',
                    'data': base64.b64encode(audio_chunk.get('audio_data', b'')).decode('utf-8'),
                    'chunk_id': audio_chunk.get('chunk_id', str(uuid.uuid4())),
                    'is_final': audio_chunk.get('is_final', False),
                    'voice_settings': audio_chunk.get('voice_settings', {}),
                    'timestamp': time.time() * 1000,
                    'request_id': request_id
                }))
                
                # If this is the final chunk, record total TTS time and total response time
                if audio_chunk.get('is_final', False):
                    # End TTS total time timer
                    tts_total_time = performance_monitor.end_timer(request_id, 'tts_total_time')
                    if tts_total_time and self.streaming_session and self.user:
                        performance_monitor.record_metric(
                            request_id, self.streaming_session, self.user,
                            'tts_total_time', tts_total_time
                        )
                    
                    # End total response time timer
                    total_response_time = performance_monitor.end_timer(request_id, 'total_response_time')
                    if total_response_time and self.streaming_session and self.user:
                        performance_monitor.record_metric(
                            request_id, self.streaming_session, self.user,
                            'total_response_time', total_response_time
                        )
                        
                        # Record API usage for Hume
                        performance_monitor.record_api_usage(
                            request_id, 'hume', {'calls': 1}
                        )
                
        except Exception as e:
            logger.error(f"Error streaming TTS response: {e}")
            
            # Record error
            if hasattr(self, 'streaming_session') and self.streaming_session and self.user:
                performance_monitor.record_error(
                    request_id, 'tts_streaming_error', str(e)
                )
                
            # End total response time timer even on error
            total_response_time = performance_monitor.end_timer(request_id, 'total_response_time')
            if total_response_time and self.streaming_session and self.user:
                performance_monitor.record_metric(
                    request_id, self.streaming_session, self.user,
                    'total_response_time', total_response_time
                )
                
            # Continue without TTS if it fails
    
    async def generate_ai_response(self, conversation, user_input):
        """Legacy AI response generation (for backward compatibility)."""
        try:
            # Send typing indicator
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'started'
            }))
            
            # Generate response using chat service
            ai_response = await self.chat_service.generate_response(
                conversation_id=str(conversation.id),
                user_input=user_input
            )
            
            # Save AI message
            ai_message = await self.save_message(
                conversation=conversation,
                sender_type='assistant',
                content=ai_response['text'],
                message_type='text',
                model_used=ai_response.get('model_used'),
                tokens_used=ai_response.get('tokens_used'),
                processing_time=ai_response.get('processing_time')
            )
            
            # Send AI response
            await self.send(text_data=json.dumps({
                'type': 'ai_message',
                'message_id': str(ai_message.id),
                'content': ai_response['text'],
                'timestamp': ai_message.created_at.isoformat(),
                'has_audio': ai_response.get('has_audio', False),
                'audio_url': ai_response.get('audio_url')
            }))
            
            # Stop typing indicator
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'stopped'
            }))
            
        except Exception as e:
            logger.error(f"Error generating AI response: {e}")
            await self.send_error("Failed to generate response")
            
            # Stop typing indicator on error
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'stopped'
            }))
    
    async def send_error(self, message, error_code=None, details=None):
        """Send enhanced error message to client."""
        error_data = {
            'type': 'error',
            'message': message,
            'timestamp': time.time() * 1000
        }
        
        if error_code:
            error_data['error_code'] = error_code
        
        if details:
            error_data['details'] = details
        
        await self.send(text_data=json.dumps(error_data))
    
    def validate_message_data(self, data: Dict[str, Any], message_type: str) -> tuple[bool, str]:
        """
        Validate incoming message data based on message type.
        
        Args:
            data: Message data to validate
            message_type: Type of message being validated
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            if message_type == 'text_message':
                content = data.get('content', '').strip()
                if not content:
                    return False, "Message content cannot be empty"
                if len(content) > 10000:  # 10KB limit
                    return False, "Message content too long (max 10,000 characters)"
                
            elif message_type == 'audio_chunk':
                audio_data = data.get('data')
                if not audio_data:
                    return False, "Audio data is required"
                
                # Validate base64 format
                try:
                    base64.b64decode(audio_data)
                except Exception:
                    return False, "Invalid base64 audio data"
                
                # Check chunk size (max 1MB)
                if len(audio_data) > 1024 * 1024:
                    return False, "Audio chunk too large (max 1MB)"
                
                chunk_id = data.get('chunk_id')
                if not chunk_id:
                    return False, "Chunk ID is required"
                
            elif message_type == 'emotion_feedback':
                emotion_scores = data.get('emotion_scores')
                if not isinstance(emotion_scores, dict):
                    return False, "Emotion scores must be a dictionary"
                
                user_validation = data.get('user_validation')
                if not isinstance(user_validation, bool):
                    return False, "User validation must be a boolean"
                
            elif message_type == 'conversation_switch':
                conversation_id = data.get('conversation_id')
                if conversation_id and not isinstance(conversation_id, str):
                    return False, "Conversation ID must be a string"
            
            return True, ""
            
        except Exception as e:
            return False, f"Validation error: {str(e)}"
    
    async def handle_message_with_validation(self, data: Dict[str, Any], message_type: str):
        """Handle message with validation and error handling."""
        # Validate message data
        is_valid, error_message = self.validate_message_data(data, message_type)
        if not is_valid:
            await self.send_error(error_message, error_code='VALIDATION_ERROR')
            return False
        
        # Check rate limiting
        if not await self.check_rate_limit(message_type):
            await self.send_error(
                "Rate limit exceeded. Please slow down.",
                error_code='RATE_LIMIT_EXCEEDED'
            )
            return False
        
        return True
    
    async def check_rate_limit(self, message_type: str) -> bool:
        """
        Check if user is within rate limits for message type.
        
        Args:
            message_type: Type of message to check
            
        Returns:
            True if within limits, False if rate limited
        """
        # Simple in-memory rate limiting (in production, use Redis)
        current_time = time.time()
        
        if not hasattr(self, '_rate_limit_data'):
            self._rate_limit_data = {}
        
        user_id = str(self.user.id)
        if user_id not in self._rate_limit_data:
            self._rate_limit_data[user_id] = {}
        
        user_limits = self._rate_limit_data[user_id]
        
        # Define rate limits (requests per minute)
        limits = {
            'text_message': 60,      # 60 text messages per minute
            'audio_chunk': 600,      # 600 audio chunks per minute (10 per second)
            'emotion_feedback': 30,  # 30 emotion feedback per minute
        }
        
        limit = limits.get(message_type, 60)
        window = 60  # 1 minute window
        
        # Clean old entries
        if message_type in user_limits:
            user_limits[message_type] = [
                timestamp for timestamp in user_limits[message_type]
                if current_time - timestamp < window
            ]
        else:
            user_limits[message_type] = []
        
        # Check if within limit
        if len(user_limits[message_type]) >= limit:
            return False
        
        # Add current request
        user_limits[message_type].append(current_time)
        return True
    
    # Database operations (async wrappers)
    
    @database_sync_to_async
    def create_chat_session(self):
        """Create a new chat session."""
        return ChatSession.objects.create(
            user=self.user,
            channel_name=self.channel_name,
            ip_address=self.scope.get('client', ['', ''])[0],
            user_agent=dict(self.scope.get('headers', {})).get(b'user-agent', b'').decode()
        )
    
    @database_sync_to_async
    def disconnect_chat_session(self):
        """Mark chat session as disconnected."""
        self.chat_session.disconnect()
    
    @database_sync_to_async
    def get_or_create_conversation(self, conversation_id=None):
        """Get existing conversation or create new one."""
        if conversation_id:
            try:
                conversation = Conversation.objects.get(
                    id=conversation_id,
                    user=self.user
                )
                return conversation
            except Conversation.DoesNotExist:
                pass
        
        # Create new conversation
        conversation = Conversation.objects.create(
            user=self.user,
            title=f"Chat {timezone.now().strftime('%Y-%m-%d %H:%M')}"
        )
        return conversation
    
    @database_sync_to_async
    def get_conversation(self, conversation_id):
        """Get conversation by ID."""
        try:
            return Conversation.objects.get(
                id=conversation_id,
                user=self.user
            )
        except Conversation.DoesNotExist:
            return None
    
    @database_sync_to_async
    def update_chat_session_conversation(self, conversation):
        """Update chat session with current conversation."""
        self.chat_session.conversation = conversation
        self.chat_session.save(update_fields=['conversation'])
    
    @database_sync_to_async
    def save_message(self, conversation, sender_type, content, message_type='text', **kwargs):
        """Save message to database."""
        return Message.objects.create(
            conversation=conversation,
            sender_type=sender_type,
            content=content,
            message_type=message_type,
            **kwargs
        )
    
    @database_sync_to_async
    def create_streaming_session(self):
        """Create a new streaming session."""
        return StreamingSession.objects.create(
            user=self.user,
            session_id=self.session_id,
            websocket_id=self.channel_name,
            is_active=True,
            performance_metrics={}
        )
    
    @database_sync_to_async
    def disconnect_streaming_session(self):
        """Mark streaming session as disconnected."""
        if self.streaming_session:
            self.streaming_session.end_session(reason='user_disconnect')
    
    @database_sync_to_async
    def update_streaming_session_activity(self):
        """Update streaming session last activity."""
        if self.streaming_session:
            self.streaming_session.last_activity = timezone.now()
            self.streaming_session.save(update_fields=['last_activity'])
    
    @database_sync_to_async
    def store_emotion_context(self, emotion_analysis, chunk_id):
        """Store emotion context from audio analysis."""
        try:
            return EmotionContext.objects.create(
                user=self.user,
                session_id=self.session_id,
                audio_emotions={
                    'emotions': [
                        {'name': emotion.name, 'score': emotion.score}
                        for emotion in emotion_analysis.emotions
                    ],
                    'confidence_score': emotion_analysis.confidence_score,
                    'chunk_id': chunk_id
                },
                text_emotions={},  # Will be filled by text analysis
                context_emotions={},  # Will be filled by context builder
                confidence_score=emotion_analysis.confidence_score
            )
        except Exception as e:
            logger.error(f"Error storing emotion context: {e}")
            return None
    
    @database_sync_to_async
    def store_emotion_feedback(self, emotion_scores, user_validation, chunk_id):
        """Store user feedback on emotion detection."""
        try:
            # Find the corresponding emotion context
            emotion_context = EmotionContext.objects.filter(
                user=self.user,
                session_id=self.session_id,
                audio_emotions__chunk_id=chunk_id
            ).first()
            
            if emotion_context:
                # Update with user feedback
                emotion_context.audio_emotions['user_feedback'] = {
                    'emotion_scores': emotion_scores,
                    'validation': user_validation,
                    'timestamp': time.time() * 1000
                }
                emotion_context.save(update_fields=['audio_emotions'])
                return emotion_context
            
        except Exception as e:
            logger.error(f"Error storing emotion feedback: {e}")
            return None
    
    async def reset_session_state(self):
        """Reset session state for reconnection."""
        try:
            self.is_processing_audio = False
            self.current_audio_chunks = []
            
            # Update streaming session
            if self.streaming_session:
                await self.update_streaming_session_activity()
            
            logger.info(f"Session state reset for user: {self.user.email}")
            
        except Exception as e:
            logger.error(f"Error resetting session state: {e}")
    
    # Connection Management Methods
    
    async def heartbeat_monitor(self):
        """Monitor connection health with periodic heartbeats."""
        try:
            while self.connection_state in ['active', 'connecting']:
                await asyncio.sleep(self.heartbeat_interval)
                
                if self.connection_state != 'active':
                    break
                
                current_time = time.time()
                
                # Check if we've received a heartbeat recently
                if self.last_heartbeat and (current_time - self.last_heartbeat) > (self.heartbeat_interval * 2):
                    logger.warning(f"Heartbeat timeout for user {self.user.email}")
                    await self.handle_connection_timeout()
                    break
                
                # Send heartbeat request to client
                await self.send(text_data=json.dumps({
                    'type': 'heartbeat_request',
                    'timestamp': current_time * 1000,
                    'session_id': self.session_id
                }))
                
        except asyncio.CancelledError:
            logger.debug("Heartbeat monitor cancelled")
        except Exception as e:
            logger.error(f"Error in heartbeat monitor: {e}")
    
    async def handle_connection_timeout(self):
        """Handle connection timeout."""
        try:
            logger.info(f"Connection timeout for user {self.user.email}")
            
            # Save current state
            await self.save_session_state()
            
            # Notify client of timeout
            await self.send(text_data=json.dumps({
                'type': 'connection_timeout',
                'message': 'Connection timeout detected',
                'session_id': self.session_id,
                'timestamp': time.time() * 1000
            }))
            
            # Close connection
            await self.close()
            
        except Exception as e:
            logger.error(f"Error handling connection timeout: {e}")
    
    async def save_session_state(self):
        """Save current session state for reconnection."""
        try:
            if not self.streaming_session:
                return
            
            # Save session state to database or cache
            session_state = {
                'conversation_id': str(self.conversation.id) if self.conversation else None,
                'is_processing_audio': self.is_processing_audio,
                'current_audio_chunks': len(self.current_audio_chunks),
                'last_activity': time.time(),
                'connection_state': self.connection_state,
                'queued_messages': len(self.message_queue)
            }
            
            # Update streaming session with state
            self.streaming_session.performance_metrics['session_state'] = session_state
            await database_sync_to_async(lambda: self.streaming_session.save(
                update_fields=['performance_metrics']
            ))()
            
            logger.debug(f"Saved session state for {self.user.email}")
            
        except Exception as e:
            logger.error(f"Error saving session state: {e}")
    
    async def restore_session_state(self, previous_session_id):
        """Restore session state from previous connection."""
        try:
            # Find previous streaming session
            previous_session = await database_sync_to_async(
                StreamingSession.objects.filter(
                    user=self.user,
                    session_id=previous_session_id
                ).first
            )()
            
            if not previous_session:
                return False
            
            # Restore state from previous session
            session_state = previous_session.performance_metrics.get('session_state', {})
            
            if session_state.get('conversation_id'):
                self.conversation = await self.get_conversation(session_state['conversation_id'])
            
            self.is_processing_audio = session_state.get('is_processing_audio', False)
            
            # Reset reconnection attempts on successful restore
            self.reconnection_attempts = 0
            
            logger.info(f"Restored session state for {self.user.email}")
            return True
            
        except Exception as e:
            logger.error(f"Error restoring session state: {e}")
            return False
    
    async def reset_connection_state(self):
        """Reset connection state after reconnection."""
        try:
            self.connection_state = 'active'
            self.last_heartbeat = time.time()
            
            # Process any queued messages
            await self.process_queued_messages()
            
            logger.debug(f"Reset connection state for {self.user.email}")
            
        except Exception as e:
            logger.error(f"Error resetting connection state: {e}")
    
    async def queue_message(self, message_data):
        """Queue message for delivery when connection is restored."""
        try:
            if len(self.message_queue) >= self.max_queue_size:
                # Remove oldest message
                self.message_queue.pop(0)
            
            message_data['queued_at'] = time.time() * 1000
            self.message_queue.append(message_data)
            
            logger.debug(f"Queued message for {self.user.email}")
            
        except Exception as e:
            logger.error(f"Error queuing message: {e}")
    
    async def process_queued_messages(self):
        """Process any queued messages after reconnection."""
        try:
            if not self.message_queue:
                return
            
            logger.info(f"Processing {len(self.message_queue)} queued messages for {self.user.email}")
            
            # Send queued messages
            for message_data in self.message_queue:
                message_data['type'] = 'queued_message'
                message_data['delivered_at'] = time.time() * 1000
                
                await self.send(text_data=json.dumps(message_data))
            
            # Clear queue
            self.message_queue.clear()
            
        except Exception as e:
            logger.error(f"Error processing queued messages: {e}")
    
    async def send_with_queue_fallback(self, message_data):
        """Send message with fallback to queuing if connection is down."""
        try:
            if self.connection_state == 'active':
                await self.send(text_data=json.dumps(message_data))
            else:
                await self.queue_message(message_data)

        except Exception as e:
            logger.error(f"Error sending message, queuing instead: {e}")
            await self.queue_message(message_data)

    async def _save_audio_chunk_for_debugging(self, audio_data: bytes, chunk_id: str, is_final: bool):
        """Save audio chunk to file for debugging purposes."""
        try:
            import os
            import wave
            from django.conf import settings

            # Check if audio debugging is enabled
            if not getattr(settings, 'DEBUG_SAVE_AUDIO_CHUNKS', False):
                return

            # Get debug directory from settings or use default
            debug_dir = getattr(settings, 'DEBUG_AUDIO_PATH', os.path.join(settings.BASE_DIR, 'debug_audio'))
            os.makedirs(debug_dir, exist_ok=True)

            # Create filename with timestamp and chunk info
            timestamp = int(time.time() * 1000)
            user_id = str(self.user.id) if self.user else 'unknown'
            filename = f"audio_{user_id}_{timestamp}_{chunk_id}{'_final' if is_final else ''}.wav"
            filepath = os.path.join(debug_dir, filename)

            # Save raw audio data as WAV file
            # Use 16-bit PCM, mono, 16kHz to match frontend format
            with wave.open(filepath, 'wb') as wav_file:
                wav_file.setnchannels(1)  # Mono
                wav_file.setsampwidth(2)  # 16-bit
                wav_file.setframerate(16000)  # 16kHz to match frontend
                wav_file.writeframes(audio_data)

            logger.info(f"🎤 Audio chunk saved for debugging: {filepath} ({len(audio_data)} bytes)")

            # Also save as raw binary for comparison
            raw_filepath = filepath.replace('.wav', '.raw')
            with open(raw_filepath, 'wb') as raw_file:
                raw_file.write(audio_data)

            # Save metadata file with audio info
            metadata_filepath = filepath.replace('.wav', '.json')
            metadata = {
                'chunk_id': chunk_id,
                'is_final': is_final,
                'user_id': user_id,
                'timestamp': timestamp,
                'audio_size_bytes': len(audio_data),
                'sample_rate': 16000,
                'channels': 1,
                'bit_depth': 16,
                'format': 'PCM',
                'wav_file': filename,
                'raw_file': os.path.basename(raw_filepath)
            }

            import json
            with open(metadata_filepath, 'w') as meta_file:
                json.dump(metadata, meta_file, indent=2)

            logger.info(f"🎤 Audio files saved for debugging:")
            logger.info(f"   WAV: {filepath}")
            logger.info(f"   RAW: {raw_filepath}")
            logger.info(f"   META: {metadata_filepath}")

        except Exception as e:
            logger.error(f"Failed to save audio chunk for debugging: {e}")
            # Don't let debugging failures affect the main flow
    @database_sync_to_async
    def get_most_recent_conversation(self):
        """Get the most recent conversation for the current user."""
        if not self.user:
            return None

        try:
            return Conversation.objects.filter(
                user=self.user,
                is_active=True
            ).order_by('-last_message_at').first()
        except Exception as e:
            logger.error(f"Error getting most recent conversation: {e}")
            return None
    
    @database_sync_to_async
    def remove_typing_indicators(self, conversation, sender_type):
        """Remove typing indicators from the database."""
        try:
            Message.objects.filter(
                conversation=conversation,
                sender_type=sender_type,
                message_type='typing'
            ).delete()
            return True
        except Exception as e:
            logger.error(f"Error removing typing indicators: {e}")
            return False
    
    @database_sync_to_async
    def update_relationship_metrics(self, conversation, interaction_type):
        """Update relationship metrics based on user interaction."""
        try:
            from chat.models_realtime import UserRelationship
            
            # Get or create relationship
            relationship, created = UserRelationship.objects.get_or_create(
                user=self.user
            )
            
            # Update interaction metrics
            relationship.total_interactions += 1
            
            # Update emotional intimacy based on interaction type
            if interaction_type == 'typing_engagement':
                # Small boost for active typing engagement
                intimacy_boost = 0.01
            elif interaction_type == 'voice_message':
                # Larger boost for voice messages (more personal)
                intimacy_boost = 0.02
            elif interaction_type == 'emotion_sharing':
                # Significant boost for sharing emotions
                intimacy_boost = 0.03
            else:
                # Default small boost for any interaction
                intimacy_boost = 0.005
            
            # Apply the boost with a cap at 1.0
            relationship.emotional_intimacy_score = min(
                1.0, 
                relationship.emotional_intimacy_score + intimacy_boost
            )
            
            # Save the relationship
            relationship.save(update_fields=[
                'total_interactions', 
                'emotional_intimacy_score',
                'last_interaction'
            ])
            
            return True
        except Exception as e:
            logger.error(f"Error updating relationship metrics: {e}")
            return False    

    @database_sync_to_async
    def get_conversation_history(self, conversation, limit=10):
        """Get recent conversation history."""
        try:
            messages = Message.objects.filter(
                conversation=conversation,
                is_deleted=False,
                message_type__in=['text', 'audio']  # Only include actual messages
            ).order_by('-created_at')[:limit]
            
            # Convert to list and reverse to get chronological order
            history = []
            for msg in reversed(list(messages)):
                history.append({
                    'sender_type': msg.sender_type,
                    'content': msg.content,
                    'message_type': msg.message_type,
                    'created_at': msg.created_at.isoformat()
                })
            
            return history
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []
    
    async def get_memory_context(self, query):
        """Get memory context for the current user and query using fast cache."""
        try:
            if not self.user or not hasattr(self, 'memory_cache_service'):
                return None

            # Use cached memory search (sub-10ms vs 2400ms!)
            memories = await self.memory_cache_service.search_cached_memories(
                user_id=str(self.user.id),
                session_id=self.session_id,
                query=query,
                k=5
            )

            return {'memories': memories} if memories else None

        except Exception as e:
            logger.error(f"Error getting cached memory context: {e}")
            # Fallback to original method if cache fails
            return await self._fallback_memory_context(query)

    async def _fallback_memory_context(self, query):
        """Fallback memory context retrieval."""
        try:
            from agents.services.memory_manager import get_memory_manager

            memory_manager = get_memory_manager()
            if not memory_manager or not self.user:
                return None

            memories = await memory_manager.async_search_memories(
                query=query,
                user_id=str(self.user.id),
                k=5,
                min_importance=0.3
            )

            return {'memories': memories} if memories else None

        except Exception as e:
            logger.error(f"Error in fallback memory context: {e}")
            return None

    async def generate_fallback_response(self, conversation, user_input, request_id):
        """Generate a fallback response when agent services are not available."""
        try:
            # Get fallback response
            fallback_response = get_fallback_response('api_failure')
            
            # Send typing indicator
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'started',
                'conversation_id': str(conversation.id),
                'timestamp': timezone.now().isoformat()
            }))
            
            # Simulate thinking time
            await asyncio.sleep(0.5)
            
            # Send response in chunks to simulate streaming
            words = fallback_response.split()
            chunks = []
            
            # Send in small chunks to simulate streaming
            current_chunk = []
            for i, word in enumerate(words):
                current_chunk.append(word)
                
                # Send every few words or at the end
                if len(current_chunk) >= 3 or i == len(words) - 1:
                    chunk_text = ' '.join(current_chunk)
                    chunks.append(chunk_text)
                    
                    chunk_message = {
                        'type': 'llm_response_chunk',
                        'content': chunk_text,
                        'chunk_id': request_id,
                        'is_final': i == len(words) - 1,
                        'timestamp': timezone.now().isoformat()
                    }
                    await self.send(text_data=json.dumps(chunk_message))
                    
                    current_chunk = []
                    await asyncio.sleep(0.1)  # Small delay between chunks
            
            # Send final chunk with is_final=True
            await self.send(text_data=json.dumps({
                'type': 'llm_response_chunk',
                'content': '',  # Empty content for final chunk
                'chunk_id': request_id,
                'is_final': True,
                'timestamp': timezone.now().isoformat()
            }))
            
            # Stop typing indicator
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'stopped',
                'conversation_id': str(conversation.id),
                'timestamp': timezone.now().isoformat()
            }))
            
            # Remove typing indicator messages
            await self.remove_typing_indicators(conversation, 'assistant')
            
            # Save fallback response to database
            ai_message = await self.save_message(
                conversation=conversation,
                sender_type='assistant',
                content=fallback_response,
                message_type='text',
                model_used='fallback',
                tokens_used=len(fallback_response.split()),
                processing_time=None
            )
            
            # End total response timer
            total_time = performance_monitor.end_timer(request_id, 'total_response_time')
            if total_time and self.streaming_session and self.user:
                performance_monitor.record_metric(
                    request_id, self.streaming_session, self.user,
                    'total_response_time', total_time
                )
            
        except Exception as e:
            logger.error(f"Error generating fallback response: {e}")
            
            # Stop typing indicator on error
            await self.send(text_data=json.dumps({
                'type': 'ai_typing',
                'status': 'stopped'
            }))
            
            # Send error to client
            await self.send_error(f"Failed to generate response: {str(e)}")
            
            # Record error
            if self.streaming_session and self.user:
                performance_monitor.record_error(
                    request_id, 'fallback_error', str(e)
                )
    
    async def check_agents_availability(self):
        """Check if the agents app is available and properly configured."""
        if not AGENTS_AVAILABLE:
            return False
        
        try:
            # Try to get agent coordinator
            agent_coordinator = get_agent_coordinator()
            
            # Perform a health check
            health_status = await agent_coordinator.health_check()
            
            # Check if overall status is healthy
            return health_status.get('overall_status') == 'healthy'
        except Exception as e:
            logger.error(f"Error checking agents availability: {e}")
            return False