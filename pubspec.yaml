name: el<PERSON><PERSON>
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI & Animation
  cupertino_icons: ^1.0.8
  flutter_animate: ^4.5.0
  glassmorphism_ui: ^0.3.0
  shimmer: ^3.0.0
  lottie: ^3.1.0
  
  # State Management
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5
  
  # Navigation
  go_router: ^14.2.3
  
  # Authentication
  google_sign_in: ^6.2.1
  sign_in_with_apple: ^6.1.1
  
  # Voice & Audio
  speech_to_text: ^6.6.0
  flutter_tts: ^4.0.2
  audio_waveforms: ^1.1.0
  permission_handler: ^11.3.1
  vad: ^0.0.6
  # whisper_ggml: ^1.1.1  # Temporarily disabled due to ffmpeg dependency issue

  # HTTP & WebSocket
  dio: ^5.4.3+1
  socket_io_client: ^2.0.3+1
  web_socket_channel: ^3.0.3

  # Security & Authentication
  flutter_secure_storage: ^9.2.2

  # Audio Processing
  audioplayers: ^6.0.0
  
  # Storage & Cache
  shared_preferences: ^2.2.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Unity Integration
  flutter_unity_widget: ^2022.2.1
  
  # Utils
  uuid: ^4.4.0
  intl: ^0.19.0
  logger: ^2.4.0
  equatable: ^2.0.5
  image_picker: ^1.1.2
  crypto: ^3.0.3
  just_audio: ^0.10.4

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting & Code Generation
  flutter_lints: ^5.0.0
  riverpod_generator: ^2.4.0
  build_runner: ^2.4.12
  json_annotation: ^4.9.0
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/animations/
    - assets/sounds/
    - assets/environments/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
