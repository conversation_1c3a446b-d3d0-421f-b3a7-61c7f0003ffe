#!/usr/bin/env ruby

# Script to help fix Unity integration issues in iOS
# Run this script from the ios directory: ruby fix_unity_integration.rb

require 'xcodeproj'

def fix_unity_integration
  puts "🔧 Fixing Unity integration for iOS..."
  
  # Open the workspace
  workspace_path = 'Runner.xcworkspace'
  project_path = 'Runner.xcodeproj'
  
  unless File.exist?(project_path)
    puts "❌ Runner.xcodeproj not found!"
    return false
  end
  
  project = Xcodeproj::Project.open(project_path)
  
  # Find the Runner target
  runner_target = project.targets.find { |target| target.name == 'Runner' }
  
  unless runner_target
    puts "❌ Runner target not found!"
    return false
  end
  
  puts "✅ Found Runner target"
  
  # Add UnityFramework to frameworks
  unity_framework_ref = project.frameworks_group.files.find { |file| file.path == 'UnityFramework.framework' }
  
  unless unity_framework_ref
    puts "⚠️  Adding UnityFramework.framework reference..."
    unity_framework_ref = project.frameworks_group.new_reference('UnityFramework.framework')
    unity_framework_ref.source_tree = 'BUILT_PRODUCTS_DIR'
  end
  
  # Add to target
  unless runner_target.frameworks_build_phase.files.find { |file| file.file_ref == unity_framework_ref }
    puts "⚠️  Adding UnityFramework to Runner target..."
    runner_target.frameworks_build_phase.add_file_reference(unity_framework_ref)
  end
  
  # Update build settings
  puts "🔧 Updating build settings..."
  
  runner_target.build_configurations.each do |config|
    # Add framework search paths
    framework_search_paths = config.build_settings['FRAMEWORK_SEARCH_PATHS'] || []
    framework_search_paths = [framework_search_paths] unless framework_search_paths.is_a?(Array)
    
    unity_framework_path = "$(PROJECT_DIR)/UnityLibrary"
    unless framework_search_paths.include?(unity_framework_path)
      framework_search_paths << unity_framework_path
      config.build_settings['FRAMEWORK_SEARCH_PATHS'] = framework_search_paths
    end
    
    # Add other linker flags
    other_linker_flags = config.build_settings['OTHER_LDFLAGS'] || []
    other_linker_flags = [other_linker_flags] unless other_linker_flags.is_a?(Array)
    
    unity_flags = ['-framework', 'UnityFramework']
    unity_flags.each do |flag|
      unless other_linker_flags.include?(flag)
        other_linker_flags << flag
      end
    end
    config.build_settings['OTHER_LDFLAGS'] = other_linker_flags
    
    # Ensure proper architecture settings
    config.build_settings['ONLY_ACTIVE_ARCH'] = 'NO'
    config.build_settings['VALID_ARCHS'] = 'arm64'
  end
  
  # Save the project
  project.save
  puts "✅ Unity integration fixes applied!"
  puts "📝 Please also manually verify in Xcode that:"
  puts "   1. UnityFramework.framework is in 'Frameworks, Libraries, and Embedded Content'"
  puts "   2. It's set to 'Embed & Sign'"
  puts "   3. Unity-iPhone.xcodeproj is added to your workspace"
  
  return true
end

# Run the fix
if fix_unity_integration
  puts "🎉 Script completed successfully!"
  puts "💡 Now try building your project again."
else
  puts "❌ Script failed. Please check the errors above."
end