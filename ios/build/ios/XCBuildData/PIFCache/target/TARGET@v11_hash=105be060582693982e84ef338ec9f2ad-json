{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98472d0cae33e9ae0b6bc1f79b35f11277", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f2eba178a617662c3991bab20fcd8b10", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9887dbb5b6aa53c391d48cd209f3b2e62e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9865d95e356bcfa5ad6c3745797588f902", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9887dbb5b6aa53c391d48cd209f3b2e62e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98df229dcc24fcd84a0ab237bb6e9c40b3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f49ead291861b866cfa216c8afa1bfee", "guid": "bfdfe7dc352907fc980b868725387e9896b5250828c7a1a7b193cdb72e2940e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b83c13e2eeeb74a9903a5527af427aa", "guid": "bfdfe7dc352907fc980b868725387e982a2562acfcdad848ae0162e78b44da32", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982790fd5eeb16dbd8589a4f2886c9c5e2", "guid": "bfdfe7dc352907fc980b868725387e989a017ccb97555264008a54d69ef7ebe1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818441d51f50b17f6caa8c89e4e783270", "guid": "bfdfe7dc352907fc980b868725387e98af6ec1b162cec23b69621d059183729e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987024dfadf930c5eb38de77915298767c", "guid": "bfdfe7dc352907fc980b868725387e9807cf26d76e00fe773db58f9373309ebe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f492897e708de64d34933a1314f922e", "guid": "bfdfe7dc352907fc980b868725387e980f69d35fa33cf8e19626c334943ea813"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cbefac13fa50eed514df4b8179017d8", "guid": "bfdfe7dc352907fc980b868725387e98fe3d36e4c0093d6aa4039382916748c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fb56b6493f53cffca5147543fd53da6", "guid": "bfdfe7dc352907fc980b868725387e98841827735967540ab8e427a1b4dec766"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98513e25ae702c3a175336de3eaa942917", "guid": "bfdfe7dc352907fc980b868725387e98a169e16f382bc1a161549badd2147c1d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8f57d268c125ca715a0a5a277ae77fe", "guid": "bfdfe7dc352907fc980b868725387e98441d05f67ca6178bb7d7d9f60d8dce50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989782afa8b17ce0dab1cf9ac164d9c92a", "guid": "bfdfe7dc352907fc980b868725387e98c779a9c43580bd5596fe9c98d538884d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e4e050321b17df3557057e022108c65", "guid": "bfdfe7dc352907fc980b868725387e980a897ee93c2db90675332712acb5a472", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec4801702c4cfc7680ab51f6858bb600", "guid": "bfdfe7dc352907fc980b868725387e989402521abe0283e70e192db86f568f49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840a053cbeecd541c44b85902fd4e2c34", "guid": "bfdfe7dc352907fc980b868725387e984d44e6adef3054ab4dec0986efed15b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828f0ac9940ac5568f87e6c7cefcc24ae", "guid": "bfdfe7dc352907fc980b868725387e980640d4950da807ade311f86b957e6c08"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989047545b037a419fc2eaf1306b96c355", "guid": "bfdfe7dc352907fc980b868725387e9864d3c8f956fa2889f21c54de16e77dc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98215eb6de5be1e808772fecf2052a6549", "guid": "bfdfe7dc352907fc980b868725387e9815f79baa34151dc1f8fd8e42df4d7eaa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e57bbe1bf152b5f4a302e9d577ce0c09", "guid": "bfdfe7dc352907fc980b868725387e982c039099735f0e4249388324d52d5896", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b07f5c9115249f8be0fb890d9251517", "guid": "bfdfe7dc352907fc980b868725387e98d588339cf6562b4b4fe5166a2c469262", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd08256ec6f23e9aaeeb2adbd0511507", "guid": "bfdfe7dc352907fc980b868725387e98e6cd29f8d66c9ba63148785f3d1b5982"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985185204a1647a286a6033c8f425ea3d0", "guid": "bfdfe7dc352907fc980b868725387e98048f999702968e92be327210403f589a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834f760defb451a10d2e1e1ae2bc12f2e", "guid": "bfdfe7dc352907fc980b868725387e982486bd5dbaf4afb4460b93d65b35c30f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802fca0d1c798729e67269b15a6ba67e0", "guid": "bfdfe7dc352907fc980b868725387e98a0b7bd648974c6d9f5f10bbe7b473fa7", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c50362dcf138400092a52e3d01c664d4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980058af2e2280a71efaa9b3436252b92f", "guid": "bfdfe7dc352907fc980b868725387e984523a5b3b9be8ca9be29798c9834825d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dc1568f7a32df27af5653329bbef119", "guid": "bfdfe7dc352907fc980b868725387e983cffc4be313e491aa9b2d545517cbbe8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989770ebdc11e5fb8ac7b2c400303c4cb0", "guid": "bfdfe7dc352907fc980b868725387e983e6a4cb05faec253264d27927eccfec7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5db57dbaf6ddd7740ab11322513d5f7", "guid": "bfdfe7dc352907fc980b868725387e9822acc38b3a8922e165f443a581e3dc9a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98173df63c04416e19079386ae1c0272ed", "guid": "bfdfe7dc352907fc980b868725387e984799f0e1a8275e0fa12bfa2623b3a610"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1c43233826a7d648bc76858a782b89c", "guid": "bfdfe7dc352907fc980b868725387e98ea52819743ae739ac43b6446275dd22b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eae7003eb03e425bfa23456220c03ec8", "guid": "bfdfe7dc352907fc980b868725387e98f1584930b8d8e98f6e409b94c6e78998"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edf38acde4f16971a9e532f1edc590b1", "guid": "bfdfe7dc352907fc980b868725387e9817a255a2d9e9c4348f2d20aae91f8b42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb0a42c0bd220179bbc6171a64717266", "guid": "bfdfe7dc352907fc980b868725387e986a6a0ae87eeb45d9705881f9fa89b2b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985da1c667d66764a2bae9f8671de90ad9", "guid": "bfdfe7dc352907fc980b868725387e9863e274e64e58c5c3c0786412681803ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849036b3095f3d406f69961f435da53fa", "guid": "bfdfe7dc352907fc980b868725387e98e214fedc019e4c201e80789cf5965fd0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7fcc00b1f7c1c038befb31efd2d7c21", "guid": "bfdfe7dc352907fc980b868725387e98791decfb75ecda1ac38994815ebf8241"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7c7f309919f0f9dd932ed5e71688e43", "guid": "bfdfe7dc352907fc980b868725387e988b5c1d2dbd2ed62d33457bce5fc8d56e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d1234f241e54e93d6f63763279d8958", "guid": "bfdfe7dc352907fc980b868725387e98af8624dfd4acc8cd4c911a25964600e0"}], "guid": "bfdfe7dc352907fc980b868725387e989086205510e04cc4693ccf97af08c328", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98296517d02b240e4d67c025b23edb9e0a", "guid": "bfdfe7dc352907fc980b868725387e989c2f095c9fa67ee086b3617c7b95bbc2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afdede430b08af96a0e81afb74f10213", "guid": "bfdfe7dc352907fc980b868725387e98ce5058655673947e1e15ea4841b70acc"}], "guid": "bfdfe7dc352907fc980b868725387e98d7b3128e5f2af78a110a877049c84480", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e986f6c3ea818bca8e465d49a30d24828e3", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e988db6202678b70ccd79085e996afb7eb3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}