{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9805376b14ea4d7033f2400967cde62a4d", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984391e2e449f1f93e4657247539278b8b", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984391e2e449f1f93e4657247539278b8b", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860126ebb5f5f40d9a082cecdeb325075", "guid": "bfdfe7dc352907fc980b868725387e98a27a49b00be0ac981e5db9f07832563d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa03d5f6a0c300c5dd1dc84d0d0c56e1", "guid": "bfdfe7dc352907fc980b868725387e989878394bd4334bc5b75f69e19db62bf2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dea745cfa1b118dddd74d41ae40e88c", "guid": "bfdfe7dc352907fc980b868725387e98e1897e3d8aed17671192779f85b1c248", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98126a1d06847c5d3af350888ae364097c", "guid": "bfdfe7dc352907fc980b868725387e981450c482f5c290824d437888a184ae8b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a86799ca7b09a7c0974e2a49d5545d96", "guid": "bfdfe7dc352907fc980b868725387e98e4fc9878a7a9815c87ead588b617e13f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cd27d86fa3c070c404af28f349903cb", "guid": "bfdfe7dc352907fc980b868725387e98931f24651cd163bcb560cc6bf0293fcb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98622e0f3cde0bc4672e08a8d4788dbf60", "guid": "bfdfe7dc352907fc980b868725387e98d4cfb9e1cbc63fdd6505f85f9a29df9c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f0fd351cbdcf32c7cf11db9cd1ae8647", "guid": "bfdfe7dc352907fc980b868725387e98603cbbc70b8dca9ffe9ae016055178e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b386558e4848db24aabda7a91e89e7e0", "guid": "bfdfe7dc352907fc980b868725387e98de1a9153e9195626d2e18082bddd5583", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861ecbc4ce18ede7842fbc144301635c2", "guid": "bfdfe7dc352907fc980b868725387e984a1d400fa247fcd1999957a8682fc831", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980df3634782429464019b88d2b0f3b4cc", "guid": "bfdfe7dc352907fc980b868725387e98d32709d3a2dbac2c57e7e408179d0866", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff206b771d7c9bdeef82cad68a7c3046", "guid": "bfdfe7dc352907fc980b868725387e9852b51035de7e7268120a09bd61060c70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb0bb6eb8c61f3a22cf3d6a3e6a52aad", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab453ed0ad2c96970b94ab5f5ab6af94", "guid": "bfdfe7dc352907fc980b868725387e9851124daa4e024f512c6435eeca4166ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bb0ea02e253bab3ff9d35e2fbce9c8f", "guid": "bfdfe7dc352907fc980b868725387e98d1ab4894e5f8a787a882b5c8c35da9b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c38f1b7053090df6147f7a5e1efad8dd", "guid": "bfdfe7dc352907fc980b868725387e980ef321af2f9f2c834e49de13f3d55bc9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbd7488a2b6a364741da104f01eb85a2", "guid": "bfdfe7dc352907fc980b868725387e98f0d31a429c82f17b0991aeffa8fa048c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824749aae61c3d84bf4360583f2789599", "guid": "bfdfe7dc352907fc980b868725387e98a42a0c118dedfd60efdbb4d16492c7b8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fa0e0ccbb4c509687aca288c1260358", "guid": "bfdfe7dc352907fc980b868725387e98b3c686ca16e5bce7ed24eabc68c27a7a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98911403d4c32513735e69d220f08fa414", "guid": "bfdfe7dc352907fc980b868725387e9828accb1b5b23f919db719dddafe64428", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987644172a7dd7a3ec5e1c8c936f4bc0d4", "guid": "bfdfe7dc352907fc980b868725387e981a70531bb3ca84e549cc6ba35f299fa1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879285b9499d7f219977f2f55e2b6ea79", "guid": "bfdfe7dc352907fc980b868725387e98d126da608ed212ace70c11a62a0c1637", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845baef09d202692b57bfb86dcf8aec9e", "guid": "bfdfe7dc352907fc980b868725387e98482d9659cbec4dc229fbd14d4097dbb0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986fcba53c1f72129d6e67e8f4d1095711", "guid": "bfdfe7dc352907fc980b868725387e989514a07acecd425069c301ac83629a34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984723a55de87527eeb051af9d10a38473", "guid": "bfdfe7dc352907fc980b868725387e98c5aeecdf68b8bd150764901c66af09a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6a3a63cc00edd1707e49cc5a57e9d2c", "guid": "bfdfe7dc352907fc980b868725387e98845832e3ddc9b9efa82804181189873f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e242f4d17b541ee230e53343410879d3", "guid": "bfdfe7dc352907fc980b868725387e988422c8d41a531ab0e5feda22b2729450"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de820b6c628f35581816d5c2f27f04f6", "guid": "bfdfe7dc352907fc980b868725387e98032b5432a99c409a15c23484d253896f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984264f3fd132bf5630caa3f81a290c956", "guid": "bfdfe7dc352907fc980b868725387e988ec79e04b31e29a98b9058ffe01c7fe6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f9c26d45b3531a2d1c57dc065a616aa", "guid": "bfdfe7dc352907fc980b868725387e980e70a7a6c573b1b7ccd3e1d5ab5d07e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98137f2dd0a6eb5643ca883507cb41d9f0", "guid": "bfdfe7dc352907fc980b868725387e983e056fb9529e4b65a3f33b7e5569c272"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4b576672ac20ef2e9f6c576cac5ad06", "guid": "bfdfe7dc352907fc980b868725387e980341c01be7632895d55fd1dac2b54ca7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815274084b0cf56ef0f72cb8348d74ec6", "guid": "bfdfe7dc352907fc980b868725387e9844b9c045f1529acb68ed027e0a48b614"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d93c530565d2d9f580a658d0ed24fa8", "guid": "bfdfe7dc352907fc980b868725387e98b1b6e64bf6811487ed4c6e1384077226"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884548bbc0020d205088e5f0a663b2b8d", "guid": "bfdfe7dc352907fc980b868725387e981f2b6f47d148085767ead35147c92b0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc9ad86f6cfe6cbc59ddb55e512fc72d", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811ab766ae4e69daedcb10061fbf2396e", "guid": "bfdfe7dc352907fc980b868725387e98ff4558f6e06cbb7fbd15dd015ca18854"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982011aac88b5bd92b9be6876b84700c23", "guid": "bfdfe7dc352907fc980b868725387e983caf79b54a68f27872c153b09b7289cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb8bb97ef565f746b0067e7de06f4a8f", "guid": "bfdfe7dc352907fc980b868725387e983550715f5dc47c6e779aeb12290a7c48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7576077d88a7435c7d60ebd8d74bfc1", "guid": "bfdfe7dc352907fc980b868725387e985800f051739a623bb35f7cc22cbdb0d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f96b85748220b15d9dde94edda37d3e9", "guid": "bfdfe7dc352907fc980b868725387e98f2203b5d78f4ac52bb8f2618d0202e75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ac3bfaf7c3632ae8208a1d92b6d615f", "guid": "bfdfe7dc352907fc980b868725387e983616275298339a71f8ce5405edcae646"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a05e1bb1207dbc2e265a1191d94ea26", "guid": "bfdfe7dc352907fc980b868725387e9859b2f7d951b31f73b36500eee5613569"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f992d49ca7307e2bacd7316816b86cf2", "guid": "bfdfe7dc352907fc980b868725387e9800fc7c6d98b1d598e7f0946391c24f5b"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98296517d02b240e4d67c025b23edb9e0a", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}