{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9850ef3623104befe878f85a1c7080bc87", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d6da72224a0002e2c8d4ad6a4140a101", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982ed6eb811d3910de06ef5bec8f50bca9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984cab3693a69eb1c553c791758183685f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982ed6eb811d3910de06ef5bec8f50bca9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f480631d57f584ae82569893436b3d19", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f6def484f860ac7b6978b354817c4980", "guid": "bfdfe7dc352907fc980b868725387e98faa2d77c0ef7eb87f7f8421e51c1b40a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a1e3dac75ef5081293a3cce12f493338", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9889de67d7736a115d9b08b03664b8a09a", "guid": "bfdfe7dc352907fc980b868725387e98a9fcf3c1ad4b496202fd1bd0770e2658"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f7d8edae0df3b1cbddab4d68e38a33c", "guid": "bfdfe7dc352907fc980b868725387e98f8ef29c5478477821b26216ea91399e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878a285724948c9a2f3abb38c43feb7aa", "guid": "bfdfe7dc352907fc980b868725387e98bbcc19cc8f7053306c671cb9b90b752f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b4ed3ada4d08fb32b833e30486dc059", "guid": "bfdfe7dc352907fc980b868725387e98169fe9585dc94b5341ce3c4f8797874e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823914bc9caf2dfa20e65c96263a1a61a", "guid": "bfdfe7dc352907fc980b868725387e98c68b98745d03970160756172e7affeec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a06f32c8969d1244fdc6344606c0068", "guid": "bfdfe7dc352907fc980b868725387e98ca71ab9b5fee7062f4e5031d9f31b8c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c176adb35592e85b9ab6beb4a7237e34", "guid": "bfdfe7dc352907fc980b868725387e982873f3cfafb9c292ea820f7513ea418b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813e2de020fa0f6fdb13f907d795494c8", "guid": "bfdfe7dc352907fc980b868725387e98ebe841ba0c1b1067a6e7d099c37622d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d68cd47cd18f95f792de0a8c4a20276", "guid": "bfdfe7dc352907fc980b868725387e9895c7b4ef97e75dbe371504d11fe40041"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807355dcdb40edfd857a4deba3b518407", "guid": "bfdfe7dc352907fc980b868725387e98cedf9282d9819e11497cb07622e65d32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983955ea36dde31cd52ca333a72326bf28", "guid": "bfdfe7dc352907fc980b868725387e98bfd860dbd983106d1184eebd4a8d94a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98964f5513cdc4490baf556a45533567aa", "guid": "bfdfe7dc352907fc980b868725387e98c6f911e71a88cf2e4e1592bb0eb18c7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98314dd7f6688875e2260a64684c689281", "guid": "bfdfe7dc352907fc980b868725387e98a0702950f587d16760ae3ef9703b475e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb4ce4ae35b1960803e831d77dcc2de9", "guid": "bfdfe7dc352907fc980b868725387e982da7cd0e398f6f8d78718a0c057488d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cf9c940f62ea84898f542014886673d", "guid": "bfdfe7dc352907fc980b868725387e98b02e808090ed34d1a6200ad70702cdb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdc788be27b96f18acef94acaabc6201", "guid": "bfdfe7dc352907fc980b868725387e9810d3575320ae0bb4b7cddc68d940b515"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987da08316920aa35d8ec05742a092971e", "guid": "bfdfe7dc352907fc980b868725387e98e2e982bdfed1982ffab8a2a18a7c4fdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98239b01ed0a9ef19f0a45a44e9dec5e5b", "guid": "bfdfe7dc352907fc980b868725387e98ac424b6c6b355064adfd2ba21127c264"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9f6091fa43bae3eebb3ab7406dc4ad0", "guid": "bfdfe7dc352907fc980b868725387e98d1dd56ffaec41174eaf797446a808e67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed3d7ff6ae6d66ae6ddc444d47509a0e", "guid": "bfdfe7dc352907fc980b868725387e98ea245f056f270a325b262f71602bfdd6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988431fa3e4cca08316cdd682d9d8a577a", "guid": "bfdfe7dc352907fc980b868725387e98e6be90f1eb0ac18c764b3450bff25c39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d7b56f5fa32741bf73ef13b6a94d566", "guid": "bfdfe7dc352907fc980b868725387e98139c99da96f9baa95b6f2139cae8b843"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b279211517eb8184f594c48462a97a1", "guid": "bfdfe7dc352907fc980b868725387e98c27bbd355169b3263b514504ed8a353f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abd70de1cf8dff78d214983fd4c8c86f", "guid": "bfdfe7dc352907fc980b868725387e98de8c2e61a15352253e32a553161dedb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98998e4ab05e7781ee35a3a968edb7d5f6", "guid": "bfdfe7dc352907fc980b868725387e98536d0d3976bda4dc2b021bcb3bae121d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a244af008b48226f44300f220b4e3360", "guid": "bfdfe7dc352907fc980b868725387e98815f44c177c3a5d8767aeaf413828141"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebe598f68c0ee92b1b08c6624b01eb63", "guid": "bfdfe7dc352907fc980b868725387e984c502637ff5becd89be5509a936fd7ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849e23a5f063d8bda7027b33f143b99d7", "guid": "bfdfe7dc352907fc980b868725387e98ad70628b077935a6c419c6b75feef182"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b37a91a4ea25703ee40a432b4b32aac5", "guid": "bfdfe7dc352907fc980b868725387e986145ae72b418bd8b27764ade58f57226"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb77acf30a40dfe81acffa0043929b05", "guid": "bfdfe7dc352907fc980b868725387e98f6fb2e51d451d9b6b07e8ae51727cd6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3de7655da7defeaffaef77e8188317c", "guid": "bfdfe7dc352907fc980b868725387e988607a965f37f9e559a6f81b0bd8396db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ecf6fff23f34983888a8d918251d3e0", "guid": "bfdfe7dc352907fc980b868725387e9800b0c879cf731cc66222397ee9ecb2b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c85eb112a79a1394a1a98802dd054ac6", "guid": "bfdfe7dc352907fc980b868725387e98af1586c8071a079da7195d2ff7ed4c6e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abfe2a89b21562b5ec1bad3c4c987e73", "guid": "bfdfe7dc352907fc980b868725387e98919f8a532384e10580c44442178ef92d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812495206c73401c6520c267adfd3f614", "guid": "bfdfe7dc352907fc980b868725387e9863e9a176bd7e1ea66085d75e3f223ded"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98981da63a2cac1a601197e8f53317ab49", "guid": "bfdfe7dc352907fc980b868725387e98a259ba9dec8a0a7c4000e1e5ab6c2d67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5d05615218c49f2a7b895620b22dcff", "guid": "bfdfe7dc352907fc980b868725387e981ffd86e51b2e47a1f8541bdd34964bc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6a3cb971843da0e46fdc6d89653510f", "guid": "bfdfe7dc352907fc980b868725387e9858927e92d8eaaab97ff5cb8198e90ebd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc303ac01df951a1ee48a993cd764789", "guid": "bfdfe7dc352907fc980b868725387e98d259083229b4eed28de4a364a94222ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98672b68023224b61c09ed8dec8aa94c2b", "guid": "bfdfe7dc352907fc980b868725387e98118d4d39a207c9b305f78273b6ea8bd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810d6e120168f09872140fa84093cb92f", "guid": "bfdfe7dc352907fc980b868725387e98c65a7663ddb1be4c47c6c66f4b547352"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eab25b3a7ea8c69977a9933839344652", "guid": "bfdfe7dc352907fc980b868725387e983aa97d187f14c98a28de079c1fad7009"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817f0edf8120bf247477e2699122b37b2", "guid": "bfdfe7dc352907fc980b868725387e98e1b9bd2576b6814da7774f199fed4aaa"}], "guid": "bfdfe7dc352907fc980b868725387e980055fbf3a11bc15129d28ebbe5cc1480", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98296517d02b240e4d67c025b23edb9e0a", "guid": "bfdfe7dc352907fc980b868725387e987ead9e15fc09a570554fa963eed1137d"}], "guid": "bfdfe7dc352907fc980b868725387e98da9cd099e7190758cfc42156c0f4e548", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b8defae1b39d33d1c1b6a7ec3e8cf51c", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e987fd8694c1d36b88c99f78feb0d04dd25", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}