{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9836efbe74617740a796d5354876d07142", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98193d32adffba897600b6dae8101af8fa", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c8391027a86d50dc99ea3a418d54ff61", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980f4c3b3c3586fb7fa25b9a6f12a97fee", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c8391027a86d50dc99ea3a418d54ff61", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98167629c0856a9e3818bcbaf02743e5c0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988655eb1983a8b8b49ef5bfe21161a003", "guid": "bfdfe7dc352907fc980b868725387e98d1995d820293c03f751f6b9378241753", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a042d5428ba9d058867a16553feb29f8", "guid": "bfdfe7dc352907fc980b868725387e981a8190c50d6533f473285c56d5880c92", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc8d05455d9e0107aefa57aaf584465f", "guid": "bfdfe7dc352907fc980b868725387e980f54c75d8a74ee39a2a36e7ae36125a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7fa0c45d0ec357c8284998a308507c5", "guid": "bfdfe7dc352907fc980b868725387e987a79fba0100e289e660335caae562106", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4aa5d52455964016a3d601bf4c3f7be", "guid": "bfdfe7dc352907fc980b868725387e98ef9e3da29f51b49d82d4ab55e89c8813", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb372e4fec564e547c164e5d2274dff1", "guid": "bfdfe7dc352907fc980b868725387e9876f93fdd6494dac71234d5a3587a1e4f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817d2de9e6d0966f497eef748640524ff", "guid": "bfdfe7dc352907fc980b868725387e98c86c25b431d324891d4c432425bf1247", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98102bc8f05913b22040891de13f77572a", "guid": "bfdfe7dc352907fc980b868725387e98d0ae20492f45592cbc450c437fb8abd7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6b285b72f7ca6be3b5f9be57b5f32ad", "guid": "bfdfe7dc352907fc980b868725387e9896461eccd08f51737632990df9e0504e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8ba2c991badd668ae7df5c4bdb347cf", "guid": "bfdfe7dc352907fc980b868725387e9803132120ab1500d5547862fcbbd3239e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b56b1c618a757f0cc0d10753a2d7856", "guid": "bfdfe7dc352907fc980b868725387e98f21e08250009eb7bfb1935a48ad36bc2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbcdb25e49436512bc488d3a9f57aeb2", "guid": "bfdfe7dc352907fc980b868725387e98243276a7922c3fc56ec9b8f44a6a5503", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983884cb711c03ac96fa60d7bcaac7479e", "guid": "bfdfe7dc352907fc980b868725387e98e89738b7b0ea5907ec1653774f08cb16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802d22fb7464bfdc1c5ff243a2bb27fd3", "guid": "bfdfe7dc352907fc980b868725387e98086a66364191fb3a94a72cf964f83ed3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98873bfce4eca01fa7ce3ff97f929f0b08", "guid": "bfdfe7dc352907fc980b868725387e98f1bdb30f3a18f808ddb2c7be2c1cd0b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883567b55a029f37e32cb4596d9d6c702", "guid": "bfdfe7dc352907fc980b868725387e989acc1e87904e48e243c5831bd752f82f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab6972bff13cdc78e32f892a8f86e754", "guid": "bfdfe7dc352907fc980b868725387e984d0391e0e6d2e1d08fe148ac50807cda", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894855c0fe298813cc0355af5837168dd", "guid": "bfdfe7dc352907fc980b868725387e98996cc59ea336c4c8d147c5ab86f6029d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856938ea35945b782d8413fc6920c02cc", "guid": "bfdfe7dc352907fc980b868725387e98d9585d0ef714bc6ff83cebc27b8f254e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1ac92115d190a85219e99416cd53fbf", "guid": "bfdfe7dc352907fc980b868725387e98d196945c77d52872a7159f26d03dac05", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd341cdb772826320187144d23eab2d8", "guid": "bfdfe7dc352907fc980b868725387e98cb98917c0fe9628705153a2c99f7b86d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cea627951483e55cb399e2da1bdc704", "guid": "bfdfe7dc352907fc980b868725387e9846a3753f3e56857159da7595429f4c71", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9809272f38f3fbb38e17d5a1600902a901", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988fe5022a903e315eac8f05da7cda5d91", "guid": "bfdfe7dc352907fc980b868725387e984140604082784d9ca9e095867d95bd9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98680adc0ebab4741a01d1102f47511180", "guid": "bfdfe7dc352907fc980b868725387e982ad6857fb8819ce62e32fd0ec61b2be5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988136ab3cedaf13be8b3d38f71fb0387d", "guid": "bfdfe7dc352907fc980b868725387e989ee205090305afeeca44017c334150b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a88fe8f14d5d96a0d810309f4e2a011d", "guid": "bfdfe7dc352907fc980b868725387e98feeb6e10c1951c41bff76ea1297a77cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e31d82adf00b5346d85c7fe262510a7f", "guid": "bfdfe7dc352907fc980b868725387e986ccadbfc250c768fb7f1034052497ecd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98658b155d053d4ed3e52178c506ed14a1", "guid": "bfdfe7dc352907fc980b868725387e98834f4136271fe9be730d9fc2fec95520"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884df5701f90e3eefcc6f98964fafe9a5", "guid": "bfdfe7dc352907fc980b868725387e98d1625cbed67a61f1a895dab4de0dbb65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cc9b9651ab0188883a5e32276ce7dc4", "guid": "bfdfe7dc352907fc980b868725387e98c459e1f1fb0104876df4c707b061f3a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843955722cc93289b454e29e761f539b4", "guid": "bfdfe7dc352907fc980b868725387e98b2fc7c825a73b9af84c5af55b78e5c3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984aeca66e371beb567cc1631f471f289a", "guid": "bfdfe7dc352907fc980b868725387e980caec3473945bbec560f5273d2d8bbb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e79edbee5ebad84ea6c686ef7b903314", "guid": "bfdfe7dc352907fc980b868725387e989a279423ce796888ce19794ed6931002"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc36dbd8d2d8c7d255b88284e8aab533", "guid": "bfdfe7dc352907fc980b868725387e9847eb6b0966836a65a81cc9717bf72809"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce048613ba9b6151bc22f3b68ce255e6", "guid": "bfdfe7dc352907fc980b868725387e98f732c414844e24d85b4d316d0a15f790"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98400d6fa28ab25ac3019aac1f596ebe27", "guid": "bfdfe7dc352907fc980b868725387e981dbe1947e9a7eb4617994b188e71df61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6201864c70070c62677c3aa47db8e0c", "guid": "bfdfe7dc352907fc980b868725387e981b3a729bb9d6ef1eafba6c02dfdf4317"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca54ecfc030f7cf74b32386924aae0b8", "guid": "bfdfe7dc352907fc980b868725387e9895730896d63be224bbdcdc93f76020da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d0c7acb98746b0d684a86d23cb3162a", "guid": "bfdfe7dc352907fc980b868725387e982a19c627bcf873b9e2decd459bc316ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986318ac4ac244676a56fbd8236977b989", "guid": "bfdfe7dc352907fc980b868725387e9838c784031d3dcb448996e1eeebd3d992"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b37af6bb6109ed32b6c69e23098a8bf1", "guid": "bfdfe7dc352907fc980b868725387e981fb9f86def68e8be3985005c3a3e4450"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987073779af36adb2fb969ad94b4a17bf2", "guid": "bfdfe7dc352907fc980b868725387e986d45c33b812cd63554bfbe0134e0b463"}], "guid": "bfdfe7dc352907fc980b868725387e98c7a239e41633ccc7c0d15f92e877887d", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98296517d02b240e4d67c025b23edb9e0a", "guid": "bfdfe7dc352907fc980b868725387e98824a3239ee623048e66969eed6b5fc60"}], "guid": "bfdfe7dc352907fc980b868725387e987fe6e6ed07934c5968a42c2745c101da", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e21659073c5e55b4d0dc14ed14634f60", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98e03a50a7f3c5062bd65e09bca7fa931f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}