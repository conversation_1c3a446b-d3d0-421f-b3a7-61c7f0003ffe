{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98814c7605eebd59c4fb63c42cc8c40025", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981ea2e525524a0a2abe626776405ead48", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9873ac62d770401282a4134727c13b0a4a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9865c0e56750f4b4890c77a642446c166f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9873ac62d770401282a4134727c13b0a4a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bbc5f68b7c2a2d47706f1d66e2dbdfb3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fef08246dfc7b67065654ae951be3b0c", "guid": "bfdfe7dc352907fc980b868725387e9893ff7a3547345cb1bfac4d740935db4c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8a8a0c366bb94a9104200c25e93b9a7", "guid": "bfdfe7dc352907fc980b868725387e98687b55dc719cb90d27c746161fbd4ff9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870cf5357a25906c31794f9932f6d9bc6", "guid": "bfdfe7dc352907fc980b868725387e987a62b9ea037544098ab27d3429f3b2a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986006cfaa38d8de91880d40987c3528e5", "guid": "bfdfe7dc352907fc980b868725387e98ba2ada12b1f3cda88dedf890233289b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888d0dcd2c9c46415bb9c6cefd0e9e611", "guid": "bfdfe7dc352907fc980b868725387e986934bd53b94ce58fe6078f3df263909a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981338ae0806ddd457a51565df57017cb3", "guid": "bfdfe7dc352907fc980b868725387e986b6da94464940304ba6fa9e1923cef7e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891395768180ebf447606b6af09c512bd", "guid": "bfdfe7dc352907fc980b868725387e98a76cbf44193e7610ca77107c53c69536", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f8cd3212da491916a7f345562ac0189", "guid": "bfdfe7dc352907fc980b868725387e98f021df2f058764e16d9060b24c6c9b6a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d5cf12446f5ff1e8655986a2eb4fa7a", "guid": "bfdfe7dc352907fc980b868725387e9831fbbb3626b014f2aa8f34790c42bf90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6fc2741de9c335df6a6420859e240f3", "guid": "bfdfe7dc352907fc980b868725387e986751419aa9b096ac300c0938db6791ba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df9af5f6d37084129247060a71324836", "guid": "bfdfe7dc352907fc980b868725387e98a7e36beb98107b60ff70959d6e20af6d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812c14f15be61347b570e96b5fe451cb2", "guid": "bfdfe7dc352907fc980b868725387e98fa1daeca149502204d57a018c285e509", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a63c1f4377409783027639884b5ed278", "guid": "bfdfe7dc352907fc980b868725387e987b32adbfcebbff079cb355728852f2aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d0be171ca2fe24c62889a9a8a92448a", "guid": "bfdfe7dc352907fc980b868725387e98ef74b28b5de01d6f90f72ee291d037b4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee3755c76978a9f7ff43edab58d873e6", "guid": "bfdfe7dc352907fc980b868725387e98f9a6dedb405efcf3b5b80076a9e05928"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98524ab772c9000ba89393143e4871dcf2", "guid": "bfdfe7dc352907fc980b868725387e98cdc804a55813c229b28f065b1fcc0d3b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852c20048f1e7386c3fa6d78fc59d52ac", "guid": "bfdfe7dc352907fc980b868725387e98a04ac1c8ababdb5c9c5d83ed934c5701", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877998d7bfd5166fae1008ffa58932f3f", "guid": "bfdfe7dc352907fc980b868725387e981f622b923de95c2b1af9d1c283c37b19", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98553fd0c16c915dea30932bc5423b5192", "guid": "bfdfe7dc352907fc980b868725387e9873caaf94d8202f2491659f2f6a1396ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e4b24055290bf135522be398c946bd0", "guid": "bfdfe7dc352907fc980b868725387e98a91175b38037191938731a02cd7b012c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981535bda826c32b191fc8c148e917bb72", "guid": "bfdfe7dc352907fc980b868725387e98ccb4db03c4aafee6b6134265775b72a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985eaedac839ffd3aa85b2dfaaf8072034", "guid": "bfdfe7dc352907fc980b868725387e981d7eec36a69728fb353add3cc8041c8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98deaf4a2e53c19ad7288f9b06cd530d55", "guid": "bfdfe7dc352907fc980b868725387e98cbaf3ef2aa9cbe1292e7dfc7333f35c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c353a1fd5dbd055d8eb550312729bf8", "guid": "bfdfe7dc352907fc980b868725387e989bf9128b25b2f6f2d2f6f230867283af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f447fc0ce3587dc0895e67341c521116", "guid": "bfdfe7dc352907fc980b868725387e98379a015595344b76cc70f49a55b4f8dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3d2e9a6a2c0e7b3ea1914d6ca58a0dc", "guid": "bfdfe7dc352907fc980b868725387e98e4dfd7afbac75d4418cad5bcfc8b67be"}], "guid": "bfdfe7dc352907fc980b868725387e98896e5d31640805303f1abf2aa1fa19cc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989bd2b62c02c0d24aa127c85514c480c2", "guid": "bfdfe7dc352907fc980b868725387e983e34e5f508ac42253fe5346c2c1f1d07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca5f048e352dc0e35763bc9bdcf7144e", "guid": "bfdfe7dc352907fc980b868725387e98f047920777a165a7132f77b024321aa7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e1d88875ea8aa86127999c1ec9c0c67", "guid": "bfdfe7dc352907fc980b868725387e9887c51b7bc0935154a1af40af791ccefa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fff7de40a32f8ee615b461e67df179c5", "guid": "bfdfe7dc352907fc980b868725387e981fa6d0d8097f736be0613b0f0f31030d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815f8ef7a5366dfaf6f78a79929dcb614", "guid": "bfdfe7dc352907fc980b868725387e98fe915285beb8a8d109efbe5f948bdbea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6bf05994a48916806394e59dea28c02", "guid": "bfdfe7dc352907fc980b868725387e9827ad6cedce87cc3d1f07c0807b38e953"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985347ca8fa18593958f735b56aab04c81", "guid": "bfdfe7dc352907fc980b868725387e98b6c11ec85be0e349633beba0a618e8e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f946784962e78cda9c1615ced73d51f4", "guid": "bfdfe7dc352907fc980b868725387e98ef0bf91a2695dc895d68a3f64c0fdbc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecfa846263a02c007d09e8c13a55825e", "guid": "bfdfe7dc352907fc980b868725387e981d5167556b9bc9ecb02df37376ab767f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c474f7f773588e02bf0bb60b804705fc", "guid": "bfdfe7dc352907fc980b868725387e98e7e11462d3b55b699876789a0ce1e9cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbd8443653d1fb2518fac0afa0c6c095", "guid": "bfdfe7dc352907fc980b868725387e986ac390d0027942170a9fb1d4b3493888"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f5ed251c5629f6754b48ebc2dbb6d98", "guid": "bfdfe7dc352907fc980b868725387e98aa770d2bf3edc07b497f13da39978824"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808cc3cce26e2f14d0b9c1ebaf0e3a14f", "guid": "bfdfe7dc352907fc980b868725387e984e4251730b6417e6e37d851d76fc1379"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d0bb4de20bb3f44098c31281d24bb0d", "guid": "bfdfe7dc352907fc980b868725387e982fb15de45a258ea2e6e5ca3a6aed5691"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980071ce52dd87663db6f9e22e241a343a", "guid": "bfdfe7dc352907fc980b868725387e986c2770d065a9b85f302f1c39742d72ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b778fb502ae1d1aa921964788bf0e16", "guid": "bfdfe7dc352907fc980b868725387e98ea80bf9ec8892e71782ee27617c047b4"}], "guid": "bfdfe7dc352907fc980b868725387e982ca9b1177cd3e4a8bbccd4e304e6a432", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98296517d02b240e4d67c025b23edb9e0a", "guid": "bfdfe7dc352907fc980b868725387e988fd96d50abec178c17932128304564ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865cff1691db8b2db0bf426be5157fcef", "guid": "bfdfe7dc352907fc980b868725387e9895795a40344a3fcc6c8e75c382424fee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983247acf3f3e06a311417313a336156f4", "guid": "bfdfe7dc352907fc980b868725387e987da3a501d87f8d4f1957ac6364eb5166"}], "guid": "bfdfe7dc352907fc980b868725387e981aa9e27eb19920d7cda89c71e1611225", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e982427b0de7ad5f7fc68ee24285a099139", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e9842a2933a968c33dfc213f7a2a2733015", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}