{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818202ce112849dc4392b24b9ceadb005", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9859ed185bddda9fac98fb955dbede7af8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9851aa04c81ebdca816b86a2d678ff760c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad3933a0aa7d10d295b0b450348462c6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9851aa04c81ebdca816b86a2d678ff760c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleSignIn/GoogleSignIn.modulemap", "PRODUCT_MODULE_NAME": "GoogleSignIn", "PRODUCT_NAME": "GoogleSignIn", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "4.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9800a96723981eef88cf31d8c7236d90f0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98522cf4240330d81b38356ff5ea68b11c", "guid": "bfdfe7dc352907fc980b868725387e98c4f5c98f0da774a21e280eab7fe057a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873b7a2b9790493732d14d107a7c79ace", "guid": "bfdfe7dc352907fc980b868725387e9839fc6c7c664204e244b373b460a9432f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815065173466a4f79dac6f5cac52ad7c5", "guid": "bfdfe7dc352907fc980b868725387e98427a666d7183895e3731d9813f412e20", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885576fc68664556b575f551209d41571", "guid": "bfdfe7dc352907fc980b868725387e98e6727dcc8f1485e917b74fd4bef0a6ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98171a5f9149a4c33ba88ae6970efb2eb8", "guid": "bfdfe7dc352907fc980b868725387e98df277450d913184a2fa4d6f95009ce33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fed8d05c957e810e97fa27e0b1c86951", "guid": "bfdfe7dc352907fc980b868725387e98eb2550ce05cbbca7cd449a6c4ff75f87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ee6e15efc1af15f0ca3c02465205345", "guid": "bfdfe7dc352907fc980b868725387e98638198299c71ff4f47d5a92a029a6b66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810fc95ccc1d3c358244c45f8040d728f", "guid": "bfdfe7dc352907fc980b868725387e983f2105b19a45887ec0cd99b8f4098a91", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c05263b76a8f4e96386cb0742a44fe65", "guid": "bfdfe7dc352907fc980b868725387e98705be49b2174f3047c35324fab7fba8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efffa7ff10e50004de734c1d79694093", "guid": "bfdfe7dc352907fc980b868725387e98552ebb95fb1f5d91334db314a2781a83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805f7775ec83a4f448640c8f223610cd1", "guid": "bfdfe7dc352907fc980b868725387e9890339403453f254130360d2034e1ca48", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d26a82a515098a6ffcc77031f3928bf4", "guid": "bfdfe7dc352907fc980b868725387e983a5f24f9d5e80f37cf291255e141080c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d518ca8e6233bcf2d4b371e88f99c6d0", "guid": "bfdfe7dc352907fc980b868725387e98100c4dd103ffa81658874e2b2d85d5e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807c746212a1b079d17a24398ff4ab496", "guid": "bfdfe7dc352907fc980b868725387e98617f042988b16886e7cc20ec65a58165"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98742601ce8f14f983bf167cd9ace1dcdf", "guid": "bfdfe7dc352907fc980b868725387e9865b0896451f9d6cf2283a039fbf63cec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859246742af0c42913ddff99c69e0a25b", "guid": "bfdfe7dc352907fc980b868725387e9817b2cd278806279c92d05559de20d64a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860f5ee2a833416883eaf14f315ae7ab4", "guid": "bfdfe7dc352907fc980b868725387e989387f43106c5ec26f7c28c4ccdaf7a83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6beb8ab8c97d6f7df15e888fc50983c", "guid": "bfdfe7dc352907fc980b868725387e98a579ec35ea48344f451f640f5133a97c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc50131d9ca60b9b969763ecd97742a0", "guid": "bfdfe7dc352907fc980b868725387e98247ce5c43b725c1b4d8c5b17a0915e14", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b8aa1472b42fc70cd489582c5774159", "guid": "bfdfe7dc352907fc980b868725387e988f1107f583b9dbd85850564d62dbe5ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5046d0e5bdcb96a56e45cd4efc5e06c", "guid": "bfdfe7dc352907fc980b868725387e987199af7a0850f7694381ed347c4a2089", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e4909702eab13a0b8a159a907f4bc57", "guid": "bfdfe7dc352907fc980b868725387e984c3ddb72446e455ac3be9c0c8232f685"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1e923625b2a4b8efc5ce26953d8c82e", "guid": "bfdfe7dc352907fc980b868725387e988e030a7305ce8e1c8acf4e05f72438fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98392a0fcf592a2bbe1ed936c41d5f89ac", "guid": "bfdfe7dc352907fc980b868725387e98a6603a3db649375f87186c6acf3277e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bdb59eb51f8a7a46120c65f1d1fb2c8", "guid": "bfdfe7dc352907fc980b868725387e984378d226f2fd781453f3a6279bdd9a5f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c376e0ab17746364af865e30f84e97fa", "guid": "bfdfe7dc352907fc980b868725387e983aa61192ae40fcb1e7747889e964445d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848bce6c5643bb63cff47767030badf46", "guid": "bfdfe7dc352907fc980b868725387e98091769d6d3ea2dad8c8511b5d34270ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd8cc8724846e326de1910ce2bef0f01", "guid": "bfdfe7dc352907fc980b868725387e989879c076e1ae8cea441a6b6f2a225e71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad5480ef8fb8310efbc62b2259f44fc1", "guid": "bfdfe7dc352907fc980b868725387e98950714569cbb291ac99cde4b378f4651", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9d17d4678ef8d8a54ebdbcdff1cc6a1", "guid": "bfdfe7dc352907fc980b868725387e984a1d9ef740af0331a31f3d263123ae9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a70ce92bf003551e2b5a7c7ea9785616", "guid": "bfdfe7dc352907fc980b868725387e985e82d340fc8c2378a4a2148d02ddaf80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d41355ba8ffaf6045baf6fed64e6c02b", "guid": "bfdfe7dc352907fc980b868725387e980a2586542aee47f50069b4e609e95eb6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871967e3fb50ec1519d5f0e37fdb63ffa", "guid": "bfdfe7dc352907fc980b868725387e98ff329000b4f20b17a27ea21ce0626977"}], "guid": "bfdfe7dc352907fc980b868725387e981aded8a02e4b1e337a315b029e4bfb91", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fb3884ab1d18e8797270b71d15d6439f", "guid": "bfdfe7dc352907fc980b868725387e983e6dcd61148781ca855fe92f7db09b2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983995edbd12d3ae03346028c56e5ba886", "guid": "bfdfe7dc352907fc980b868725387e9866e5064fd7a9125a6b713372d680c524"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b639056ac566084cafaa30cc158774f", "guid": "bfdfe7dc352907fc980b868725387e9872d600e0be7a46c569226c942040fa57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981067db2195b174dd3c69cd30af3cd1cf", "guid": "bfdfe7dc352907fc980b868725387e98ee427f7940a070d745623c66d8c1b97b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7d245c9c3344d890f6a2f05c6fbdbb3", "guid": "bfdfe7dc352907fc980b868725387e98808c3f107c3150d987a622f7d1bbda56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98362ae8c3869cdedafb3d7a5e4829ec7c", "guid": "bfdfe7dc352907fc980b868725387e9861eaa32b5adcdd7d6ee6553abcbf3d8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6633d0de634119e12eb5720119e776f", "guid": "bfdfe7dc352907fc980b868725387e9811c8c0afe8469cb9c707bf9746a7ac2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0ab38ea4f339f26d5447a0282ba3e9b", "guid": "bfdfe7dc352907fc980b868725387e9897e87d5e4218ee912acae6064cce6234"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f681efc572dc6c480cef76c37ea067e", "guid": "bfdfe7dc352907fc980b868725387e98d83895c00a5d47fee98859d5c618a8d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98375cadeb5808cff2f34d97ea8edad918", "guid": "bfdfe7dc352907fc980b868725387e987e404fabd4e635a6a708573622e6eed2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887847ce510190bb3401f3c79cf55db64", "guid": "bfdfe7dc352907fc980b868725387e98c4f914657109c26cd7a882f091e4852b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98819fcc44472c00d1c3660d22d1878cbf", "guid": "bfdfe7dc352907fc980b868725387e9855dbb60637d32e33e42287f4d9fc792c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d2b443f44f9774eb51c7292f2cb493b", "guid": "bfdfe7dc352907fc980b868725387e98158466ccbc0debc03eb00b4ca0c3afc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e30cd963ed5e6d15ab7c8d4fea073be", "guid": "bfdfe7dc352907fc980b868725387e98801320b4a527d37dd02e224865a958b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f48f6fc95d3a2df82de62b337c4689f0", "guid": "bfdfe7dc352907fc980b868725387e98d7099315dcf4f878890c3932418d5154"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98024704438c81b9170b01fe6106ac5033", "guid": "bfdfe7dc352907fc980b868725387e985966cde74b01bd2ba48d65abbebc9c21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e72a07b5e49e157d3afb7a25e3159567", "guid": "bfdfe7dc352907fc980b868725387e9828395b9104b94e746bda0055712ef66f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ddf0a56a4df089c21c080fc544ba423", "guid": "bfdfe7dc352907fc980b868725387e981f81db25bccfef163e8f58db59961417"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897ec918337b7c1df871e3f73ba357831", "guid": "bfdfe7dc352907fc980b868725387e9839786429cdc16b47ba317f6fc105d1bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872bb4b4debd8e47c0e0705517975fc55", "guid": "bfdfe7dc352907fc980b868725387e9874acb33857fc8088fa06c76d5b5baaf6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883172bdda2cdf340a90a0678d91dc0a7", "guid": "bfdfe7dc352907fc980b868725387e98b97d363b0575d23b26a4f5b8b703ec17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa2d9a844d1a41802744749869c3d33c", "guid": "bfdfe7dc352907fc980b868725387e985519cd16c390bfceaab7853a823fb459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8a6dfff0df7432036af780c2d546c3c", "guid": "bfdfe7dc352907fc980b868725387e98b36f75eed1c780aaf0f08cec0eb278b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e186557fcea992b74c10351e1ff20c3a", "guid": "bfdfe7dc352907fc980b868725387e98e41590211de7b295360237849f5f3e4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899ddafbe7cdc133a298402b32cbd2a4b", "guid": "bfdfe7dc352907fc980b868725387e98943ae06af83ca3f01d2ab544826ef749"}], "guid": "bfdfe7dc352907fc980b868725387e98a321da4fc5869c3c238cb23e0891a680", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98165ba185aaa21f3aacb9684b276fd16d", "guid": "bfdfe7dc352907fc980b868725387e983171ca607709ba70b7546ebed4586225"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f982a99b372a3b2ef58d7610104413af", "guid": "bfdfe7dc352907fc980b868725387e98c2d16e607333c752087b1e4969575d2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98296517d02b240e4d67c025b23edb9e0a", "guid": "bfdfe7dc352907fc980b868725387e985bf343262a16656a91a507b09bce39f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851394a1314c14d2c744515f3c3f4bb41", "guid": "bfdfe7dc352907fc980b868725387e98130ec0c2334a400e82d3ad32be151f71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865cff1691db8b2db0bf426be5157fcef", "guid": "bfdfe7dc352907fc980b868725387e98d54612bc5779951b16ff59fe9db93e54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afdede430b08af96a0e81afb74f10213", "guid": "bfdfe7dc352907fc980b868725387e9825424d0feb24a4df1e3c56fdb9919796"}], "guid": "bfdfe7dc352907fc980b868725387e9897accaa9157e6d4daa68cd6abfaa8465", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9892138fcdebd2c18a4559605a27420448", "targetReference": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5"}], "guid": "bfdfe7dc352907fc980b868725387e98e408fa91ecb9a202b7a733d7c8b68761", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98758cc842172da540ffb591e63e38dc1e", "name": "AppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e98cd8162b601eb6c17e4d86eec112a388c", "name": "AppCheckCore"}, {"guid": "bfdfe7dc352907fc980b868725387e980be6c76e7b3dde057d7e3e6ad61f30d4", "name": "GTMAppAuth"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e9832c61b747d3949a8e639c0653b6048d5", "name": "GoogleSignIn-GoogleSignIn"}], "guid": "bfdfe7dc352907fc980b868725387e989b0ee9a6d93c0cfa024bbc34a88b2122", "name": "GoogleSignIn", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9815509a5aa54606eda7171e744ada7414", "name": "GoogleSignIn.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}