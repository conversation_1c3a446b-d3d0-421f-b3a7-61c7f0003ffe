{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983a53b2ca5d233417705ef7d533a502f7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b9ecdc329b9850837d853b00285cd01f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98710fb2a7258b9f2678f395c8ba0d6118", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9896f6efebf9ef233e9d22445cd201d80e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98710fb2a7258b9f2678f395c8ba0d6118", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d6ace4da41d01141b6ae9e5fbc5e7cf4", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98be2b0f698391419d7d50f9381b269680", "guid": "bfdfe7dc352907fc980b868725387e98952730642e3eff78658db8ed766181c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d792d06f1b7ec163b059cf9f0f1dea8", "guid": "bfdfe7dc352907fc980b868725387e98218a243aa5f10bf3b9d4109df2e74155", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a6ff4c1db1b5a40dc706c431fc04530", "guid": "bfdfe7dc352907fc980b868725387e981f468847ca46e5c693921238024d013b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825da5508d5596c6d213b1d65fcea410e", "guid": "bfdfe7dc352907fc980b868725387e986bb35c3faad6d6f0f8e8128f7db11178", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f393d1f90f8dec2b9a82630563d69a8", "guid": "bfdfe7dc352907fc980b868725387e9861470137bb3aac669ee7786d89ccc03f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfea3244a99d59f2c6f45a0124ea1a9a", "guid": "bfdfe7dc352907fc980b868725387e980bbdbec893f233eeba77eb87470d6038", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7c0edf39b9bcd172cc895ea8ce1f0fb", "guid": "bfdfe7dc352907fc980b868725387e988fc7f34c6c5eea2c5d1a840d076ab63d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d56b05a2c2ea30ba848d6039d28300d", "guid": "bfdfe7dc352907fc980b868725387e983aa299470b5bc981d49c9a7ca1b07c3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c691dd8d93a1314d493f71a968a699cf", "guid": "bfdfe7dc352907fc980b868725387e986b383a90f2ab8757d66c9569f00e8052", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9831f093b8b3236d4d7fe540a4e7206c96", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9824af6ce6019f8ee670873a9f9e047739", "guid": "bfdfe7dc352907fc980b868725387e98902b4637ef039c489471fe948f162b64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b17ce4b49529d7c0025501f57f417cd", "guid": "bfdfe7dc352907fc980b868725387e98cef893e8359133aca67a295cb71081f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a1afdfe76261ec0b767179a2e1888f1", "guid": "bfdfe7dc352907fc980b868725387e988760740abcf9a20f604f0b6e5a4e6dfe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985de0d45cafd33228c1bf20ac98fbc1c8", "guid": "bfdfe7dc352907fc980b868725387e98646a0a3578beed10bd419b43bc2d4f04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886dba94f8ed3023b8dffa9e1563e9642", "guid": "bfdfe7dc352907fc980b868725387e98e47fa486199ef0fd7f435d1f96020914"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a3354a36930e867989da795ca7e50c8", "guid": "bfdfe7dc352907fc980b868725387e98a058de7a3d7637836621a1ae325d90ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d051590ffbe5b21fcc87f1e08bb12d6", "guid": "bfdfe7dc352907fc980b868725387e98578b4cbdd66d8463cb78dc07624d7500"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b989b16a30797b4de0eaf24287dcaa8", "guid": "bfdfe7dc352907fc980b868725387e9873713f4ed7c5a14ba36a49a4876624b2"}], "guid": "bfdfe7dc352907fc980b868725387e9885967c96344189ad5465674737881812", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98296517d02b240e4d67c025b23edb9e0a", "guid": "bfdfe7dc352907fc980b868725387e9857fd7ab051d4e387d07d0a6089b5edf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865cff1691db8b2db0bf426be5157fcef", "guid": "bfdfe7dc352907fc980b868725387e98d2505c087ca7a7fe615b358f868430bf"}], "guid": "bfdfe7dc352907fc980b868725387e98cee34b6a198273ba562d8463659344c2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9842ff224784a1141cfe904dfdf728fcd5", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e98c2e862870669a30c1c2260c9addbff8f", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e98445ea76c11431c8b28644f3394ef65c6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}