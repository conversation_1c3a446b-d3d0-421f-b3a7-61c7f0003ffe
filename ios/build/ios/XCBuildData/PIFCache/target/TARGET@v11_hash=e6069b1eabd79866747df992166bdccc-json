{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9876b703835f5d4ca28909a31f348c9b7f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d620364272c0498e8a5f35ceae6202da", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98caf54c53db871dcfb0e2a947ce155248", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d1a89a91aab8b6d7535065689461df01", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98caf54c53db871dcfb0e2a947ce155248", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f96d16c4554d0ae077d22ec976cfa534", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98084b2d4c84d915e69bf3c494788c6bd6", "guid": "bfdfe7dc352907fc980b868725387e98907a8316fc22cd0e43649e787159242a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a6065054c34b65eb9b1702126155673", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984f66626b13423502e670596aa2457653", "guid": "bfdfe7dc352907fc980b868725387e98c3c27213aa97ffaf258891ccd3b1342f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858f27a581dba0fced5eb98200315fee5", "guid": "bfdfe7dc352907fc980b868725387e986b37af86626246541f2cb3e2cfe043de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98718ba6b268c00df292d0aa4847bf4570", "guid": "bfdfe7dc352907fc980b868725387e98c05a8c56b2ed9f783fcd6ef08d5fd936"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984873568971bca1683e52859b9c1f14f1", "guid": "bfdfe7dc352907fc980b868725387e980ca0bfa1c091e50e40dce5fe4d16cb86"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b32bd1bd37b46495e7f818359eee4aa0", "guid": "bfdfe7dc352907fc980b868725387e98f83302dc49b25f6327dadc7bc9e6f66d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98897654c22ff26eda768c4a6f483b0da2", "guid": "bfdfe7dc352907fc980b868725387e98cc383b3f24df257a2478f5c07157ff55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873dd841e2a7a56165ef3984c1889b5fb", "guid": "bfdfe7dc352907fc980b868725387e9848dfb2870d7d915a05b925909da5827d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa5186469cd626d9030badd3ec0e9c92", "guid": "bfdfe7dc352907fc980b868725387e98e6b5f420caeaaaeb3b2d9487a458b847"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d2d1e8a9a423a9f27614f99fdd6e60c", "guid": "bfdfe7dc352907fc980b868725387e98a447e6e6ecd4cada700a346e5ad97b47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce041390fa5dcbc2a5ecafacb8cb2d2a", "guid": "bfdfe7dc352907fc980b868725387e985dd69df3f2c5878b00b39bafa6b4498f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e72a29710e24032957cc82499159899f", "guid": "bfdfe7dc352907fc980b868725387e983d28aeab7a86f8e5893dee0f2e5f0c2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ac9aab88db1a5da95981edd0c407c09", "guid": "bfdfe7dc352907fc980b868725387e983f5735726d7010bcc9e67a8d99ad8d48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e75cc2c1d2028336b5365d4d0a14e1f", "guid": "bfdfe7dc352907fc980b868725387e98753863bb0a198a2356becd168c70427b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccdf08c0cafde78cee673765bde8424f", "guid": "bfdfe7dc352907fc980b868725387e98f70239667d5880e8841b813015ad60ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c25476ee780af7c38cab3c3d9d669198", "guid": "bfdfe7dc352907fc980b868725387e982cc30eb79d3ee0e0a73de29d931a6d3a"}], "guid": "bfdfe7dc352907fc980b868725387e98d350aa8ee103d88075d9485caae94376", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98296517d02b240e4d67c025b23edb9e0a", "guid": "bfdfe7dc352907fc980b868725387e985d8915303f6d11f4f4070041982dbfa3"}], "guid": "bfdfe7dc352907fc980b868725387e980bb311bb44bf97c1f9997b0370be3637", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c972348c89047f68d3afc3fcea0ac138", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98a8ea30270b2eb5e717c66953addb7ac2", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}