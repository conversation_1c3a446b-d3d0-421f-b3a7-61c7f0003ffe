{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e9841694db9191404dab0574df10aa4d92c", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e983e45497ad99b5f0ab9ebcda8afbb048b", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98ab0849b3c8d2627830dfc45f1064e777", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9812ca4e888d5adbf2894b63efd16282da", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audio_waveforms-1.3.0/ios/Classes/AudioPlayer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989f871152ab141b9246adc99a1a1600d1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audio_waveforms-1.3.0/ios/Classes/AudioRecorder.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98902fbd22c8dff041ce5bc7a84c671c90", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audio_waveforms-1.3.0/ios/Classes/AudioWaveformsPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dec21ddcb3120f8eb28a48fa7b59d75a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audio_waveforms-1.3.0/ios/Classes/AudioWaveformsPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9801e5e2fc153e76d392b4244b08157614", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audio_waveforms-1.3.0/ios/Classes/Constants.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986e27ec491930660cad7cbeedc301693d", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audio_waveforms-1.3.0/ios/Classes/RecordingSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98793207306b2d60714f38df667c457ecc", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audio_waveforms-1.3.0/ios/Classes/SwiftAudioWaveformsPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f8e4cf856cbab9024ee2ffb981fef005", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audio_waveforms-1.3.0/ios/Classes/Utils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98379b69bb1f80384b93f89d627f6c625e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/audio_waveforms-1.3.0/ios/Classes/WaveformExtractor.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9892cf7dacf6c03deff33d8a560b14e8ee", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b39502a21a1bc624c146fd3bd4fd8857", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ba1e115cbc3ceb14177ae61dd0fc3f16", "name": "audio_waveforms", "path": "audio_waveforms", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984e9c11a04f7db6c0e13123f129c62b0f", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9874439011a34cea9bb15c4cb1fcef28cf", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bbebe2f57a159d06483e4a79ce7b4b1a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f2ff8457da3ab848cbb5b6fd0ca8b1d2", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4c864f257c4516e5065ef7583566fa2", "name": "windsurf-project", "path": "windsurf-project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98678c3681ee2b836e4a72e0e4725ce74d", "name": "CascadeProjects", "path": "CascadeProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ff5f02a180c156fc557c131268bd7ff8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e3162b21cc5287b66d63ea45108aa960", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b5741dbe070882ed8706f7d57f7d99e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98721469d80918b83c5640a5d9f335476c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d04e34e0211d249393e466c343fc1cf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd94750eb7af231932e5f8303f27624f", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audio_waveforms-1.3.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98e1cf5980462e93a90fe027068ba74119", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audio_waveforms-1.3.0/ios/audio_waveforms.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9896efd8f744af270d0e1f9a3f50ec0372", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/audio_waveforms-1.3.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982afc288696ea736ccf13d58ab2bbbd59", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e987693420d756c862113e2b3d0fc7184aa", "path": "audio_waveforms.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986a5206b3df7f303a683579be9de7fdd3", "path": "audio_waveforms-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984d920f395d034384a8560bb44d0b7998", "path": "audio_waveforms-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aa077ed8624f95b4cab82f518f465fc7", "path": "audio_waveforms-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989e814be14cc1f5cbecbc76d468cd527c", "path": "audio_waveforms-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980aaabd9a53d5e733a117a148de2ecd27", "path": "audio_waveforms.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983b26c8ca0d16b378ba7bae144b538af3", "path": "audio_waveforms.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9825550c58ee92d722f9cf18063eacede0", "name": "Support Files", "path": "../../../../Pods/Target Support Files/audio_waveforms", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988bf70ce8bf6b4c300eb88c2230d79d86", "name": "audio_waveforms", "path": "../.symlinks/plugins/audio_waveforms/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9879e6182632772cac435cd2c5f0c08fd2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/ios/firebase_auth/Sources/firebase_auth/firebase_auth_messages.g.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988fdfc4d6d6878858e4708b56e47d17b4", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/ios/firebase_auth/Sources/firebase_auth/FLTAuthStateChannelStreamHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9814c9c22265be53d88174c25e69454027", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/ios/firebase_auth/Sources/firebase_auth/FLTFirebaseAuthPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e6f7d1c78e44a53f5b563092fa7be70d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/ios/firebase_auth/Sources/firebase_auth/FLTIdTokenChannelStreamHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d9d964f552174443b5839db4a8ba1e6f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/ios/firebase_auth/Sources/firebase_auth/FLTPhoneNumberVerificationStreamHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987d7c9da34c592b5048d78eaafb947042", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/ios/firebase_auth/Sources/firebase_auth/PigeonParser.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fde42fd7e25a5a442273c1a79b64785c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/ios/firebase_auth/Sources/firebase_auth/include/Private/FLTAuthStateChannelStreamHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988661c7f98217484b8d0b94ba729d6a7c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/ios/firebase_auth/Sources/firebase_auth/include/Private/FLTIdTokenChannelStreamHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98da908d28dc257ae858ebfc32b9e52252", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/ios/firebase_auth/Sources/firebase_auth/include/Private/FLTPhoneNumberVerificationStreamHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9883f330749c5156136a79c0d5f81dfb6c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/ios/firebase_auth/Sources/firebase_auth/include/Private/PigeonParser.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b0555a0f044c36553493aa873e4ca927", "name": "Private", "path": "Private", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988b52526eea89c7e4993858e14fff93f8", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/ios/firebase_auth/Sources/firebase_auth/include/Public/CustomPigeonHeader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9821b0f7c47928df5ea98c087f8dee6601", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/ios/firebase_auth/Sources/firebase_auth/include/Public/firebase_auth_messages.g.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ed67129f802b6f2dd1ea4abf1a4086ee", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/ios/firebase_auth/Sources/firebase_auth/include/Public/FLTFirebaseAuthPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cad3eecb67023172ea12ec42b6e56ba1", "name": "Public", "path": "Public", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9862f844d307865f9667af5bc237b68d6b", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bbe1bf417e71584b93c9a11cd8639714", "name": "firebase_auth", "path": "firebase_auth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987163ca6f9eb2a8f9c8225be59fa9d62e", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986cb061431be4907387be38047d2ae174", "name": "firebase_auth", "path": "firebase_auth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9846b84da2d17898eff55403ea955f40d0", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982e5d9fda08a8801e17b2ad53f7b947dd", "name": "firebase_auth", "path": "firebase_auth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9807fd72900f9e300d72a459efe8160088", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e6485f9025becc378d80e04724990588", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982a04ce0f1e380067f532de7f0ccd8adb", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988d31abca1e3888264025bcb44f445c99", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9863d9729031ddf2703dd81bf4407ad897", "name": "windsurf-project", "path": "windsurf-project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f8800d695fce2b0d76007af047fa838f", "name": "CascadeProjects", "path": "CascadeProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98504ed3745eae2b3f24147f8988f25c79", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898e0f157966c50871dbc0ab7a89577b0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98308b4241879ef0a5655cd70e8ab81c44", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98210b60a09d9edaa9a0e5b72611c89397", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce2f9b947d331a7effa79fc6e1a118f3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982a6e5cf40acc94d74cdf6e29d0427762", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982af3afbafd8161cb2816e6be5f90aa41", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9889f4bd6e5fbbc27eb97b15d0f626dd0b", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/ios/firebase_auth/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98edb699574e42c53c559b9d278a94dc16", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/ios/firebase_auth.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9827dfd5b477eae6a119767b9d3490998b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_auth-5.6.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bdee74859bc339b4d92ed41c0d2635f9", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9829ceb6c18fc627b54d700465aa576c6d", "path": "firebase_auth.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9851fb2c9db0ed971c77ab4328e7ba1989", "path": "firebase_auth-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9872f57446aeda4906c701fb8fe9f46cb0", "path": "firebase_auth-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fac6587d418ab308043474dbc654d41c", "path": "firebase_auth-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a722a2f01ad77023b7a7bc7a0c846a53", "path": "firebase_auth-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9874c0850838b8f2060741f77573e4e8b9", "path": "firebase_auth.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9899bc89ffbf5b34f30cc46b1c288f1b7d", "path": "firebase_auth.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9850ba80ffbbb7ac6c3e1062c9688e00ae", "name": "Support Files", "path": "../../../../Pods/Target Support Files/firebase_auth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d1149e2b0468d1343322d402642c1486", "name": "firebase_auth", "path": "../.symlinks/plugins/firebase_auth/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d1f5cd20f11a4ed6c4d8469343bb75a0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources/firebase_core/dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982ce0436175245a89c3a3ea610927a45d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources/firebase_core/FLTFirebaseCorePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981eaf0b5add9fcf442b96ab03db2a4861", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources/firebase_core/FLTFirebasePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9861f59511af4d0c528521384ac8451182", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources/firebase_core/FLTFirebasePluginRegistry.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f96f4ab0a4102f1a2ff92bb4366b6eee", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources/firebase_core/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9831722186bb7b07cc0c29107e7b49896f", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources/firebase_core/include/firebase_core/dummy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98869d2d2c9fb011e704c8e8439b23c3f7", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources/firebase_core/include/firebase_core/FLTFirebaseCorePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a4186aa0b5a6694b70b1269f230e4b23", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources/firebase_core/include/firebase_core/FLTFirebasePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9808f474d72990a9cfed1559c298160d35", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources/firebase_core/include/firebase_core/FLTFirebasePluginRegistry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dea9414023d3a3a838bb6958d78a4b2e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources/firebase_core/include/firebase_core/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fb780d267be9c23ee034dc447afbc7dd", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987185399fd671bfb5b31b8646278bbe70", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98de426307b79aa7d9fae5e0bf9121482e", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e34f9d1e58f8c8beec0b12def679fec7", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c5699b170ce4c63b45b6e431d971f66b", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9832d5338fd2538e347eaf4b9263236ad8", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e56f087df7d713f4320fab73f01b9092", "name": "firebase_core", "path": "firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983aa70b4c3a6e501590bbe4ba715d8af5", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a4b32fa3558e294dafdd0121fc27cffe", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a650e3c5e605dffc5506ff01b73c4af8", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e6aa4c3e9c65e13a2f6b95ae247fd4d", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab48f00a546ababb64372eb71ea54399", "name": "windsurf-project", "path": "windsurf-project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981545d322ed99d7bb144658d2151e9ac0", "name": "CascadeProjects", "path": "CascadeProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98366782a88ec1c29920ce5f15270480bd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989eaeb79e4c6751f9c99432d99ad894e1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880b32c99a1ecc5a5a80597a10a31cafa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98590d06ecadd7b46092e094b4bfe3fa01", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984594687ad3b3b8e157fc169ff7b4d738", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a3252d6ccf988bd9c4dd16a629caa51a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ac54e8e268d0010b2e5c25aa8ec44d1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9815e00f4db0bf5ea75d1c46ec55b03410", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d98575674b9ab01c6f4352d27d9a768e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/ios/firebase_core.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e987db7cdf404f3f7391a9082d8f523a41a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/firebase_core-3.15.1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98834072d3c8f2bb007ebeb58bff859e3d", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9802831a8dc0cd19966d60c4289df58c97", "path": "firebase_core.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98baebf6e6251e43c3fd9df452f644cc20", "path": "firebase_core-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980a1938acbb88a38f228b9563d4e889f2", "path": "firebase_core-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9806b507dfb9ba84251a4333e8f2a10bbf", "path": "firebase_core-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e55129efc7b6ec13381146fe0c865fbc", "path": "firebase_core-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98cdfd1d296e3842eed2a18630e12db550", "path": "firebase_core.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9842a9e6b3346b34cdf30ae415d3f71c98", "path": "firebase_core.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9845a1fd09af4d7e51ee43f08af29e78f9", "name": "Support Files", "path": "../../../../Pods/Target Support Files/firebase_core", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988afe96429bdb55e21627540fded71b84", "name": "firebase_core", "path": "../.symlinks/plugins/firebase_core/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98fc0496179224f4488835d9def64b727a", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e5edf47b02ada2fdd92c7351e38629c9", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bc0771a525b2cd9ce9b5827423aa2580", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9841ec97e9cebdbe5bcfd244a73e13e01c", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981b537e7cdb19afda733e0aeca4b25941", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9885956bd3025cbdde4919bfca28e9c86b", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984442867c0a0d408558738e9a2248061c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_tts-4.2.3/ios/Classes/AudioCategory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dcd7b185156dfe13e01f4db10a4f42f5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_tts-4.2.3/ios/Classes/AudioCategoryOptions.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cedc3433e80f917da21bbcb0ba862135", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_tts-4.2.3/ios/Classes/AudioModes.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989f4a5d682144cc3e4468e1ca706af1f9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_tts-4.2.3/ios/Classes/FlutterTtsPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9883605aed15254668f83722db847e4a06", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_tts-4.2.3/ios/Classes/FlutterTtsPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98719c4c7b59d28caf6927597622e7c9fb", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_tts-4.2.3/ios/Classes/SwiftFlutterTtsPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98253d4f0ffaf582e721f13a0ca655d062", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dc86f8a1390b3868f28f521c3570d332", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9829830b7ef670ecaf6811e50fbc2503db", "name": "flutter_tts", "path": "flutter_tts", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fb73220e50621af71ace2857a1dedb3c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980076329eca9b722a21b188e2a412bc46", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982f3596090704dd31a12e5a8f738cab6e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c833d858a8a6a6a84877968a150b7456", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9823804915259d89b4f43a1d1ca94bc007", "name": "windsurf-project", "path": "windsurf-project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a938037486ab881cce9270cf16074875", "name": "CascadeProjects", "path": "CascadeProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e07c8b52372b5e0a2e9dd1f7e484233e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98613385da8c7bbc7d42b913f1bdf97e54", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c09db8d40c812445404f1882e61d725", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9849b162f93da53e1ce3e9ca62a09cbcd1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984fcf52ca8683ae95adac39e5bf10d1fa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f3fce6b407639dc59ece8a4a5a297e52", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_tts-4.2.3/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9865de04304e07b7666b7ead6b0abb5def", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_tts-4.2.3/ios/flutter_tts.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e981fb833858a3f0e4acafdf94a1f9e4333", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_tts-4.2.3/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a3de9a0edb7a13490e581a9eed7e56a7", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98f3a0711ef15b3d90a03995cf1b4932ff", "path": "flutter_tts.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fd6bb7ed08ec7473f18521895846d8e4", "path": "flutter_tts-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98dd6d8385576550ec44649e3926198e0f", "path": "flutter_tts-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9898337d748829707b060364aec836f19a", "path": "flutter_tts-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e6b43669ac95485fe96ab2445f30b432", "path": "flutter_tts-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98411511954ec92e65537675a8dbb4fbb5", "path": "flutter_tts.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987bf468f7fa204f68110f36202f290144", "path": "flutter_tts.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989bd2e238ccb23766c5e6f30b5c0707e1", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_tts", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98762c9e81a39f2d38335abfcf7426c6f8", "name": "flutter_tts", "path": "../.symlinks/plugins/flutter_tts/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989bb9707e12650c499cdf27768d5933fb", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_unity_widget-2022.2.1/ios/Classes/FLTUnityOptionsSink.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9898338b16dba04c69e2c9124e82656c90", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_unity_widget-2022.2.1/ios/Classes/FLTUnityView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f352f678466d7a97e00c468eaf8c179b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_unity_widget-2022.2.1/ios/Classes/FLTUnityViewFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b5742a0c68664413fc6eb192957fbce1", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_unity_widget-2022.2.1/ios/Classes/FLTUnityWidgetController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9850a036564e8e4be1aa6dd09d05013563", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_unity_widget-2022.2.1/ios/Classes/FlutterUnityWidgetPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ad3e46005c2eb5de272df0c60bb9439a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_unity_widget-2022.2.1/ios/Classes/FlutterUnityWidgetPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983b1dc0e08e76a753108119bf245375ed", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_unity_widget-2022.2.1/ios/Classes/SwiftFlutterUnityWidgetPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dccee9375726fbe3f2536cc5c237195b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_unity_widget-2022.2.1/ios/Classes/UnityPlayerUtils.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ee7c46cc8fc924fa7889bc1d0c447e8a", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9803e767c145cf98de114dc736d87a7640", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b0cc6d7a315dcd2aa177e7d631cbe92", "name": "flutter_unity_widget", "path": "flutter_unity_widget", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98523ccb3c87987bdb5b0ab099f0f4a688", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ee5db633a7fb109b37caf975199fcea3", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98626d47c48119844b8b5066f7a11b4698", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983941d026455d47018da940b1ec702eab", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982409ddd0f83b93f47c54f54b3b84354a", "name": "windsurf-project", "path": "windsurf-project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9866d95b941c8266a69905d46d5e7ecd8e", "name": "CascadeProjects", "path": "CascadeProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98144406122125c58908f0e020c882f88f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a6cf73388356d4c38a6da6383531e00a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b767e7a93d766775f7979301c69009da", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cba6efaad5de75b17d7bd3334d666001", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984383e88b241eac00775906a9c547ecaf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98348e095cf64e0d61767f70649a49efb7", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_unity_widget-2022.2.1/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e988c5157d87cf866ceac1fdc0ebba780c1", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_unity_widget-2022.2.1/ios/flutter_unity_widget.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98aa111be34a6824893e0a9454a9c2bcdf", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_unity_widget-2022.2.1/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ad164d5c96afe3e7293191d10b1c97b2", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98912652867f3f024f0af6e19ec4438139", "path": "flutter_unity_widget.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9880a12174b376a6442c27d983c960fea9", "path": "flutter_unity_widget-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984c72af3d1087a6743e3ecf97f385f77e", "path": "flutter_unity_widget-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e0c4b09f85b8cf817f9ca1070b4c4c33", "path": "flutter_unity_widget-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980fc8fe31229e1d3e4126bd1e6ab58064", "path": "flutter_unity_widget-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9880cb99e56bcfdd51a967b1399ebb67e8", "path": "flutter_unity_widget.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98171f90901b72642e5d492b287f813778", "path": "flutter_unity_widget.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a85c4a857e93e0b0a6a96207aed58f19", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_unity_widget", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98466a00b012f6f0de4265268e81123d78", "name": "flutter_unity_widget", "path": "../.symlinks/plugins/flutter_unity_widget/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9841ab44a4fb5bc49dadef060c990f178d", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98be1c3a0ed0f3faa8b59142aa402e4bc9", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a64f91e39e0e436931b508ac862255e0", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986fffb3c26712c9cd8bbdac820cb58531", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9887516d891b84883626409387de4af2d3", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98da86dc734974c65d9c5c140f06ee86f7", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a14349fb23580533f5894acec38c0457", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d25efd07373e2ec42fb8af7073f46a38", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bea6cd0847b639da9cb59f34ac740dec", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984fdc00320638a3c8a30b2101057b9052", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bf1bacb6c1b656ec8f506abcf1981e45", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e0d1ae4c007a1317fad9f5e88a62c7c6", "name": "windsurf-project", "path": "windsurf-project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b57adf332abacdbae5938da24af7541", "name": "CascadeProjects", "path": "CascadeProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9885d5a9e23324af0b2a97d60ecac3b75b", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c690ad0eb6c7f55ca9f474b20a023071", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/FLTGoogleSignInPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98562275132988363d73a39392b8b4307b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9880a0d090f570a3d4d3e57966fb814e9d", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/include/google_sign_in_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981ea766b3dcbb05e103a6ebc43e607dda", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/include/google_sign_in_ios/FLTGoogleSignInPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98831b4fde2f27c43a7f7c9c59b0d93b04", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/include/google_sign_in_ios/FLTGoogleSignInPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c190c5d0acb8c5c583471df7301a4eed", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/include/google_sign_in_ios/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98baa1fbcfd301e8db1149cf918829b867", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9815658ec2467b9a88e0df1daad321831c", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981fe198a67097caf79b32790c04544a5a", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9855fb085f4191e0c10e0267f2f50cbd3e", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9803e0de3c646e6d7280acab5ad16b0343", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98841860c627c59000a4e8e8f69abf4d60", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98090463aaa5a643b4ff36e7a1ceba9d1b", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98acbe4a11996c06d9f4d8df5255f61cc9", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ed5a6acc0799b21d81ef22bf4325a1c8", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ed1c0876045d509b79aa92891e05271", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984b1468eff9311772769d262d7c25e562", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cfda1dc7c0b4607f35bcb22a6a61ae60", "name": "windsurf-project", "path": "windsurf-project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981632fd8e3b6966c334ebcf318b6d4f7a", "name": "CascadeProjects", "path": "CascadeProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98aa9c462c5d7161561b6947a58d4aa22a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bbc7b07d638a8f00290b6fa0938e6b04", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bdbdb0a0e0a89a45bdb7c17bfbc32d01", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981411028b5b5938cec46ef25dc5af8979", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9846eeb767e25f97d4229dbd7b8a995bd4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9882186ccac403306d0401dd493f121362", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9850785ff880ac13c1f5bdfffa166e0231", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9867fea4e463ac17ce3f655615bb24a9fe", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98cda1e8b67af633836b20261ff5a22b23", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/include/FLTGoogleSignInPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98f5ffa16932da49feb73df131b081813d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98e79061d97de5853347e6bafe48b87333", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98060da787a231ea5726747751792b7f99", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9852bf9c6ed94ef07b98c69e340f42cea7", "path": "google_sign_in_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989fecdf985f034675641d6436049cf600", "path": "google_sign_in_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9820222d3e32a8937905a87df9646f5973", "path": "google_sign_in_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988783c2b320ee4921fc037c4a49cb52b0", "path": "google_sign_in_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9809b444de189bbb4ba017c1ca7bf88099", "path": "google_sign_in_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98eeab8d0e18990fb1fe9baace99fe1e2d", "path": "google_sign_in_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98efbffe41a4ce95e0d9d7ee45acdfc4cb", "path": "ResourceBundle-google_sign_in_ios_privacy-google_sign_in_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985bce382ad9a77a626c82086410a056bd", "name": "Support Files", "path": "../../../../Pods/Target Support Files/google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987c4bfc489b384a042abe696f24e5ee0e", "name": "google_sign_in_ios", "path": "../.symlinks/plugins/google_sign_in_ios/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98377a5292c6b9e55b3c2a36909500f618", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c119732b35a02d70e3d1afcb5b561b1e", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e32fb4c38cf42db5e8c0cdb948845d7c", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983ddd9531f276612cc5aead90c84a7efe", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b98564f4249e07db81422825cd92891", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98838b66940579a665ba63b613c9b15511", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9896932e09d0dda29bca3cd4a44f19b8f5", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a37ab548213971b9c25e0ca0cd140138", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9895fbbc4f7a498a0a534e9081f6793d3f", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986c97cea653d69b68d9a62cfced3d7473", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9895a14c80621fdfc38ded0b3b06f4aeeb", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986be0ef5343a16bfcfdf9994f7a50fd9b", "name": "windsurf-project", "path": "windsurf-project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982b88bc5430cac8e2b66de8d2d40245e7", "name": "CascadeProjects", "path": "CascadeProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987dc265c0cf4167eb3b6714d9af837081", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98870507d4c2413e91b26713587bb95b5a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ac86f76757f72f413f676e725317e2c9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985026bc10bb343e4f44151cfa21bb70a6", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98afa18cbc4f196d046cbd0b425909a9e5", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b74b36aa9ec5b45422a9e55b6cfead7c", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9899ad9f2643f0e4015525f96b57aca72f", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a2aba4709e9b78f188e2ae2727799c96", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c67aedf7a04bbdc5ae151b64423928bf", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9832f310eb7859d05df6d081b3a5cdd092", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ebe1e935a4fc51e18cd421c1298400fc", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988b086ef386918b907aa2964c7b1232e9", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98215a38ecd560a75517ec64df55b4afe2", "name": "windsurf-project", "path": "windsurf-project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ed8b2885b528c0fa91aa2f6dc7f9c7f", "name": "CascadeProjects", "path": "CascadeProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880929a0c094f4ddef65e609267d8b27c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d67ac885c4cd5d1befaf07c5ec6161ce", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b0d56e24bc9312f57152566c1085e561", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a90c374850bd07a99a88bd56b598f111", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987fa8877730ea1ccfff66f2e07fc1bfab", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9884b2cd087c3aef2014401c46d5c83064", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986fa5107b234a3ff149c9224ab76b9b95", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9806afb3fb202fb42b2bc653158dea9339", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98a750d7bdb5610c355d6905cd4623e32c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e984cbdaffe125fd1598cc73a87e589e91a", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dc43e6f68f0c123eaf63221c374921ef", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e982a8e132d448f4bfc1de4940b938e93d1", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987b6cf90e99f28efacc1f59c325bb1d51", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9887884a9f55c2f387ce4497a800f6f341", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b25f951e434426d23446f1b1719f7827", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9879ac4f599452b77897252176bffdb8cd", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9855a9bc34339ca2756a3e6c3527ff28aa", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98925d570946f2d1566b3cfc348d91b8e4", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d91f6c1f817a882743fd028cf372e907", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fdc235d573d4b6af3fc485c85e2c3543", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f41aa25ce484531e98b660dfaf218b4c", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ab453ed0ad2c96970b94ab5f5ab6af94", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerEnums.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983bb0ea02e253bab3ff9d35e2fbce9c8f", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9811ab766ae4e69daedcb10061fbf2396e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c38f1b7053090df6147f7a5e1efad8dd", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982011aac88b5bd92b9be6876b84700c23", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/PermissionManager.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9860126ebb5f5f40d9a082cecdeb325075", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986fcba53c1f72129d6e67e8f4d1095711", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aa03d5f6a0c300c5dd1dc84d0d0c56e1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AssistantPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984723a55de87527eeb051af9d10a38473", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AssistantPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982dea745cfa1b118dddd74d41ae40e88c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AudioVideoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a6a3a63cc00edd1707e49cc5a57e9d2c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/AudioVideoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98126a1d06847c5d3af350888ae364097c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BackgroundRefreshStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e242f4d17b541ee230e53343410879d3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BackgroundRefreshStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a86799ca7b09a7c0974e2a49d5545d96", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BluetoothPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98de820b6c628f35581816d5c2f27f04f6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/BluetoothPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98622e0f3cde0bc4672e08a8d4788dbf60", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/ContactPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983f9c26d45b3531a2d1c57dc065a616aa", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/ContactPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f0fd351cbdcf32c7cf11db9cd1ae8647", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/CriticalAlertsPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98137f2dd0a6eb5643ca883507cb41d9f0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/CriticalAlertsPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b386558e4848db24aabda7a91e89e7e0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/EventPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a4b576672ac20ef2e9f6c576cac5ad06", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/EventPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9861ecbc4ce18ede7842fbc144301635c2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/LocationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9815274084b0cf56ef0f72cb8348d74ec6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/LocationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980df3634782429464019b88d2b0f3b4cc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/MediaLibraryPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985d93c530565d2d9f580a658d0ed24fa8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/MediaLibraryPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ff206b771d7c9bdeef82cad68a7c3046", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/NotificationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9884548bbc0020d205088e5f0a663b2b8d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/NotificationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cbd7488a2b6a364741da104f01eb85a2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9824749aae61c3d84bf4360583f2789599", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhonePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bb8bb97ef565f746b0067e7de06f4a8f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhonePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986fa0e0ccbb4c509687aca288c1260358", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhotoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e7576077d88a7435c7d60ebd8d74bfc1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/PhotoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98911403d4c32513735e69d220f08fa414", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SensorPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f96b85748220b15d9dde94edda37d3e9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SensorPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987644172a7dd7a3ec5e1c8c936f4bc0d4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SpeechPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989ac3bfaf7c3632ae8208a1d92b6d615f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/SpeechPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9879285b9499d7f219977f2f55e2b6ea79", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/StoragePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983a05e1bb1207dbc2e265a1191d94ea26", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/StoragePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9845baef09d202692b57bfb86dcf8aec9e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/UnknownPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f992d49ca7307e2bacd7316816b86cf2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/strategies/UnknownPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f1277dd6840088f9f78501b0563130c3", "name": "strategies", "path": "strategies", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983cd27d86fa3c070c404af28f349903cb", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/util/Codec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984264f3fd132bf5630caa3f81a290c956", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Classes/util/Codec.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b77cac416245e60b46c8bf59643e318d", "name": "util", "path": "util", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c611b79d25f54de8f362cf8fc446d3f", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98451f91a75c12968c3439ac3c6c9b4cd5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98aa277dd7c4e56b7faabf4aaa31d2b394", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eec00266cf6903341be07e58bb5b7643", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c7a930e893425a6fdd6f5b30dfa8c93", "name": "permission_handler_apple", "path": "permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981605b7f180ee3f6f1d53fdec2d3064a9", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ddd9aa532b057573be6928fd0c5ea25d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98de6de5a35d16a09d4b6def531f41340e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ee2bc11781f1d6e9441600193094f8b3", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981981cc39b0e324e3858428630ffe3b1f", "name": "windsurf-project", "path": "windsurf-project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98291ad9b2d97cb21600ab1c40dfa37185", "name": "CascadeProjects", "path": "CascadeProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9885717c7d79792ee0259578b38651e0bc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f54ed0f053e1e40ca6d99b627d50c3c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9820e8ac04b3ac7c4d6836a8b48a97beed", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d3a9fb3cf8578f873a81f08b3be08b06", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9826aa21a61467e415e9918d7afa78c351", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b491e8e28dcf5c5f3a576d91c975dd7b", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98afe0c97cb6be2d70c97ad394f6e4b891", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e985ac99356aa3548dd5eaeccdae0aa9eca", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/ios/permission_handler_apple.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984a67c4c636184c8c415cb22bdf8f6fb4", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9894f8028fa7857856fffd21c8c6329e13", "path": "permission_handler_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fc9ad86f6cfe6cbc59ddb55e512fc72d", "path": "permission_handler_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9807a5506af5a63cdd5d2750a9dda77648", "path": "permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98260e3b62c81a7db49884d672da74e0b4", "path": "permission_handler_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb0bb6eb8c61f3a22cf3d6a3e6a52aad", "path": "permission_handler_apple-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9805376b14ea4d7033f2400967cde62a4d", "path": "permission_handler_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984391e2e449f1f93e4657247539278b8b", "path": "permission_handler_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98854fc296c77f3008acc5c8c989e2eb5f", "path": "ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98850fd26d91b8f27dd107e6c58b87fd46", "name": "Support Files", "path": "../../../../Pods/Target Support Files/permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be8e81c86fd0b6c66f1eeb5e412a24aa", "name": "permission_handler_apple", "path": "../.symlinks/plugins/permission_handler_apple/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9800230b01aa9b9fca969d43e985715890", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98337b34562190009d6ec97992eeb4a4cd", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b77579df6095c616a4adf71284f4f0ca", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9867d57c6fb4d4b54c7ade55b2430eb154", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98067e9baf97a964800a30fb583225ee02", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e387b3da9f0e2ef2abb6c6d4dc93915c", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984efaa88c6a5b78916127be0cdaafb795", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987ef3a2405f7155fff86f7ac08fb7f42e", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eefc9af836a2c31f252ab8493b22cb37", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988a3e1e75911c7fe6713f0e4a0941add1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987d13334f9eddc03290a996c696fcd2a5", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e683974c06d331b56ee2148ee9ceb3e4", "name": "windsurf-project", "path": "windsurf-project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f3a99f936d294fcef3048f7dfc45de48", "name": "CascadeProjects", "path": "CascadeProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98586acca2d5d808355fa0e4d8aa312d45", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9822019c2ca4fce1e909be1aa3da7c027f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988245e723ed4a8ba56843f864e1f7c26c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9806b8dc074055537c05283e124bb181e1", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9861add91899b36570b7a4efdf6a7a1007", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9818e68bcb469292922ee676010d1d488e", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983d50127e2d6697a968950d12cc4f887c", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9851cb602d68b9ad2ac3795c69554e843a", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98542973ec6a7638e5c2e1f2ca279846e6", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f6ffa84c39782edcf391a4b06454b93b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985774d035dbb0c0a47d786b14e14cc27d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988d4058bd25156774b8b8b252451c6c81", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b11bdb8af44c920cdf39c40d57dbbe1", "name": "windsurf-project", "path": "windsurf-project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d01da47786e9f28d4940840fcd7f251d", "name": "CascadeProjects", "path": "CascadeProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986849ff8765e1ffee3af457428c307e58", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98206ddd51201e420523b790460754115d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848a0b01c2c61d1d032291897463a44da", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d0960e96f8013b9bea0a70cf2405ff3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9859f381266910533311c151224b18e7b3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9816f7222922c81560b27506e314c27cf6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f492ea3a890b4b4ca7235f50bbb26c7d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ed9f17c05ec3a6d5ca38fbceb813ee52", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9845040a90fb95b0dcf8b8dd056f2fc223", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e989277b3395f7592b74ef05dcc48ad3d4b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c4a57010c8e32bc9605e02498816e5eb", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9885486c0ec0fd0012dbf4606323aa1c24", "path": "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e981d0f3cc41868756838cd7edbaf571e54", "path": "shared_preferences_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b3fdf5aa3ee6f631b44203a90ab4e30b", "path": "shared_preferences_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981b6fa1e53ab0b016318ca4b63eaae260", "path": "shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9857bd23853949499553bfa6bc16a835d6", "path": "shared_preferences_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9840b9720abdbaed24d47276863866218b", "path": "shared_preferences_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989ffd32628a23ec72f8964127a8727803", "path": "shared_preferences_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f73ef5b723f47a323358c94bc4a13ebb", "path": "shared_preferences_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989916210e6af75e875a8774e82796716e", "name": "Support Files", "path": "../../../../Pods/Target Support Files/shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e67071c30313abe712494e69bf477acd", "name": "shared_preferences_foundation", "path": "../.symlinks/plugins/shared_preferences_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986548455d1fcae91e946be24303df45a9", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/Classes/SignInWithAppleAvailablePlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e9760cc7ed572daad8ae156894508759", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/Classes/SignInWithAppleError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9822925489662aaecaadf71287d4c0a311", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/Classes/SignInWithApplePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c21d5365b074ff3e806851347476162e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/Classes/SignInWithApplePlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98228e55ce3c7c2814553cf0286779389b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/Classes/SignInWithAppleUnavailablePlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981debb9dac35b9b92c82de68e59e83efc", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/Classes/SwiftSignInWithApplePlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9866317dcc24b6aef6d00c20fe631cd6fb", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981866a1be3ade2af58bcb4ecafb3a6095", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a00f5838b2da5ff36a624fdcfccb2e48", "name": "sign_in_with_apple", "path": "sign_in_with_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e8fae74a09a306756aa72aed270d2663", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9858a0e1536aad7a144b149cb3ceb55b92", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c6956f9bb3f6a1d34ec5a0e73a8ec5da", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ecac243ac0e9786d232c1c64592cb50f", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bdee0ab48a9cc80c88f07370922e6b8f", "name": "windsurf-project", "path": "windsurf-project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c9e7c0e39e31e46088f92a8050ec6f68", "name": "CascadeProjects", "path": "CascadeProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cf3e49f2c4251e8f1cc0da10f950ad0e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981ccbf863c247e7e09f52be53b64ce916", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989035e4e0c893a808dc920eee64653d63", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988b380365046fe24f6cf9d094ae125c06", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98862394ee3561ae05551e78f504dca7bc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985f17e06dc00b23cbe10784f5b564a0e0", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98228bf56e468be6127cc3b23e1456c6ea", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98cf748af93d7c35bd67cec55aa4300ac0", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/ios/sign_in_with_apple.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988bfd418537de66a61d0956295eec3325", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9870789988e2d3f86c87d5b48de1a04c97", "path": "sign_in_with_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987a736902ce6c9245ba09a66883b3283f", "path": "sign_in_with_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d3d58782a42bc06f2079ef8a1b768279", "path": "sign_in_with_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c12abd3984fb00a8ef8ecddf372a3156", "path": "sign_in_with_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987193a97003a819da33671cab9bafa479", "path": "sign_in_with_apple-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9882ac04b4d5191a78ec16a21c76cf7ab1", "path": "sign_in_with_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e25e0ec395d2dd8c0442e630b421f84c", "path": "sign_in_with_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9802f8b7668a50b3237c1d3245e72e46fb", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sign_in_with_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9868c7f99b3d6d5e89d9f46596989cecd5", "name": "sign_in_with_apple", "path": "../.symlinks/plugins/sign_in_with_apple/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9898c7f48cd3b19ebc27658596cf34077b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/speech_to_text-7.2.0/darwin/speech_to_text/Sources/speech_to_text/SpeechToTextPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98771f94a520bf8edfba6825f8dcf69279", "name": "speech_to_text", "path": "speech_to_text", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987c9ebbf9ddba3fb67e28fc236c48ab7d", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9899d8be35cdfe50bb1a3d7f0b288ae763", "name": "speech_to_text", "path": "speech_to_text", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987607b3a54238e5287a452fb9188ab6c6", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c496ba163d2d11fda01846adef37f7f4", "name": "speech_to_text", "path": "speech_to_text", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b701d5ac65727ef8acd60ad85b7ee07", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98168f7a019c3670562fca77f8d1c7fddb", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98554b537263759e716eecd1977456f5f8", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d8784c2b64adfeb3a1fbb458e2ec3a94", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985cf628eeb228122e9b3142ec22f33da1", "name": "windsurf-project", "path": "windsurf-project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981093f1367018a6a4381807b2fc086502", "name": "CascadeProjects", "path": "CascadeProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98435a5765410448d6e9a529e40b202b8b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989958ac2551ea720a578b34dd172de444", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988df190345ba3f7db8db08c2ee7c5d355", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9832631e69055116da4717b013a79da536", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98596e11437d8273888e937afc63fd2dde", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984b5bf3ce49f9d4efe9a2d3e7fb7a0382", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980bb5f9592f487722bed8ee88e5621107", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e64379fc8e65013efc8755281de69ddf", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/speech_to_text-7.2.0/darwin/speech_to_text/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9871c3da071541a95cf1ef17da54575cdb", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/speech_to_text-7.2.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98ca6aec443200d491518d4af071db8e46", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/speech_to_text-7.2.0/darwin/speech_to_text.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fecd2063a68a724d528941027902ab15", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98ab034357f7db919ea24209be117b6401", "path": "speech_to_text.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98749d2bd2548c3558e18634e228545a31", "path": "speech_to_text-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982939eae6204948afa7130be2c607beee", "path": "speech_to_text-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b52603d2bdf7d707260a358fb83ca39e", "path": "speech_to_text-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fcdb5a0eec4987fa7181e7331d935718", "path": "speech_to_text-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987ff7ca4f6746588df2df3cf360206393", "path": "speech_to_text.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98feb6216ac849afea04801ea5dc4a1654", "path": "speech_to_text.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989b7f4b7a08ed5514acb56e63beb323fc", "name": "Support Files", "path": "../../../../Pods/Target Support Files/speech_to_text", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9806148501914ded8febac41d03b8f8df3", "name": "speech_to_text", "path": "../.symlinks/plugins/speech_to_text/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98b3c6856e2831598715ae0a933c7551bb", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987e7e683bc7e0ccc51160c60329a441ba", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9892c8a6538ca7d5c6bc4f90defc59ba0e", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982fa60a817716487ed6f89d7b245c87dd", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9826baedf47ed95bd7e816e0e41cbd5c04", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d8aa69857f6da0f17248fd9e67ad92c2", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98322c6972074930e3e0c545894446a3d9", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984a30a0779d2ba1d7206949a0982382b8", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b504028e4cf7494056ed103129b12293", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983f1a61ae1126cf858f7faed963d4fe40", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a91eea4b17e7a41a5d36650713b9f539", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d35a9f60d4a57feca46532bbb042de94", "name": "windsurf-project", "path": "windsurf-project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984bc5ea0a5bd51d4194b1be63cac7547f", "name": "CascadeProjects", "path": "CascadeProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d098c3120deea93fe741af4be9f90d8e", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9889de67d7736a115d9b08b03664b8a09a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/AuthenticationChallengeResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986f7d8edae0df3b1cbddab4d68e38a33c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/AuthenticationChallengeResponseProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9878a285724948c9a2f3abb38c43feb7aa", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ErrorProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985b4ed3ada4d08fb32b833e30486dc059", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FlutterAssetManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9823914bc9caf2dfa20e65c96263a1a61a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FlutterViewFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983a06f32c8969d1244fdc6344606c0068", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FrameInfoProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c176adb35592e85b9ab6beb4a7237e34", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/GetTrustResultResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9813e2de020fa0f6fdb13f907d795494c8", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/GetTrustResultResponseProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987d68cd47cd18f95f792de0a8c4a20276", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/HTTPCookieProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9807355dcdb40edfd857a4deba3b518407", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/HTTPCookieStoreProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983955ea36dde31cd52ca333a72326bf28", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/HTTPURLResponseProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98964f5513cdc4490baf556a45533567aa", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/NavigationActionProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98314dd7f6688875e2260a64684c689281", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/NavigationDelegateProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fb4ce4ae35b1960803e831d77dcc2de9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/NavigationResponseProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981cf9c940f62ea84898f542014886673d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/NSObjectProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cdc788be27b96f18acef94acaabc6201", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/PreferencesProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987da08316920aa35d8ec05742a092971e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ProxyAPIRegistrar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98239b01ed0a9ef19f0a45a44e9dec5e5b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ScriptMessageHandlerProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d9f6091fa43bae3eebb3ab7406dc4ad0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ScriptMessageProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ed3d7ff6ae6d66ae6ddc444d47509a0e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ScrollViewDelegateProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988431fa3e4cca08316cdd682d9d8a577a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ScrollViewProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987d7b56f5fa32741bf73ef13b6a94d566", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/SecCertificateProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984b279211517eb8184f594c48462a97a1", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/SecTrustProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98abd70de1cf8dff78d214983fd4c8c86f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/SecurityOriginProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98998e4ab05e7781ee35a3a968edb7d5f6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/SecWrappers.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a244af008b48226f44300f220b4e3360", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/StructWrappers.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ebe598f68c0ee92b1b08c6624b01eb63", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/UIDelegateProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9849e23a5f063d8bda7027b33f143b99d7", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/UIViewProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b37a91a4ea25703ee40a432b4b32aac5", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLAuthenticationChallengeProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fb77acf30a40dfe81acffa0043929b05", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLCredentialProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e3de7655da7defeaffaef77e8188317c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLProtectionSpaceProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985ecf6fff23f34983888a8d918251d3e0", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c85eb112a79a1394a1a98802dd054ac6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLRequestProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98abfe2a89b21562b5ec1bad3c4c987e73", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/UserContentControllerProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9812495206c73401c6520c267adfd3f614", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/UserScriptProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98981da63a2cac1a601197e8f53317ab49", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebKitLibrary.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b5d05615218c49f2a7b895620b22dcff", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebpagePreferencesProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a6a3cb971843da0e46fdc6d89653510f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebsiteDataStoreProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98672b68023224b61c09ed8dec8aa94c2b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebViewConfigurationProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9810d6e120168f09872140fa84093cb92f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebViewFlutterPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98eab25b3a7ea8c69977a9933839344652", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebViewFlutterWKWebViewExternalAPI.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9817f0edf8120bf247477e2699122b37b2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebViewProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bba48ca00fc05a2da971b55b0b25c745", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9899947b7eab145b01b933e1dd631f613f", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891f531011bb02ec056abe971e5c86522", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cf95db59fb1040fbe365ba9a994e0409", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98387a109bae9110d2f6dbc8f91d6d45cd", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982f23562373a589c390f86168143d679c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e043959e78250bc2fdb7d10d0f7f304d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b7c5b380bfd28a7cb69d338b27ffb43e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98250b838c79a66fa0596997f95070f5fb", "name": "<PERSON><PERSON><PERSON>", "path": "<PERSON><PERSON><PERSON>", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98495e7eb25b6473984793c756bdcc9c16", "name": "windsurf-project", "path": "windsurf-project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b8fe30aed2e2a5de2bb016b5f8905f43", "name": "CascadeProjects", "path": "CascadeProjects", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987f4d0f15f4fab6d019700c8148fcc68c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898e89d4dc37519e85d03b8e2a89f13de", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9804b4f0c3d9d1e4f7f48aa82ee4b5ac24", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982731f1b5284d47e2636df45aabab23bf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fc065dc79ffedfc635e8e547a9c328fb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984cf5f35015e37d952a858dc1de2fa2c9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f23847b7464b76afb9b9dc754b9fc7b9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb068d7e5b1a25bc508f6d53ec9bce87", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e984d0d1034c80ac5f61d344b65b47758ff", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e980484c47f60336abebb01e4c4830ae44d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.1/darwin/webview_flutter_wkwebview.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b5d56a3be3cc22a838029088ee6f54b1", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e980706f19c1ff8937d7abe510f7d3c0ee4", "path": "ResourceBundle-webview_flutter_wkwebview_privacy-webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e980602e57cb1b8dd58ec0a2712d17abcdf", "path": "webview_flutter_wkwebview.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bc303ac01df951a1ee48a993cd764789", "path": "webview_flutter_wkwebview-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989d7cbd8191163378ccbcf82a4d2d685b", "path": "webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ba6da29b9c3325704e6ac34cbc7d3e20", "path": "webview_flutter_wkwebview-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f6def484f860ac7b6978b354817c4980", "path": "webview_flutter_wkwebview-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9850ef3623104befe878f85a1c7080bc87", "path": "webview_flutter_wkwebview.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982ed6eb811d3910de06ef5bec8f50bca9", "path": "webview_flutter_wkwebview.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f727ffd29184fab5dc71e463dfd08f06", "name": "Support Files", "path": "../../../../Pods/Target Support Files/webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986dea6da3491a198c2ba25bfb8655adee", "name": "webview_flutter_wkwebview", "path": "../.symlinks/plugins/webview_flutter_wkwebview/darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fedd0fed0ddd8ff1af54f2413f706788", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98165ba185aaa21f3aacb9684b276fd16d", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreGraphics.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98f982a99b372a3b2ef58d7610104413af", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreText.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98296517d02b240e4d67c025b23edb9e0a", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9851394a1314c14d2c744515f3c3f4bb41", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/LocalAuthentication.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98208a6efa109c783eb29aa0e7202fc65b", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/SafariServices.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9865cff1691db8b2db0bf426be5157fcef", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Security.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e983247acf3f3e06a311417313a336156f4", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/SystemConfiguration.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98afdede430b08af96a0e81afb74f10213", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9807889ca3074139d61644efd803b5ea54", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UnityFramework.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985e6e1dc595ca3acce7a9d2c0ed27a531", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986f57df5597ed36b645cb934c885be56d", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b42354ab64adabee1c75700f63e308c", "path": "Sources/AppAuthCore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988a52192c990c8d6ff3a18ead4d71ffe4", "path": "Sources/AppAuthCore/OIDAuthorizationRequest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fea81be5c1a20e5bf3166575525bd5d0", "path": "Sources/AppAuthCore/OIDAuthorizationRequest.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aa66dcef6a2a76505c110eb1b14afb63", "path": "Sources/AppAuthCore/OIDAuthorizationResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9855deaf209f7ef524e7bd17297c9b38b8", "path": "Sources/AppAuthCore/OIDAuthorizationResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d651f9466a243c475e4b5621c9a381f4", "path": "Sources/AppAuthCore/OIDAuthorizationService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989874cd72943201889d453f8aa802ecfa", "path": "Sources/AppAuthCore/OIDAuthorizationService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9846c06a95fcb31b0a53d00914b301602e", "path": "Sources/AppAuthCore/OIDAuthState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bd3efb93feb9e4577c8609dd57907db1", "path": "Sources/AppAuthCore/OIDAuthState.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f56b5ccdb1600e04574b8735cf87d112", "path": "Sources/AppAuthCore/OIDAuthStateChangeDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985a3e36fc6fae8c23986ca5444912b228", "path": "Sources/AppAuthCore/OIDAuthStateErrorDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cf8fdc57d5b92a02a69655734f42e26a", "path": "Sources/AppAuthCore/OIDClientMetadataParameters.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f67d85bf722690d190868abd4e2d38b5", "path": "Sources/AppAuthCore/OIDClientMetadataParameters.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f5122366bf5ae62c906168e75b2d19b4", "path": "Sources/AppAuthCore/OIDDefines.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980682ebb3cc108d45a5660e430cb9c7a8", "path": "Sources/AppAuthCore/OIDEndSessionRequest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984e153c1c23ea337966d6cbc894d071b8", "path": "Sources/AppAuthCore/OIDEndSessionRequest.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985ea941fc3ca67e69bd58609b81fa16f9", "path": "Sources/AppAuthCore/OIDEndSessionResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d1ed62374ec5357269eb9a14a0b35af0", "path": "Sources/AppAuthCore/OIDEndSessionResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9811c2b1f5e3bf2450d2ae4041befb252e", "path": "Sources/AppAuthCore/OIDError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a3272febb19faeb8c8fbf04b3ec9d0f4", "path": "Sources/AppAuthCore/OIDError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98988d4bebe68fc2f075bb52b36b0cc467", "path": "Sources/AppAuthCore/OIDErrorUtilities.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985318f2d0781fcaa3f5f8ba65cc6e9211", "path": "Sources/AppAuthCore/OIDErrorUtilities.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9866de2d217bb0afc5c477f7e5b8795639", "path": "Sources/AppAuthCore/OIDExternalUserAgent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ec7322ff9dc57c4da873bb3933dfd103", "path": "Sources/AppAuthCore/OIDExternalUserAgentRequest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b6587fc8aa1fa8d11381e3e9b723a48c", "path": "Sources/AppAuthCore/OIDExternalUserAgentSession.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ac9383992f25b3228619e718144e3239", "path": "Sources/AppAuthCore/OIDFieldMapping.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9842cda26c1983a2168be4066f0589fbb0", "path": "Sources/AppAuthCore/OIDFieldMapping.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e96e0219cf4dbfc03603e6666d10a8fc", "path": "Sources/AppAuthCore/OIDGrantTypes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981a2f7bbcffb79e8be85391069c470e96", "path": "Sources/AppAuthCore/OIDGrantTypes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986b5c95f269f7234e7d19ddf55a6cb36f", "path": "Sources/AppAuthCore/OIDIDToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982c13bb7c346b8f04baee79749e79714d", "path": "Sources/AppAuthCore/OIDIDToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98290c25af33b9dd6a0b1c3443a43f6be9", "path": "Sources/AppAuthCore/OIDRegistrationRequest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980e78d4f0c04c6e5febf89f7eb3565787", "path": "Sources/AppAuthCore/OIDRegistrationRequest.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984801ce79efc9db4f6e2b5e1ed69fd6ce", "path": "Sources/AppAuthCore/OIDRegistrationResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9854532006604691868d70fe8a7854c8b1", "path": "Sources/AppAuthCore/OIDRegistrationResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987a218d59fc032aca135e0d85c3f43382", "path": "Sources/AppAuthCore/OIDResponseTypes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9822d02eae01f19038f921ccd2ce0c3538", "path": "Sources/AppAuthCore/OIDResponseTypes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9804cf7ec1ef6a7c46d2ff1ab667cd67c1", "path": "Sources/AppAuthCore/OIDScopes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9862cb83b932f6825218adf80e45cd0026", "path": "Sources/AppAuthCore/OIDScopes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d331c497d2c48a9831df535a03b0c822", "path": "Sources/AppAuthCore/OIDScopeUtilities.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987b9223f7a08875aa400859583bbdc79d", "path": "Sources/AppAuthCore/OIDScopeUtilities.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a2468ee2b6dd6414237dacd08992a40c", "path": "Sources/AppAuthCore/OIDServiceConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9874d90a0e28341ca71d29aa84df00d63b", "path": "Sources/AppAuthCore/OIDServiceConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987517a5728fdba1c6d54c792ae58da89e", "path": "Sources/AppAuthCore/OIDServiceDiscovery.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9800b119d91938de5487bd7b0b791820e6", "path": "Sources/AppAuthCore/OIDServiceDiscovery.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980334ca052f588b733766a81c994b8bbf", "path": "Sources/AppAuthCore/OIDTokenRequest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9891ad1be2f0b96108604eb564280bb6b9", "path": "Sources/AppAuthCore/OIDTokenRequest.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98319e98451162d9d3e8fdc70135c0dcb5", "path": "Sources/AppAuthCore/OIDTokenResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a21a97d0e4f72bef78a85e4c13e67eb3", "path": "Sources/AppAuthCore/OIDTokenResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dce4317e8d61077b94ca6cccfd6fa51b", "path": "Sources/AppAuthCore/OIDTokenUtilities.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98602c8496490b428830be0fb705155f56", "path": "Sources/AppAuthCore/OIDTokenUtilities.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9869823645123195289c03e49011a4e891", "path": "Sources/AppAuthCore/OIDURLQueryComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f19d6cfeabee32608f0854576e8d0b44", "path": "Sources/AppAuthCore/OIDURLQueryComponent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b556082640a6c2e5f69096da90378102", "path": "Sources/AppAuthCore/OIDURLSessionProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98409cfe6beb26350c8db1c03e082ffbbf", "path": "Sources/AppAuthCore/OIDURLSessionProvider.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98db9d710a03fdfd744f218b5ee421c5af", "path": "Sources/AppAuthCore/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9842daed83e4bf9d86bf470df459f23a6c", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eeba98efe6cb1bab60ab47b88d4fe24e", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ad81ecf13a20db9b7971e10dfdaa2518", "path": "Sources/AppAuth.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980cd31b9864092a5622a3f94b77202185", "path": "Sources/AppAuth/iOS/OIDAuthorizationService+IOS.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9820c48a19eb183e2b7b120ee909c098c3", "path": "Sources/AppAuth/iOS/OIDAuthorizationService+IOS.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9892f910743cdfcecd335fc86b820c9064", "path": "Sources/AppAuth/iOS/OIDAuthState+IOS.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dd8690df29db325905bbf28d23e6be4e", "path": "Sources/AppAuth/iOS/OIDAuthState+IOS.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981ecdea5e23b97b6192e5d7c2f5b4e706", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentCatalyst.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98db63f93fd04da4ebc8b3ac97a0070ed8", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentCatalyst.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae5c00ab13e836cbceb0542d073654cd", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentIOS.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9852ebcb8e4afc905823f37dba28bf74d4", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentIOS.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5d4a768e933c0f5992114f158d92a57", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentIOSCustomBrowser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985ad52f3f33706a3e0b7eb6269471b611", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentIOSCustomBrowser.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98375ffe4ee21ad076a79a996bee5cadf1", "name": "ExternalUserAgent", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98689315f3f32580b8e1a88a5258e97b34", "path": "AppAuth.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982228630c88c7dd556f23f738bc0ab053", "path": "AppAuth-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9812c708d2cddc575319e88fb7a88c933b", "path": "AppAuth-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98311f051da2fecd550f719ce7ba443cbe", "path": "AppAuth-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988bd4160792a66b1fb1076ca47f3351fc", "path": "AppAuth-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b3eb747c2fc15dc85f4d4625a6097802", "path": "AppAuth.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9899cf47e5b72f6c1da6428072cecbe53a", "path": "AppAuth.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982ba07cb4cc946406b10706995fe626ad", "path": "ResourceBundle-AppAuthCore_Privacy-AppAuth-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9898af60ba3a57988add2b92ab4c75f00a", "name": "Support Files", "path": "../Target Support Files/AppAuth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982796ef6adda2bbd710a346bf130f44df", "name": "AppAuth", "path": "AppAuth", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872d69b49b50b95d360491ab83555a2c0", "path": "AppCheckCore/Sources/Public/AppCheckCore/AppCheckCore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ccbd673371fdee30f48e2ade19698e34", "path": "AppCheckCore/Sources/AppAttestProvider/DCAppAttestService+GACAppAttestService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a2dbeb2b315619f1e0c1305e344c1aae", "path": "AppCheckCore/Sources/AppAttestProvider/DCAppAttestService+GACAppAttestService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980f0e6d627364d5158ba54eb62e4b5b19", "path": "AppCheckCore/Sources/DeviceCheckProvider/DCDevice+GACDeviceCheckTokenGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9885f065766df74322864c9ffd24366c7e", "path": "AppCheckCore/Sources/DeviceCheckProvider/DCDevice+GACDeviceCheckTokenGenerator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982c27fbfcd67a0ced50a0e7b34ffa905c", "path": "AppCheckCore/Sources/AppAttestProvider/API/GACAppAttestAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9880db49ef4b6439be0f041ecf5bc6faac", "path": "AppCheckCore/Sources/AppAttestProvider/API/GACAppAttestAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b6898b495474f69aaf605ea7f15c74ea", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestArtifactStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a28616b1aa2fff3eb2918490258d268b", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestArtifactStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984007ec40e88dfc6c0b8c283206060126", "path": "AppCheckCore/Sources/AppAttestProvider/API/GACAppAttestAttestationResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983405cd61751edf5cad1cbae41e986868", "path": "AppCheckCore/Sources/AppAttestProvider/API/GACAppAttestAttestationResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98051759764c2fba1d12b938cf202bab32", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestKeyIDStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d13a7df25505e86e703d74429b56a862", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestKeyIDStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989a85ed8d2af83e3fb8f7755406eb1288", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppAttestProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d7f94deb3b76e587463c702e9137d9f5", "path": "AppCheckCore/Sources/AppAttestProvider/GACAppAttestProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b6e4bde45e0cc7a2784dbfbed3f02ee2", "path": "AppCheckCore/Sources/AppAttestProvider/GACAppAttestProviderState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98746a29c098e91f552601ac024b9fd060", "path": "AppCheckCore/Sources/AppAttestProvider/GACAppAttestProviderState.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9866cdf91d5934b61d4f2a803f2e9fc2ff", "path": "AppCheckCore/Sources/AppAttestProvider/Errors/GACAppAttestRejectionError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9845ae5a30dc299c9e8934d185f67fd5d8", "path": "AppCheckCore/Sources/AppAttestProvider/Errors/GACAppAttestRejectionError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98932a04e86fedc7c6f2c0ad91a4b36462", "path": "AppCheckCore/Sources/AppAttestProvider/GACAppAttestService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b03296a2d623790c723ca8823938f8e8", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestStoredArtifact.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98984f09765c9ace51fbeb22a32772fa76", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestStoredArtifact.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9818cb41f9508e367eeba19fc1990635a4", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheck.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981178afc94b0d18c19b91d428dd90e99d", "path": "AppCheckCore/Sources/Core/GACAppCheck.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983e4aa269823d594d7690428d39b35003", "path": "AppCheckCore/Sources/Core/APIService/GACAppCheckAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9840e5d97816ad4acf119ffaec875b4297", "path": "AppCheckCore/Sources/Core/APIService/GACAppCheckAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98be93d9ed1624f079a86dd6709f378e13", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckAvailability.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98be9be5e504e365587b645a6e7c23eb22", "path": "AppCheckCore/Sources/Core/Backoff/GACAppCheckBackoffWrapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98138b306b0e2934282158a9a14875be72", "path": "AppCheckCore/Sources/Core/Backoff/GACAppCheckBackoffWrapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e30dd40dda07898f39be0244d1705826", "path": "AppCheckCore/Sources/Core/Utils/GACAppCheckCryptoUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98807532dd22ee5af51b51974e95e564dd", "path": "AppCheckCore/Sources/Core/Utils/GACAppCheckCryptoUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b981efd21cda9c9cf528cfa0fce51bcb", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckDebugProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98822897e16f69d792c2f3279d50b0141f", "path": "AppCheckCore/Sources/DebugProvider/GACAppCheckDebugProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98110c98fd5912c29a6be66debd9cc7352", "path": "AppCheckCore/Sources/DebugProvider/API/GACAppCheckDebugProviderAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981a8301ea354e4a5c914d14586dfe40ea", "path": "AppCheckCore/Sources/DebugProvider/API/GACAppCheckDebugProviderAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98882770994d8b891034927dacbd3e508e", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckErrors.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9871d67005d7c0c2beec779dfa863170cc", "path": "AppCheckCore/Sources/Core/Errors/GACAppCheckErrors.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c31fe6a0afa3d230897a4250fb96074a", "path": "AppCheckCore/Sources/Core/Errors/GACAppCheckErrorUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f7a69c128b3177c856e7ceceeb8bd2de", "path": "AppCheckCore/Sources/Core/Errors/GACAppCheckErrorUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a4df3ab236637f95515013104621b757", "path": "AppCheckCore/Sources/Core/Errors/GACAppCheckHTTPError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989e7731530eb0b8d45c472147edc4a52b", "path": "AppCheckCore/Sources/Core/Errors/GACAppCheckHTTPError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989b8db4b2a7a6d4d5983bdab12540ad18", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bca7fe1d53100144062aeab166a5ad86", "path": "AppCheckCore/Sources/Core/GACAppCheckLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98756d62700cad637393df1eef90c4be3c", "path": "AppCheckCore/Sources/Core/GACAppCheckLogger+Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a4d8e166bce4fda8ecf64897209e434", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989ff4bbe55b2b25e33773528ef7990e63", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckSettings.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983c46f112a088e45273faf4fb4fc71b04", "path": "AppCheckCore/Sources/Core/GACAppCheckSettings.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983f0ecbe240bd78417a1c59125ba35bae", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985e50b093d7e05965bc11294ecbf2e693", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc840a66412c2adbf116a8d15bcb5b01", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStoredToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9895aefa83654af8f1aa517194d2e61833", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStoredToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98921985600b78de9e9d3351ad0fa939ac", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStoredToken+GACAppCheckToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b26b42fc4c9d88b0596766daa70afd44", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStoredToken+GACAppCheckToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bbd91ebea6574be33575e6307fdf061b", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTimer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e1f66c3fb3993c094f5d197ff71b0c62", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTimer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a9c4611c809c6f32f376799a8665e056", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f5552cc22ed097696ddd83ab40f0e218", "path": "AppCheckCore/Sources/Core/GACAppCheckToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c9333b14c1c8d56c22c880d97d9c96cb", "path": "AppCheckCore/Sources/Core/APIService/GACAppCheckToken+APIResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c2f3c5ffd043e5b6cfefa7b6de19127b", "path": "AppCheckCore/Sources/Core/APIService/GACAppCheckToken+APIResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982798fe01cb589c7d17e6fce4396e1668", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckTokenDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989b49d03eb0a18fdb131db506e7ad0402", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTokenRefresher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a613349e213104252dd89599692050ac", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTokenRefresher.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981dcda69e60e1d9c772e6f7462c703b60", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTokenRefreshResult.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c2c1d561fec47f401917ba9d6d6f6450", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTokenRefreshResult.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ee3f2945d947dea3247d6b02f8b738c4", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckTokenResult.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987988624d2a4fc5f203c32a21dcd69828", "path": "AppCheckCore/Sources/Core/GACAppCheckTokenResult.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ba7fabaf58e492581159e0d0d6b8dc2c", "path": "AppCheckCore/Sources/DeviceCheckProvider/API/GACDeviceCheckAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98973572dde750cf740b90753544d8028a", "path": "AppCheckCore/Sources/DeviceCheckProvider/API/GACDeviceCheckAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98696371a1c08637b01f6c64b2299a9f7d", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACDeviceCheckProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985296225b635be95477c509afc1daf307", "path": "AppCheckCore/Sources/DeviceCheckProvider/GACDeviceCheckProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989957c48ef2ba11e07625eb6bcf2d7e0c", "path": "AppCheckCore/Sources/DeviceCheckProvider/GACDeviceCheckTokenGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cbb7a78b05e4644c7a56e5c842ad289f", "path": "AppCheckCore/Sources/Core/APIService/GACURLSessionDataResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984f96567fd3ab005942c088adb42c5bcd", "path": "AppCheckCore/Sources/Core/APIService/GACURLSessionDataResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981c4b3162fd7bc73a7d13ac169e0ccc33", "path": "AppCheckCore/Sources/Core/APIService/NSURLSession+GACPromises.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98513b53e3546e4d20ffbc0a80e7595405", "path": "AppCheckCore/Sources/Core/APIService/NSURLSession+GACPromises.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e981523cef1ec00f45051328bcde5b265fc", "path": "AppCheckCore.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984d2a743897e36734eec9d6ba63848ca8", "path": "A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9880066059c9108a327947718cffffe93c", "path": "AppCheckCore-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9833d38a9e0056261e1f99519c37209abe", "path": "AppCheckCore-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9897637aa5785a8db0602aeed2c8d0f21f", "path": "AppCheckCore.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9875644b1d37b0a89401ca1abc1b188c7e", "path": "AppCheckCore.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988d17a794cfb166493d3dd17d4d6b466d", "name": "Support Files", "path": "../Target Support Files/AppCheckCore", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cf76f189264d96525f2f7659af2ef74d", "name": "AppCheckCore", "path": "AppCheckCore", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989894172e19a3e4061c8a05a74e1318d7", "path": "Sources/CwlCatchException/CwlCatchException.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984241eeaf6f5dfeb53abca532c8151f49", "path": "CwlCatchException.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989dc8867456bfebc0a0f66add294931cf", "path": "CwlCatchException-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981fe9d5d44db6eb54130c76b605983413", "path": "CwlCatchException-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f0d775aebe81b6fa6c592c944062e1c2", "path": "CwlCatchException-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eee177216e825645557316e79c3387b3", "path": "CwlCatchException-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9871b7aacd89c6891b6f6b8c224fb60694", "path": "CwlCatchException.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98eb16e93594ae9c6905c8c8eab2560cbf", "path": "CwlCatchException.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9808979deb78232725cf16416aa4813a0e", "name": "Support Files", "path": "../Target Support Files/CwlCatchException", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98facb4a87519b4953e05bc4587db2e8e1", "name": "CwlCatchException", "path": "CwlCatchException", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984d171a5dd4ea75d09d68d86f1c94fdfe", "path": "Sources/CwlCatchExceptionSupport/include/CwlCatchException.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9802a0cae3ecc3070fafe51324555984ca", "path": "Sources/CwlCatchExceptionSupport/CwlCatchException.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b59c6e891cca8783b32986f3b4e970a8", "path": "CwlCatchExceptionSupport.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98731c007080a4ff65513e0ef7353e1d88", "path": "CwlCatchExceptionSupport-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98bf0ebd253d7356ba657f3aa7213d400c", "path": "CwlCatchExceptionSupport-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988396f0c5d820d37fe2e11aeafdb9d8ed", "path": "CwlCatchExceptionSupport-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989892fc35afce01a728ca5956919dd109", "path": "CwlCatchExceptionSupport-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9838c02325722d329c8e83ffabafb3bdde", "path": "CwlCatchExceptionSupport.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9814209ef42c831b57b8151ebdc8ba88af", "path": "CwlCatchExceptionSupport.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9853ed674703c18c47cc3ac8cbdc1a699b", "name": "Support Files", "path": "../Target Support Files/CwlCatchExceptionSupport", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af9ac8032baa2e34c4d3089b33feaa91", "name": "CwlCatchExceptionSupport", "path": "CwlCatchExceptionSupport", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9814772343e5c6ba1124c93975b1e0c602", "path": "CoreOnly/Sources/Firebase.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98668b971b18a361bbac16eaa484dd7173", "name": "CoreOnly", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9871b16da1d6c80ffc8ac792478e071871", "path": "Firebase.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985ddcfbc70696a9b93d42edd2d4b057f6", "path": "Firebase.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98359d64b71cf80c922e82364adc46f008", "name": "Support Files", "path": "../Target Support Files/Firebase", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98acd54a4b05e32344cb935a572c0b1d6a", "name": "Firebase", "path": "Firebase", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eb9777a51f8d2db1811311a8d9a27351", "path": "FirebaseAppCheck/Interop/dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98451ca47c295b02ecb0e3b036c2f0fd6c", "path": "FirebaseAppCheck/Interop/Public/FirebaseAppCheckInterop/FIRAppCheckInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f30fd7608cf80b2b38188d0ec0bd1bc2", "path": "FirebaseAppCheck/Interop/Public/FirebaseAppCheckInterop/FIRAppCheckTokenResultInterop.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9877dc8447726290d491db5a23b4353489", "path": "FirebaseAppCheck/Interop/Public/FirebaseAppCheckInterop/FirebaseAppCheckInterop.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9874d2d00874eaa55ef7c7507b3e6c3f54", "path": "FirebaseAppCheckInterop.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e0f907212f7515dbd3dfc4ca4331dfe0", "path": "FirebaseAppCheckInterop-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984f369bb917cb384e3ba497ac8ae4a6c3", "path": "FirebaseAppCheckInterop-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984824bd6fc39747b7ed5c67abd2f39f1a", "path": "FirebaseAppCheckInterop-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98051ea07a958befc9ca4d383668cd48ee", "path": "FirebaseAppCheckInterop-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fe5d52f7369f3c239cebf16612db0b97", "path": "FirebaseAppCheckInterop.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987140ba371620fa85f841605fbd9a18e3", "path": "FirebaseAppCheckInterop.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981addaecc2b1dabf2535dbb8bcdf8ba0c", "name": "Support Files", "path": "../Target Support Files/FirebaseAppCheckInterop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d271527293b7d0611736ed91335f432", "name": "FirebaseAppCheckInterop", "path": "FirebaseAppCheckInterop", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989eafd8493abe74c4d1256753cd0c1761", "path": "FirebaseAuth/Sources/Swift/ActionCode/ActionCodeInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f7cca32533edd683419a15aa8b945b45", "path": "FirebaseAuth/Sources/Swift/ActionCode/ActionCodeOperation.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9871bb94488a6a3fed8e4952a5e10861b2", "path": "FirebaseAuth/Sources/Swift/ActionCode/ActionCodeSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e722aa78cf3019e96c1259c388bf3c99", "path": "FirebaseAuth/Sources/Swift/ActionCode/ActionCodeURL.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a69928e3635a43a0aa30a9acf69de51e", "path": "FirebaseAuth/Sources/Swift/User/AdditionalUserInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983672675845746064979cd59ef21cdd68", "path": "FirebaseAuth/Sources/Swift/Auth/Auth.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9820aa0bbf9c988e88cf87b91939ccce93", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthAPNSToken.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98da5b6f1b3d57abaf38c12d52f1b0980d", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthAPNSTokenManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98771e848ae72b93215014606c894f230d", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthAPNSTokenType.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9834a37b00b199cf85e7fb971a36dae465", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthAppCredential.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980caf825204a8d08f630c75683b9a81c1", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthAppCredentialManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9821aa22bf898e13be8423062123ad765e", "path": "FirebaseAuth/Sources/Swift/Backend/AuthBackend.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98325ad17fc9f542c39e17a7a3f0180271", "path": "FirebaseAuth/Sources/Swift/Backend/AuthBackendRPCIssuer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983a487c16b7a8f8cac584623f0dd5cc57", "path": "FirebaseAuth/Sources/Swift/Auth/AuthComponent.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9850dd960c3a55db110bedc183b346ff70", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthCondition.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f56fb2506a85052ec7ae0668af5e0127", "path": "FirebaseAuth/Sources/Swift/AuthProvider/AuthCredential.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d20676eab38b6b0f20cd4eb277be1321", "path": "FirebaseAuth/Sources/Swift/Auth/AuthDataResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980f866b02a63b59f0f9e6293f2af5176e", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthDefaultUIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9863e803f30a8a0277983f3cbf4345d20e", "path": "FirebaseAuth/Sources/Swift/Auth/AuthDispatcher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98557a03086bd4cdf617629cb7b0855dc6", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthErrors.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98eb55c8daaa7f3d6d1b2c868ec5665720", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthErrorUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980d1939fcda4406336474ef5c671f5be3", "path": "FirebaseAuth/Sources/Swift/Auth/AuthGlobalWorkQueue.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982990b4ce96bd9bc639d800ece8f475f7", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthInternalErrors.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d90a5cab10cd4658bdf21fe764abe562", "path": "FirebaseAuth/Sources/Swift/Storage/AuthKeychainServices.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c4faecced53a932aebe0c736506698c2", "path": "FirebaseAuth/Sources/Swift/Storage/AuthKeychainStorage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987255f9408707e02901e170b478097957", "path": "FirebaseAuth/Sources/Swift/Storage/AuthKeychainStorageReal.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f89700c2f6292d86eff0cc7ec6769e0b", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthLog.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9836435a123480d47b6660ba435a01cb64", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/AuthMFAResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980fc65c3d2064de900bcc3e67d5d13e15", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthNotificationManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986231a9cd9528dcd5cb36c021ba8be533", "path": "FirebaseAuth/Sources/Swift/Auth/AuthOperationType.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98db3c595e692ae949552784159f6e0046", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/AuthProto.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c4805fb44d9e8d34d5a52135e496e070", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/Phone/AuthProtoFinalizeMFAPhoneRequestInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cebae6052faf13ad713398bd1b9d1a9b", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/Phone/AuthProtoFinalizeMFAPhoneResponseInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9875b33cf5db0e1dbc4a71ab6e0a9460e7", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/TOTP/AuthProtoFinalizeMFATOTPEnrollmentRequestInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98499533d97700af8e5fdc4557808b99d3", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/TOTP/AuthProtoFinalizeMFATOTPEnrollmentResponseInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9848ad09b83276d735876b87fdce096aa0", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/TOTP/AuthProtoFinalizeMFATOTPSignInRequestInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dc7b7a3300b85ccbef37679716d63397", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/AuthProtoMFAEnrollment.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98408e8316b7c659a4ced0cab268788581", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/Phone/AuthProtoStartMFAPhoneRequestInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98db66e1762215ebac918ee0468a6c572b", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/Phone/AuthProtoStartMFAPhoneResponseInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c8a8320bc1aae3c29f50f2119757cc8f", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/TOTP/AuthProtoStartMFATOTPEnrollmentRequestInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985c06d45a3ea76dc0fcfda719bba20ce0", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/Proto/TOTP/AuthProtoStartMFATOTPEnrollmentResponseInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983735646329f027da14d686dba7ba9162", "path": "FirebaseAuth/Sources/Swift/AuthProvider/AuthProviderID.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988fff50f9f39cf5e16928531183a751b4", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthRecaptchaVerifier.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ed43f7565c6a1651f418b586fde198ec", "path": "FirebaseAuth/Sources/Swift/Backend/AuthRequestConfiguration.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988d25687bdb0b78d26c35fa3d296c4612", "path": "FirebaseAuth/Sources/Swift/Backend/AuthRPCRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bda31dd613487ad47e6d2420d7dacac1", "path": "FirebaseAuth/Sources/Swift/Backend/AuthRPCResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cb94905c42ddfa8f5ac3b66290403d78", "path": "FirebaseAuth/Sources/Swift/Auth/AuthSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98696a2e00b54da00ed274415bc67740e2", "path": "FirebaseAuth/Sources/Swift/SystemService/AuthStoredUserManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9829882ecca826035db74872b1e45578f7", "path": "FirebaseAuth/Sources/Swift/Auth/AuthTokenResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984284481b22e839a34d1b13ed4ae1991b", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthUIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9891ee64a3b4e38d4c451da6317f6323a9", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthURLPresenter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bd7aedb9314b6f111c5ce51c7d92fa26", "path": "FirebaseAuth/Sources/Swift/Storage/AuthUserDefaults.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985b37924df31764b56f03f0b54bbcaf93", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthWebUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98899e45dba201d92bbc9bb5953ff54cd8", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthWebView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bb01b676e8e4a8d2e2874f9d1f6ded88", "path": "FirebaseAuth/Sources/Swift/Utilities/AuthWebViewController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98597a9d5496e74f6770608203f518c6a4", "path": "FirebaseAuth/Sources/Swift/Base64URLEncodedStringExtension.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98046f4e34b885f6ffe7b7289371eaa92d", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/CreateAuthURIRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984695a968b13bddb36245aa37b4a71989", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/CreateAuthURIResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98eb258beaefad5e269ec1736e42e0428e", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/DeleteAccountRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989c425de534b959695e9e40dcc90324ce", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/DeleteAccountResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980242573c2db4dc22c7304e43753e37e2", "path": "FirebaseAuth/Sources/Swift/AuthProvider/EmailAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a8b139d896d827f66085235e0d5a7627", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/EmailLinkSignInRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982ed07c143ab340204bac41b7071b4eb9", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/EmailLinkSignInResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e6b33452605770cb9a438551c17fa3c0", "path": "FirebaseAuth/Sources/Swift/AuthProvider/FacebookAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f6ad54dd69d29170d53e4b778b9ac83f", "path": "FirebaseAuth/Sources/Swift/AuthProvider/FederatedAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98423edfd82f2cdb348e3128a0146ff052", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Enroll/FinalizeMFAEnrollmentRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987b45d1e0efa4f0159caf64ca46723ded", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Enroll/FinalizeMFAEnrollmentResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9859df394165bc0488c937d9a3fbd8ddf9", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/SignIn/FinalizeMFASignInRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9855bdb983abdff8eb69ae6ef62aa34ea4", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/SignIn/FinalizeMFASignInResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985b7c9b7779081c74fa2b06e2b45fffe2", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRAuth.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9856a8c1534d1af27bc656acfd6575df66", "path": "FirebaseAuth/Sources/ObjC/FIRAuth.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98664ed9a0316897d63a6e52c45233cd32", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRAuthErrors.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d482a851c6464b99bd64771731f98919", "path": "FirebaseAuth/Sources/ObjC/FIRAuthErrorUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989ec90533880a701e978f961c1d10f98c", "path": "FirebaseAuth/Sources/ObjC/FIRAuthProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e8187e96699102511efbeb0916cbe673", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FirebaseAuth.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9883317dfb8798b59250affeb99a764665", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIREmailAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9814eed69c3ea7ebd0cdb4c6b31962acc6", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRFacebookAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c58a3eeb0ce48da860c8e77bf73d976d", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRFederatedAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b4aa216a28d16b66448de1a2a49725c8", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRGameCenterAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98204c9714f44bb6aa610335fb2061e609", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRGitHubAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bb5d4fceb628f47d250b0c87a757656a", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRGoogleAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9863fe571a813cb17038ae1234fdde6951", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRMultiFactor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f609c5713cfc815317d0ff3a36df456b", "path": "FirebaseAuth/Sources/ObjC/FIRMultiFactorConstants.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e6543f9e69a464b645c214758820da7b", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRPhoneAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e50eab9f18a38d69b1ee3d78e53060df", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRTwitterAuthProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e4c66ac2957918d0a2b42a10a47fa415", "path": "FirebaseAuth/Sources/Public/FirebaseAuth/FIRUser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988d6b1acfcb16310d3533575cde9f8f90", "path": "FirebaseAuth/Sources/Swift/AuthProvider/GameCenterAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986eea2203234aa08325c6edd08cfc5a61", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetAccountInfoRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981959ba2f426549abe5215289860ce07f", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetAccountInfoResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98acdfbcae428bc4c7c00091360370d238", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetOOBConfirmationCodeRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987dd375b0af293aa5e1eede2ff71d768f", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetOOBConfirmationCodeResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9832c4a881108b3b1b059d839f1a3690b5", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetProjectConfigRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9878832e23431edbd3bcdc807305827a82", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetProjectConfigResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9826aa9101174f6739d08b2bea19db1f74", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetRecaptchaConfigRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f71037b4b311854b37496d8f3ef9c118", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/GetRecaptchaConfigResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98897c9546c495535cb29623ede9983ac7", "path": "FirebaseAuth/Sources/Swift/AuthProvider/GitHubAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f6d718acb67baed26edff5e3c5bd20dc", "path": "FirebaseAuth/Sources/Swift/AuthProvider/GoogleAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9899abbcbbf85d3d0ac21322423f73a6c2", "path": "FirebaseAuth/Sources/Swift/Backend/IdentityToolkitRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d675445dac1c63b4b8faac1e44a5bf11", "path": "FirebaseAuth/Sources/Swift/MultiFactor/MultiFactor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dd60887c889fa4882fd6fd19cd4579ae", "path": "FirebaseAuth/Sources/Swift/MultiFactor/MultiFactorAssertion.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9852c851508516f4056144db510638ac35", "path": "FirebaseAuth/Sources/Swift/MultiFactor/MultiFactorInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980f0d2244bc47590a31965317fd39031d", "path": "FirebaseAuth/Sources/Swift/MultiFactor/MultiFactorResolver.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9886474f723e6afe5d3740e05b25bd5249", "path": "FirebaseAuth/Sources/Swift/MultiFactor/MultiFactorSession.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9857295faa31bda91c9a799672c902fa72", "path": "FirebaseAuth/Sources/Swift/AuthProvider/OAuthCredential.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f29091c9457feb309b1a1db93b09ebf4", "path": "FirebaseAuth/Sources/Swift/AuthProvider/OAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f82e3188b58647e0d58ed6e30200ba28", "path": "FirebaseAuth/Sources/Swift/AuthProvider/PhoneAuthCredential.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d3ee17be59e4592c7c2b060c7b111fdd", "path": "FirebaseAuth/Sources/Swift/AuthProvider/PhoneAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983eb85be942303d0f00316677fced4ec6", "path": "FirebaseAuth/Sources/Swift/MultiFactor/Phone/PhoneMultiFactorAssertion.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983301fcfb20ed7f7833f35ce78a58bfab", "path": "FirebaseAuth/Sources/Swift/MultiFactor/Phone/PhoneMultiFactorGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fcce1deef3c5cd46feb16920cfba5f2c", "path": "FirebaseAuth/Sources/Swift/MultiFactor/Phone/PhoneMultiFactorInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b4bb4fb55c584d23451ee413fe0ddd37", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/ResetPasswordRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981f6aa94d89271fde5f144ae099f2d20e", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/ResetPasswordResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f87b483720677ba00075ab32f5dc3fc8", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/RevokeTokenRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f3c39769cab838f28ff2af32356658a4", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/RevokeTokenResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984abb925def4484b907c527e0b08ccac7", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SecureTokenRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988c4c8f673d50738e7f3c3b3afd029114", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SecureTokenResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981f81e8167b09ec063d8feac68adf8e6c", "path": "FirebaseAuth/Sources/Swift/SystemService/SecureTokenService.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e941a01f121ca47353c9829992b76794", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SendVerificationTokenRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98757823f92103164bbb4aa3faff547298", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SendVerificationTokenResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9820b2e6f4ab0a9f37a0da4c7031c29ba8", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SetAccountInfoRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c4231b62e0f4df467c362a0a8aa0598f", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SetAccountInfoResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989fa77ca34f08434a9b3f02fbe29fa469", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SignInWithGameCenterRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98eeed3b457b919d9cbcc3dbe59fdf93af", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SignInWithGameCenterResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f38747265fae15de5ce023569d6884b4", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SignUpNewUserRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9887cea1656d46651bf19f6d0746940cd2", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/SignUpNewUserResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981c916f5168a5989955cea8d2b2a631d5", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Enroll/StartMFAEnrollmentRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98904627ac0769c2eb3d05e9b88ac70139", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Enroll/StartMFAEnrollmentResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981939dc93c0a57274d42669173f77e64e", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/SignIn/StartMFASignInRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980153d75831dcff3303605c72cdb44e52", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/SignIn/StartMFASignInResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98480955d5207889dcaba9e529a4686101", "path": "FirebaseAuth/Sources/Swift/MultiFactor/TOTP/TOTPMultFactorAssertion.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98383508f50c1d28e0058fcf953abef05e", "path": "FirebaseAuth/Sources/Swift/MultiFactor/TOTP/TOTPMultiFactorGenerator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9833d91e12573a67361d3c85dda1da9298", "path": "FirebaseAuth/Sources/Swift/MultiFactor/TOTP/TOTPMultiFactorInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b9e24989adcbca6da54b19f4fb7d3e56", "path": "FirebaseAuth/Sources/Swift/MultiFactor/TOTP/TOTPSecret.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ec286cb77d6a0961569e735555112aaf", "path": "FirebaseAuth/Sources/Swift/AuthProvider/TwitterAuthProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982b319527f873103d2ba43eb5bf42e745", "path": "FirebaseAuth/Sources/Swift/User/User.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c9c5778334c97a91259fc47469d0b67c", "path": "FirebaseAuth/Sources/Swift/User/UserInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983757085b86a6aa7b53db399d0039c2e7", "path": "FirebaseAuth/Sources/Swift/User/UserInfoImpl.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e5588ca85f20e48d1eab231c7c5fca65", "path": "FirebaseAuth/Sources/Swift/User/UserMetadata.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f7eb301a80d4a684935a48b8240378ba", "path": "FirebaseAuth/Sources/Swift/User/UserProfileChangeRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f52932b5cb83448077c31c42576b5397", "path": "FirebaseAuth/Sources/Swift/User/UserProfileUpdate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985d14c53bd6000d96f45bf6436f066822", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyAssertionRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989c0e170974e3d9c39590554bfde190d3", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyAssertionResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fed3fe0b4ef633b7a02ad258398c796e", "path": "FirebaseAuth/Sources/Swift/Backend/VerifyClientRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9827dd2ae5aeeb60a85ee5e5ca39153bdf", "path": "FirebaseAuth/Sources/Swift/Backend/VerifyClientResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982c7434f9b043d49b700bbe40e433f99e", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyCustomTokenRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989b5083154dbf0f42da935c66a88fe087", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyCustomTokenResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986284397e4862851282304a3d0096a55c", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyPasswordRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98565ed54f064ffea7657455d99e69b3fd", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyPasswordResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98297a833da25b5908984d75e6c56c7042", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyPhoneNumberRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c624687d6ba3087675d8d688f050d78c", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/VerifyPhoneNumberResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9816213c37a5d37d40c9f0125e2f30abd8", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Unenroll/WithdrawMFARequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d059665a3d4a3e48b694e561379b317b", "path": "FirebaseAuth/Sources/Swift/Backend/RPC/MultiFactor/Unenroll/WithdrawMFAResponse.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9833879b51238824cd85cdfa1b283e150d", "path": "FirebaseAuth/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9812007fe9b554b4a7f315601dab4dcb90", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986d10170a5e13b7a3a034a7e82ea3da9e", "path": "FirebaseAuth.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985437b795d308c9b398a5bfafd309b915", "path": "FirebaseAuth-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98fa30da6088c8cbb65b4c1d300fc8f381", "path": "FirebaseAuth-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981cad1d7dcee78c8060ce51d470519d79", "path": "FirebaseAuth-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9867a708e6e57a9779ffd6551ad44fd72a", "path": "FirebaseAuth.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f334345a162861a6a80b2fe0a5c9b350", "path": "FirebaseAuth.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ebe3781c3347e522c4612375fd3db8e8", "path": "ResourceBundle-FirebaseAuth_Privacy-FirebaseAuth-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b72ace1c4447a0de9169e3e1a1f86da1", "name": "Support Files", "path": "../Target Support Files/FirebaseAuth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9874c8d3c25f5a55bdd847faf2dd27b984", "name": "FirebaseAuth", "path": "FirebaseAuth", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bbd85cbf25cba25975fd97f71dd6db7f", "path": "FirebaseAuth/Interop/dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d0e94f46eb0e6e2a6c1cc9f1557baaf0", "path": "FirebaseAuth/Interop/Public/FirebaseAuthInterop/FIRAuthInterop.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9814ec2c9a90e6a7044ab3c681f37f399d", "path": "FirebaseAuthInterop.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9853dcc31425552e37f00a369290dcc584", "path": "FirebaseAuthInterop-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987783d129afecb80354ba713fa330e79c", "path": "FirebaseAuthInterop-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9883349f89dac91e16ac1fcdd68f459c7f", "path": "FirebaseAuthInterop-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a86b7923bd31fce0cca385e9034d57b", "path": "FirebaseAuthInterop-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983cb9bc283c9ddd80fe5a3e782d432d58", "path": "FirebaseAuthInterop.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e985d3cf3347bc84309aacd7a322be8b02e", "path": "FirebaseAuthInterop.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987f9bc73f6b870af3912a65e9f4d3404e", "name": "Support Files", "path": "../Target Support Files/FirebaseAuthInterop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4a2bded6140948c9626412dc08d1a2d", "name": "FirebaseAuthInterop", "path": "FirebaseAuthInterop", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f49ead291861b866cfa216c8afa1bfee", "path": "FirebaseCore/Sources/FIRAnalyticsConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980058af2e2280a71efaa9b3436252b92f", "path": "FirebaseCore/Sources/FIRAnalyticsConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986b83c13e2eeeb74a9903a5527af427aa", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRApp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985dc1568f7a32df27af5653329bbef119", "path": "FirebaseCore/Sources/FIRApp.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982790fd5eeb16dbd8589a4f2886c9c5e2", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9818441d51f50b17f6caa8c89e4e783270", "path": "FirebaseCore/Sources/FIRBundleUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989770ebdc11e5fb8ac7b2c400303c4cb0", "path": "FirebaseCore/Sources/FIRBundleUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987024dfadf930c5eb38de77915298767c", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f5db57dbaf6ddd7740ab11322513d5f7", "path": "FirebaseCore/Sources/FIRComponent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985f492897e708de64d34933a1314f922e", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98173df63c04416e19079386ae1c0272ed", "path": "FirebaseCore/Sources/FIRComponentContainer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986cbefac13fa50eed514df4b8179017d8", "path": "FirebaseCore/Sources/FIRComponentContainerInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987fb56b6493f53cffca5147543fd53da6", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e1c43233826a7d648bc76858a782b89c", "path": "FirebaseCore/Sources/FIRComponentType.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98513e25ae702c3a175336de3eaa942917", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eae7003eb03e425bfa23456220c03ec8", "path": "FirebaseCore/Sources/FIRConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d8f57d268c125ca715a0a5a277ae77fe", "path": "FirebaseCore/Sources/FIRConfigurationInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989782afa8b17ce0dab1cf9ac164d9c92a", "path": "FirebaseCore/Sources/Public/FirebaseCore/FirebaseCore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ec4801702c4cfc7680ab51f6858bb600", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9840a053cbeecd541c44b85902fd4e2c34", "path": "FirebaseCore/Sources/FIRFirebaseUserAgent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eb0a42c0bd220179bbc6171a64717266", "path": "FirebaseCore/Sources/FIRFirebaseUserAgent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9828f0ac9940ac5568f87e6c7cefcc24ae", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985da1c667d66764a2bae9f8671de90ad9", "path": "FirebaseCore/Sources/FIRHeartbeatLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989047545b037a419fc2eaf1306b96c355", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98215eb6de5be1e808772fecf2052a6549", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9849036b3095f3d406f69961f435da53fa", "path": "FirebaseCore/Sources/FIRLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e57bbe1bf152b5f4a302e9d577ce0c09", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRLoggerLevel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980b07f5c9115249f8be0fb890d9251517", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIROptions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e7fcc00b1f7c1c038befb31efd2d7c21", "path": "FirebaseCore/Sources/FIROptions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bd08256ec6f23e9aaeeb2adbd0511507", "path": "FirebaseCore/Sources/FIROptionsInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985185204a1647a286a6033c8f425ea3d0", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRTimestamp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e7c7f309919f0f9dd932ed5e71688e43", "path": "FirebaseCore/Sources/FIRTimestamp.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9834f760defb451a10d2e1e1ae2bc12f2e", "path": "FirebaseCore/Sources/FIRTimestampInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9802fca0d1c798729e67269b15a6ba67e0", "path": "FirebaseCore/Sources/Public/FirebaseCore/FIRVersion.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981d1234f241e54e93d6f63763279d8958", "path": "FirebaseCore/Sources/FIRVersion.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9882c20812de26535ca459ff2e0860992d", "path": "FirebaseCore/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d68a2519ff62ed57c233ae66976118b8", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9801cbc7f94dcaacf63c7e0a579e10d046", "path": "FirebaseCore.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98edf38acde4f16971a9e532f1edc590b1", "path": "FirebaseCore-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9804251e62f22a08bbca0a2bb4b807fa78", "path": "FirebaseCore-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984e4e050321b17df3557057e022108c65", "path": "FirebaseCore-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98472d0cae33e9ae0b6bc1f79b35f11277", "path": "FirebaseCore.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9887dbb5b6aa53c391d48cd209f3b2e62e", "path": "FirebaseCore.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98198fb1e4d5a7e8c9559a89ceb97ae1c7", "path": "ResourceBundle-FirebaseCore_Privacy-FirebaseCore-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ee79f3265d9b87dbca658bec459df25c", "name": "Support Files", "path": "../Target Support Files/FirebaseCore", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98599af9df1d91bd2315ca001a6eec7f1e", "name": "FirebaseCore", "path": "FirebaseCore", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988e608e77ff1f0ad417abfb8f9ef904e1", "path": "FirebaseCore/Extension/dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc5a3c57e4b7e1802beb383ed3508e32", "path": "FirebaseCore/Extension/FIRAppInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bf0621f5c39bcece9463fc83e754d2b1", "path": "FirebaseCore/Extension/FIRComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9842cf37b04ce02613b35dae370a3b2ccf", "path": "FirebaseCore/Extension/FIRComponentContainer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9856abe809f5008db82b84b62c2103958d", "path": "FirebaseCore/Extension/FIRComponentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98368572dcf1f79f8a5b1f938dbfd9d17c", "path": "FirebaseCore/Extension/FirebaseCoreInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985bc197ee4f991d035c9baf8cfcc6d6c1", "path": "FirebaseCore/Extension/FIRHeartbeatLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f0711c66e38982e8ccd91027de07d020", "path": "FirebaseCore/Extension/FIRLibrary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fdcafa49c5e43abf4deda449df7ea27a", "path": "FirebaseCore/Extension/FIRLogger.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9864f62bc7f3065e91596874c9d24f2a05", "path": "FirebaseCore/Extension/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cb2e252a514a4c68428158af08297ac1", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98b69db9c7a64a28f1945364a6903f0189", "path": "FirebaseCoreExtension.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988d09afc4356e0275b1412f7b30f03fc8", "path": "FirebaseCoreExtension-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b5f091092e572132ae9329c5ed2ff6b6", "path": "FirebaseCoreExtension-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982af26b92625d8064012c2cf026a36028", "path": "FirebaseCoreExtension-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b36401bd143bdf0da7f475f30c05eea", "path": "FirebaseCoreExtension-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980aa7893a37e282de35c2c9fe4a0b4414", "path": "FirebaseCoreExtension.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983825f64b079f671a1822e4082f95c8bf", "path": "FirebaseCoreExtension.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98a199404bf60fd718a9a29e16a669ebfc", "path": "ResourceBundle-FirebaseCoreExtension_Privacy-FirebaseCoreExtension-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a679c7a829e8c430953563aef3f0134d", "name": "Support Files", "path": "../Target Support Files/FirebaseCoreExtension", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ad694e16b33b83966fcd2433b8c9eba9", "name": "FirebaseCoreExtension", "path": "FirebaseCoreExtension", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984f66626b13423502e670596aa2457653", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/_ObjC_HeartbeatController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9858f27a581dba0fced5eb98200315fee5", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/_ObjC_HeartbeatsPayload.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98718ba6b268c00df292d0aa4847bf4570", "path": "FirebaseCore/Internal/Sources/Utilities/AtomicBox.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984873568971bca1683e52859b9c1f14f1", "path": "FirebaseCore/Internal/Sources/Utilities/FIRAllocatedUnfairLock.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98897654c22ff26eda768c4a6f483b0da2", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/Heartbeat.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9873dd841e2a7a56165ef3984c1889b5fb", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98aa5186469cd626d9030badd3ec0e9c92", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatLoggingTestUtils.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985d2d1e8a9a423a9f27614f99fdd6e60c", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatsBundle.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ce041390fa5dcbc2a5ecafacb8cb2d2a", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatsPayload.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e72a29710e24032957cc82499159899f", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/HeartbeatStorage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987ac9aab88db1a5da95981edd0c407c09", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/RingBuffer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989e75cc2c1d2028336b5365d4d0a14e1f", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/Storage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ccdf08c0cafde78cee673765bde8424f", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/StorageFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c25476ee780af7c38cab3c3d9d669198", "path": "FirebaseCore/Internal/Sources/HeartbeatLogging/WeakContainer.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9830aac463a58a8526d80737df4acd431c", "path": "FirebaseCore/Internal/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980044acd8cb46dd390a875c5ce8074994", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e987ca92f34b0f4f9a2e7fc622f47b81755", "path": "FirebaseCoreInternal.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b32bd1bd37b46495e7f818359eee4aa0", "path": "FirebaseCoreInternal-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98aad423710b61bd1846df31a66857ce23", "path": "FirebaseCoreInternal-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f9fd6e9f1a2a9ab3934af9c82ff6acb7", "path": "FirebaseCoreInternal-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98084b2d4c84d915e69bf3c494788c6bd6", "path": "FirebaseCoreInternal-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9876b703835f5d4ca28909a31f348c9b7f", "path": "FirebaseCoreInternal.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98caf54c53db871dcfb0e2a947ce155248", "path": "FirebaseCoreInternal.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f7b9625368ab9660181f6273284cb62a", "path": "ResourceBundle-FirebaseCoreInternal_Privacy-FirebaseCoreInternal-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982162131913f5031a66347ee845dd679c", "name": "Support Files", "path": "../Target Support Files/FirebaseCoreInternal", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981116a5ee97fe6787a0abefc1b2f87132", "name": "FirebaseCoreInternal", "path": "FirebaseCoreInternal", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98522cf4240330d81b38356ff5ea68b11c", "path": "GoogleSignIn/Sources/GIDAppCheck/UI/GIDActivityIndicatorViewController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fb3884ab1d18e8797270b71d15d6439f", "path": "GoogleSignIn/Sources/GIDAppCheck/UI/GIDActivityIndicatorViewController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9873b7a2b9790493732d14d107a7c79ace", "path": "GoogleSignIn/Sources/GIDAppCheck/Implementations/GIDAppCheck.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983995edbd12d3ae03346028c56e5ba886", "path": "GoogleSignIn/Sources/GIDAppCheck/Implementations/GIDAppCheck.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9815065173466a4f79dac6f5cac52ad7c5", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDAppCheckError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9885576fc68664556b575f551209d41571", "path": "GoogleSignIn/Sources/GIDAppCheck/Implementations/Fake/GIDAppCheckProviderFake.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982b639056ac566084cafaa30cc158774f", "path": "GoogleSignIn/Sources/GIDAppCheck/Implementations/Fake/GIDAppCheckProviderFake.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98171a5f9149a4c33ba88ae6970efb2eb8", "path": "GoogleSignIn/Sources/GIDAuthentication.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981067db2195b174dd3c69cd30af3cd1cf", "path": "GoogleSignIn/Sources/GIDAuthentication.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fed8d05c957e810e97fa27e0b1c86951", "path": "GoogleSignIn/Sources/GIDAuthStateMigration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c7d245c9c3344d890f6a2f05c6fbdbb3", "path": "GoogleSignIn/Sources/GIDAuthStateMigration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985ee6e15efc1af15f0ca3c02465205345", "path": "GoogleSignIn/Sources/GIDCallbackQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98362ae8c3869cdedafb3d7a5e4829ec7c", "path": "GoogleSignIn/Sources/GIDCallbackQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9810fc95ccc1d3c358244c45f8040d728f", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b6633d0de634119e12eb5720119e776f", "path": "GoogleSignIn/Sources/GIDConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c05263b76a8f4e96386cb0742a44fe65", "path": "GoogleSignIn/Sources/GIDEMMErrorHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d0ab38ea4f339f26d5447a0282ba3e9b", "path": "GoogleSignIn/Sources/GIDEMMErrorHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98efffa7ff10e50004de734c1d79694093", "path": "GoogleSignIn/Sources/GIDEMMSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980f681efc572dc6c480cef76c37ea067e", "path": "GoogleSignIn/Sources/GIDEMMSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9805f7775ec83a4f448640c8f223610cd1", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDGoogleUser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98375cadeb5808cff2f34d97ea8edad918", "path": "GoogleSignIn/Sources/GIDGoogleUser.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d26a82a515098a6ffcc77031f3928bf4", "path": "GoogleSignIn/Sources/GIDGoogleUser_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d518ca8e6233bcf2d4b371e88f99c6d0", "path": "GoogleSignIn/Sources/GIDMDMPasscodeCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9887847ce510190bb3401f3c79cf55db64", "path": "GoogleSignIn/Sources/GIDMDMPasscodeCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9807c746212a1b079d17a24398ff4ab496", "path": "GoogleSignIn/Sources/GIDMDMPasscodeState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98819fcc44472c00d1c3660d22d1878cbf", "path": "GoogleSignIn/Sources/GIDMDMPasscodeState.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98742601ce8f14f983bf167cd9ace1dcdf", "path": "GoogleSignIn/Sources/GIDMDMPasscodeState_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9859246742af0c42913ddff99c69e0a25b", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDProfileData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986d2b443f44f9774eb51c7292f2cb493b", "path": "GoogleSignIn/Sources/GIDProfileData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9860f5ee2a833416883eaf14f315ae7ab4", "path": "GoogleSignIn/Sources/GIDProfileData_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c6beb8ab8c97d6f7df15e888fc50983c", "path": "GoogleSignIn/Sources/GIDScopes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988e30cd963ed5e6d15ab7c8d4fea073be", "path": "GoogleSignIn/Sources/GIDScopes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dc50131d9ca60b9b969763ecd97742a0", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDSignIn.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f48f6fc95d3a2df82de62b337c4689f0", "path": "GoogleSignIn/Sources/GIDSignIn.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985b8aa1472b42fc70cd489582c5774159", "path": "GoogleSignIn/Sources/GIDSignIn_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f5046d0e5bdcb96a56e45cd4efc5e06c", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDSignInButton.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98024704438c81b9170b01fe6106ac5033", "path": "GoogleSignIn/Sources/GIDSignInButton.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989e4909702eab13a0b8a159a907f4bc57", "path": "GoogleSignIn/Sources/GIDSignInCallbackSchemes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e72a07b5e49e157d3afb7a25e3159567", "path": "GoogleSignIn/Sources/GIDSignInCallbackSchemes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1e923625b2a4b8efc5ce26953d8c82e", "path": "GoogleSignIn/Sources/GIDSignInInternalOptions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987ddf0a56a4df089c21c080fc544ba423", "path": "GoogleSignIn/Sources/GIDSignInInternalOptions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98392a0fcf592a2bbe1ed936c41d5f89ac", "path": "GoogleSignIn/Sources/GIDSignInPreferences.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9897ec918337b7c1df871e3f73ba357831", "path": "GoogleSignIn/Sources/GIDSignInPreferences.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984bdb59eb51f8a7a46120c65f1d1fb2c8", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDSignInResult.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9872bb4b4debd8e47c0e0705517975fc55", "path": "GoogleSignIn/Sources/GIDSignInResult.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c376e0ab17746364af865e30f84e97fa", "path": "GoogleSignIn/Sources/GIDSignInResult_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9848bce6c5643bb63cff47767030badf46", "path": "GoogleSignIn/Sources/GIDSignInStrings.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9883172bdda2cdf340a90a0678d91dc0a7", "path": "GoogleSignIn/Sources/GIDSignInStrings.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cd8cc8724846e326de1910ce2bef0f01", "path": "GoogleSignIn/Sources/GIDTimedLoader/GIDTimedLoader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fa2d9a844d1a41802744749869c3d33c", "path": "GoogleSignIn/Sources/GIDTimedLoader/GIDTimedLoader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ad5480ef8fb8310efbc62b2259f44fc1", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c8a6dfff0df7432036af780c2d546c3c", "path": "GoogleSignIn/Sources/GIDToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d9d17d4678ef8d8a54ebdbcdff1cc6a1", "path": "GoogleSignIn/Sources/GIDToken_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a70ce92bf003551e2b5a7c7ea9785616", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GoogleSignIn.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9871967e3fb50ec1519d5f0e37fdb63ffa", "path": "GoogleSignIn/Sources/NSBundle+GID3PAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9899ddafbe7cdc133a298402b32cbd2a4b", "path": "GoogleSignIn/Sources/NSBundle+GID3PAdditions.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9813d39c278ac4305ce88c3c241a214f6d", "path": "GoogleSignIn/Sources/Strings/ar.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e989a0f5ac68e1763f5c2e462e2c1fb3606", "path": "GoogleSignIn/Sources/Strings/ca.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98f34e015157fa7b5083ad10360410fa2f", "path": "GoogleSignIn/Sources/Strings/cs.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98054304b5b64c8fdf99511abd91a4d062", "path": "GoogleSignIn/Sources/Strings/da.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9873082981b29e7fb4af7e320be56acb33", "path": "GoogleSignIn/Sources/Strings/de.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98265d0f9a0164242d948fd65154569f13", "path": "GoogleSignIn/Sources/Strings/el.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e986a59d962ad683b2db652d7c1bc21c321", "path": "GoogleSignIn/Sources/Strings/en.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9877bfeebc4cac1e04a8f00ddf4e474f4a", "path": "GoogleSignIn/Sources/Strings/en_GB.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98d8569de8ca37c2f614397be852d4633f", "path": "GoogleSignIn/Sources/Strings/es.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e988ecfb265fa5edb8143d80c61deea6569", "path": "GoogleSignIn/Sources/Strings/es_MX.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9856b8cafe80afdc04856020284290b790", "path": "GoogleSignIn/Sources/Strings/fi.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98cb024c06b116fa1bab1b1788171e546c", "path": "GoogleSignIn/Sources/Strings/fr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e981919c36091ed2fd7d9f12f6ef1bf853a", "path": "GoogleSignIn/Sources/Strings/fr_CA.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "bfdfe7dc352907fc980b868725387e98af47b2ed83fc06e848e503a8ec03d494", "path": "GoogleSignIn/Sources/Resources/google.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "bfdfe7dc352907fc980b868725387e9867d95d488d8275d7a2d624e1b88f068c", "path": "GoogleSignIn/Sources/Resources/<EMAIL>", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "bfdfe7dc352907fc980b868725387e980f5c9331b77810a29ab44e24036a3b4b", "path": "GoogleSignIn/Sources/Resources/<EMAIL>", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9868b721f4b39a17df2c8d79d953d980c9", "path": "GoogleSignIn/Sources/Strings/he.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98a06b7329c8fcc6af1b3ddb092203a7fb", "path": "GoogleSignIn/Sources/Strings/hi.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98faec66631238a3552a418221f8b71557", "path": "GoogleSignIn/Sources/Strings/hr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98dc914818dca1c6296cc46ce73e57d668", "path": "GoogleSignIn/Sources/Strings/hu.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e983123e27e9562683d76edb1d646389154", "path": "GoogleSignIn/Sources/Strings/id.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98196039eeccf3927e881f631660cc3a38", "path": "GoogleSignIn/Sources/Strings/it.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9833cbaee37d782df9102e940fa5a47ae2", "path": "GoogleSignIn/Sources/Strings/ja.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98d1e43c0a643175914b5c4bfa203e2cf8", "path": "GoogleSignIn/Sources/Strings/ko.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98f69db74b1559c2faf4e183e5f3972688", "path": "GoogleSignIn/Sources/Strings/ms.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e980ca4202380d68d388a7697ea807ee1d9", "path": "GoogleSignIn/Sources/Strings/nb.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9882c2efa0f17e949d148cef1eb0219179", "path": "GoogleSignIn/Sources/Strings/nl.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e987faa1f94384d8b56128ff7f49d19def2", "path": "GoogleSignIn/Sources/Strings/pl.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98b58fe2d01878f8a52be7267ed96345d1", "path": "GoogleSignIn/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9842816022c2e989e3bb58cbf3112d4aed", "path": "GoogleSignIn/Sources/Strings/pt.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98471719b9f43ff5845ed2590f151b422e", "path": "GoogleSignIn/Sources/Strings/pt_BR.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e982a4fb33a417d6d93d0267eafa3310217", "path": "GoogleSignIn/Sources/Strings/pt_PT.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e987177ed68a59336e08cae183486f0d045", "path": "GoogleSignIn/Sources/Strings/ro.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "file", "guid": "bfdfe7dc352907fc980b868725387e988f170515eac9f23e702a850f0351d753", "path": "GoogleSignIn/Sources/Resources/Roboto-Bold.ttf", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98bd4020681fe4dea8c7826db0a03c01fd", "path": "GoogleSignIn/Sources/Strings/ru.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98663bc2d24d0ba784c0ce29774b66bf2b", "path": "GoogleSignIn/Sources/Strings/sk.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e983a920250163075874f2e331aa858190c", "path": "GoogleSignIn/Sources/Strings/sv.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9806d3153a165881ef6431a290d4e95221", "path": "GoogleSignIn/Sources/Strings/th.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98ddce3ff24a9a12cdfbcbdd0434ba1227", "path": "GoogleSignIn/Sources/Strings/tr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98da7f12fd40f7dd1999e7ec48d1386f10", "path": "GoogleSignIn/Sources/Strings/uk.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98914b23ea9fbfef7f36898775728f3a66", "path": "GoogleSignIn/Sources/Strings/vi.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9832e34942064f870d94bea8e9ff66c97a", "path": "GoogleSignIn/Sources/Strings/zh_CN.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e987fb566464c1a903628209683f78b95f5", "path": "GoogleSignIn/Sources/Strings/zh_TW.lproj", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bd63b2df6db8e67ad0f93818beeaf094", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e989551cbba692cbb7f1e38348e311ce8a3", "path": "GoogleSignIn.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e186557fcea992b74c10351e1ff20c3a", "path": "GoogleSignIn-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981def8dcef8da3286a76b48d697fb51ee", "path": "GoogleSignIn-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d41355ba8ffaf6045baf6fed64e6c02b", "path": "GoogleSignIn-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9818202ce112849dc4392b24b9ceadb005", "path": "GoogleSignIn.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9851aa04c81ebdca816b86a2d678ff760c", "path": "GoogleSignIn.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987a8c774abefb48b164a4f8d4fe126374", "path": "ResourceBundle-GoogleSignIn-GoogleSignIn-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9851399265bd66b105fdc3003440502ed7", "name": "Support Files", "path": "../Target Support Files/GoogleSignIn", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98428b13c07ac2058e62dd478435fecae9", "name": "GoogleSignIn", "path": "GoogleSignIn", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e8a8a0c366bb94a9104200c25e93b9a7", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULAppDelegateSwizzler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ca5f048e352dc0e35763bc9bdcf7144e", "path": "GoogleUtilities/AppDelegateSwizzler/GULAppDelegateSwizzler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9870cf5357a25906c31794f9932f6d9bc6", "path": "GoogleUtilities/AppDelegateSwizzler/Internal/GULAppDelegateSwizzler_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9888d0dcd2c9c46415bb9c6cefd0e9e611", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULApplication.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982d5cf12446f5ff1e8655986a2eb4fa7a", "path": "GoogleUtilities/Common/GULLoggerCodes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98deaf4a2e53c19ad7288f9b06cd530d55", "path": "GoogleUtilities/AppDelegateSwizzler/Public/GoogleUtilities/GULSceneDelegateSwizzler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988d0bb4de20bb3f44098c31281d24bb0d", "path": "GoogleUtilities/AppDelegateSwizzler/GULSceneDelegateSwizzler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986c353a1fd5dbd055d8eb550312729bf8", "path": "GoogleUtilities/AppDelegateSwizzler/Internal/GULSceneDelegateSwizzler_Private.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98da04f1c77fe0ace6bc452ba5616a2a8a", "name": "AppDelegateSwizzler", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986006cfaa38d8de91880d40987c3528e5", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULAppEnvironmentUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989e1d88875ea8aa86127999c1ec9c0c67", "path": "GoogleUtilities/Environment/GULAppEnvironmentUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981338ae0806ddd457a51565df57017cb3", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULKeychainStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fff7de40a32f8ee615b461e67df179c5", "path": "GoogleUtilities/Environment/SecureStorage/GULKeychainStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9891395768180ebf447606b6af09c512bd", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULKeychainUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9815f8ef7a5366dfaf6f78a79929dcb614", "path": "GoogleUtilities/Environment/SecureStorage/GULKeychainUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d0be171ca2fe24c62889a9a8a92448a", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULNetworkInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c474f7f773588e02bf0bb60b804705fc", "path": "GoogleUtilities/Environment/NetworkInfo/GULNetworkInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c3d2e9a6a2c0e7b3ea1914d6ca58a0dc", "path": "third_party/IsAppEncrypted/Public/IsAppEncrypted.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986b778fb502ae1d1aa921964788bf0e16", "path": "third_party/IsAppEncrypted/IsAppEncrypted.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c329cf27a8a5b13c4d2799befd371894", "name": "Environment", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983f8cd3212da491916a7f345562ac0189", "path": "GoogleUtilities/Logger/Public/GoogleUtilities/GULLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a6bf05994a48916806394e59dea28c02", "path": "GoogleUtilities/Logger/GULLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c6fc2741de9c335df6a6420859e240f3", "path": "GoogleUtilities/Logger/Public/GoogleUtilities/GULLoggerLevel.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98612c56434b67db8d11a2b1284e13b43c", "name": "<PERSON><PERSON>", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98df9af5f6d37084129247060a71324836", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULMutableDictionary.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985347ca8fa18593958f735b56aab04c81", "path": "GoogleUtilities/Network/GULMutableDictionary.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9812c14f15be61347b570e96b5fe451cb2", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetwork.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f946784962e78cda9c1615ced73d51f4", "path": "GoogleUtilities/Network/GULNetwork.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a63c1f4377409783027639884b5ed278", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkConstants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ecfa846263a02c007d09e8c13a55825e", "path": "GoogleUtilities/Network/GULNetworkConstants.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ee3755c76978a9f7ff43edab58d873e6", "path": "GoogleUtilities/Network/GULNetworkInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98524ab772c9000ba89393143e4871dcf2", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkLoggerProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9852c20048f1e7386c3fa6d78fc59d52ac", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkMessageCode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9877998d7bfd5166fae1008ffa58932f3f", "path": "GoogleUtilities/Network/Public/GoogleUtilities/GULNetworkURLSession.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cbd8443653d1fb2518fac0afa0c6c095", "path": "GoogleUtilities/Network/GULNetworkURLSession.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e0afc07334497e3e732c6893bde08a66", "name": "Network", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98553fd0c16c915dea30932bc5423b5192", "path": "GoogleUtilities/NSData+zlib/Public/GoogleUtilities/GULNSData+zlib.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980f5ed251c5629f6754b48ebc2dbb6d98", "path": "GoogleUtilities/NSData+zlib/GULNSData+zlib.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9866fb0d01d1f810cd3a6560904e2e54a6", "name": "NSData+zlib", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98deaa3f9a8bf02fdcb1b07f48418b86ee", "path": "GoogleUtilities/Privacy/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f65a188c4a80091ab13b37d9c1358bc1", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f27b0f63d52415f6795ab96d1be8ac96", "name": "Privacy", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984e4b24055290bf135522be398c946bd0", "path": "GoogleUtilities/Reachability/Public/GoogleUtilities/GULReachabilityChecker.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9808cc3cce26e2f14d0b9c1ebaf0e3a14f", "path": "GoogleUtilities/Reachability/GULReachabilityChecker.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981535bda826c32b191fc8c148e917bb72", "path": "GoogleUtilities/Reachability/GULReachabilityChecker+Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985eaedac839ffd3aa85b2dfaaf8072034", "path": "GoogleUtilities/Reachability/GULReachabilityMessageCode.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985363442edeb0e3f0b6e22a4f5730579f", "name": "Reachability", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9867fa6c04359bcccf7cded487c5b6456d", "path": "GoogleUtilities.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989bd2b62c02c0d24aa127c85514c480c2", "path": "GoogleUtilities-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984ccb6444d209110d070c5bbfff9ff898", "path": "GoogleUtilities-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fef08246dfc7b67065654ae951be3b0c", "path": "GoogleUtilities-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98814c7605eebd59c4fb63c42cc8c40025", "path": "GoogleUtilities.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9873ac62d770401282a4134727c13b0a4a", "path": "GoogleUtilities.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98026d03e57cc01553ec6629ac238be8e1", "path": "ResourceBundle-GoogleUtilities_Privacy-GoogleUtilities-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9889c08205cc60fa8adbfa8a2a9bc99595", "name": "Support Files", "path": "../Target Support Files/GoogleUtilities", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f447fc0ce3587dc0895e67341c521116", "path": "GoogleUtilities/UserDefaults/Public/GoogleUtilities/GULUserDefaults.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980071ce52dd87663db6f9e22e241a343a", "path": "GoogleUtilities/UserDefaults/GULUserDefaults.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98face18565f6ed1ab7650295dd755dffe", "name": "UserDefaults", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981565339ec5303ab1dac05cc3f51db1cf", "name": "GoogleUtilities", "path": "GoogleUtilities", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986a5d0b78edf149dcdd662196bbee6fcc", "path": "GTMAppAuth/Sources/AuthSession.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9802b8263f5b3665a101883edb10aaf6dc", "path": "GTMAppAuth/Sources/AuthSessionDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988ec25bfa1f780218957f25039d399475", "path": "GTMAppAuth/Sources/AuthSessionStore.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98186f3a17343c80a79dd7c54dd374e028", "path": "GTMAppAuth/Sources/KeychainStore/GTMOAuth2Compatibility.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98097817c3e5d075994f0a6fac4697dfab", "path": "GTMAppAuth/Sources/KeychainStore/KeychainAttribute.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988f8a42a50007d6b60c66aa17628826a5", "path": "GTMAppAuth/Sources/KeychainStore/KeychainHelper.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984fe743d10633d0560fbcd1c9e310c5a5", "path": "GTMAppAuth/Sources/KeychainStore/KeychainStore.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9800ceb095cab369771c620283d7361937", "path": "GTMAppAuth/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9818b35c405ad376b6a4299339e8b6d100", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983a16f01b82b0020240fefec04c2c998f", "path": "GTMAppAuth.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987ea6b69f777c0f9659185327c5edd5c9", "path": "GTMAppAuth-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9890afc1c95901fbe4744478a879538494", "path": "GTMAppAuth-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987ed331bc251fe2faae84e53c79cc0a2c", "path": "GTMAppAuth-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d4f1bd35decc8ec8210af3499bb6a6ab", "path": "GTMAppAuth.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98784b9e3a033012d8118f1c0f9b1e84a9", "path": "GTMAppAuth.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d172737c3898bdb0ed243d6f36ae03da", "path": "ResourceBundle-GTMAppAuth_Privacy-GTMAppAuth-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9841cf794805ecbb64512885701104f95e", "name": "Support Files", "path": "../Target Support Files/GTMAppAuth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cc692440f60e5526a2472ddf04ad0f49", "name": "GTMAppAuth", "path": "GTMAppAuth", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9825da5508d5596c6d213b1d65fcea410e", "path": "Sources/Core/Public/GTMSessionFetcher/GTMSessionFetcher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985de0d45cafd33228c1bf20ac98fbc1c8", "path": "Sources/Core/GTMSessionFetcher.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dfea3244a99d59f2c6f45a0124ea1a9a", "path": "Sources/Core/Public/GTMSessionFetcher/GTMSessionFetcherLogging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986a3354a36930e867989da795ca7e50c8", "path": "Sources/Core/GTMSessionFetcherLogging.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e7c0edf39b9bcd172cc895ea8ce1f0fb", "path": "Sources/Core/Public/GTMSessionFetcher/GTMSessionFetcherService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986d051590ffbe5b21fcc87f1e08bb12d6", "path": "Sources/Core/GTMSessionFetcherService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988d56b05a2c2ea30ba848d6039d28300d", "path": "Sources/Core/GTMSessionFetcherService+Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c691dd8d93a1314d493f71a968a699cf", "path": "Sources/Core/Public/GTMSessionFetcher/GTMSessionUploadFetcher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989b989b16a30797b4de0eaf24287dcaa8", "path": "Sources/Core/GTMSessionUploadFetcher.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9809eb02ae4bf96ab7955323e90833e174", "path": "Sources/Core/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980b864ca7e58107b2f8a3d3394dac6132", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984825e48cd0d94509f68b02e501caa44f", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98be2b0f698391419d7d50f9381b269680", "path": "Sources/Full/Public/GTMSessionFetcher/GTMGatherInputStream.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9824af6ce6019f8ee670873a9f9e047739", "path": "Sources/Full/GTMGatherInputStream.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983d792d06f1b7ec163b059cf9f0f1dea8", "path": "Sources/Full/Public/GTMSessionFetcher/GTMMIMEDocument.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980b17ce4b49529d7c0025501f57f417cd", "path": "Sources/Full/GTMMIMEDocument.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987a6ff4c1db1b5a40dc706c431fc04530", "path": "Sources/Full/Public/GTMSessionFetcher/GTMReadMonitorInputStream.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989a1afdfe76261ec0b767179a2e1888f1", "path": "Sources/Full/GTMReadMonitorInputStream.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e980b6ea1f83ff9bd96e3b2bef9e515747c", "path": "Sources/Full/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980fe92896dee1391ed79717f54c0da0bf", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986dad798774ace254ece3a73d770a76ab", "name": "Full", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98f390c0f9eae925893f6f9d875ae37de2", "path": "GTMSessionFetcher.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9886dba94f8ed3023b8dffa9e1563e9642", "path": "GTMSessionFetcher-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b1f1ff061a122c1e2f63b4c01ebe4fd3", "path": "GTMSessionFetcher-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983f393d1f90f8dec2b9a82630563d69a8", "path": "GTMSessionFetcher-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983a53b2ca5d233417705ef7d533a502f7", "path": "GTMSessionFetcher.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98710fb2a7258b9f2678f395c8ba0d6118", "path": "GTMSessionFetcher.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986b7bc5c169f82a8774dcba585a3cdc18", "path": "ResourceBundle-GTMSessionFetcher_Core_Privacy-GTMSessionFetcher-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9827e1b05acf17bdd7a6d09efc30c4aff7", "path": "ResourceBundle-GTMSessionFetcher_Full_Privacy-GTMSessionFetcher-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98789732a7cc9c07004dd923c41f720c64", "name": "Support Files", "path": "../Target Support Files/GTMSessionFetcher", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bf07fc1aa227ed74a51d163120467e66", "name": "GTMSessionFetcher", "path": "GTMSessionFetcher", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988655eb1983a8b8b49ef5bfe21161a003", "path": "Sources/FBLPromises/include/FBLPromise.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988fe5022a903e315eac8f05da7cda5d91", "path": "Sources/FBLPromises/FBLPromise.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a042d5428ba9d058867a16553feb29f8", "path": "Sources/FBLPromises/include/FBLPromise+All.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98680adc0ebab4741a01d1102f47511180", "path": "Sources/FBLPromises/FBLPromise+All.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fc8d05455d9e0107aefa57aaf584465f", "path": "Sources/FBLPromises/include/FBLPromise+Always.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988136ab3cedaf13be8b3d38f71fb0387d", "path": "Sources/FBLPromises/FBLPromise+Always.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a7fa0c45d0ec357c8284998a308507c5", "path": "Sources/FBLPromises/include/FBLPromise+Any.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a88fe8f14d5d96a0d810309f4e2a011d", "path": "Sources/FBLPromises/FBLPromise+Any.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f4aa5d52455964016a3d601bf4c3f7be", "path": "Sources/FBLPromises/include/FBLPromise+Async.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e31d82adf00b5346d85c7fe262510a7f", "path": "Sources/FBLPromises/FBLPromise+Async.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bb372e4fec564e547c164e5d2274dff1", "path": "Sources/FBLPromises/include/FBLPromise+Await.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98658b155d053d4ed3e52178c506ed14a1", "path": "Sources/FBLPromises/FBLPromise+Await.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9817d2de9e6d0966f497eef748640524ff", "path": "Sources/FBLPromises/include/FBLPromise+Catch.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9884df5701f90e3eefcc6f98964fafe9a5", "path": "Sources/FBLPromises/FBLPromise+Catch.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98102bc8f05913b22040891de13f77572a", "path": "Sources/FBLPromises/include/FBLPromise+Delay.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983cc9b9651ab0188883a5e32276ce7dc4", "path": "Sources/FBLPromises/FBLPromise+Delay.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d6b285b72f7ca6be3b5f9be57b5f32ad", "path": "Sources/FBLPromises/include/FBLPromise+Do.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9843955722cc93289b454e29e761f539b4", "path": "Sources/FBLPromises/FBLPromise+Do.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e8ba2c991badd668ae7df5c4bdb347cf", "path": "Sources/FBLPromises/include/FBLPromise+Race.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984aeca66e371beb567cc1631f471f289a", "path": "Sources/FBLPromises/FBLPromise+Race.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983b56b1c618a757f0cc0d10753a2d7856", "path": "Sources/FBLPromises/include/FBLPromise+Recover.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e79edbee5ebad84ea6c686ef7b903314", "path": "Sources/FBLPromises/FBLPromise+Recover.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dbcdb25e49436512bc488d3a9f57aeb2", "path": "Sources/FBLPromises/include/FBLPromise+Reduce.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bc36dbd8d2d8c7d255b88284e8aab533", "path": "Sources/FBLPromises/FBLPromise+Reduce.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983884cb711c03ac96fa60d7bcaac7479e", "path": "Sources/FBLPromises/include/FBLPromise+Retry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ce048613ba9b6151bc22f3b68ce255e6", "path": "Sources/FBLPromises/FBLPromise+Retry.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9802d22fb7464bfdc1c5ff243a2bb27fd3", "path": "Sources/FBLPromises/include/FBLPromise+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98400d6fa28ab25ac3019aac1f596ebe27", "path": "Sources/FBLPromises/FBLPromise+Testing.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98873bfce4eca01fa7ce3ff97f929f0b08", "path": "Sources/FBLPromises/include/FBLPromise+Then.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f6201864c70070c62677c3aa47db8e0c", "path": "Sources/FBLPromises/FBLPromise+Then.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9883567b55a029f37e32cb4596d9d6c702", "path": "Sources/FBLPromises/include/FBLPromise+Timeout.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ca54ecfc030f7cf74b32386924aae0b8", "path": "Sources/FBLPromises/FBLPromise+Timeout.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ab6972bff13cdc78e32f892a8f86e754", "path": "Sources/FBLPromises/include/FBLPromise+Validate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988d0c7acb98746b0d684a86d23cb3162a", "path": "Sources/FBLPromises/FBLPromise+Validate.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9894855c0fe298813cc0355af5837168dd", "path": "Sources/FBLPromises/include/FBLPromise+Wrap.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986318ac4ac244676a56fbd8236977b989", "path": "Sources/FBLPromises/FBLPromise+Wrap.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9856938ea35945b782d8413fc6920c02cc", "path": "Sources/FBLPromises/include/FBLPromiseError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b37af6bb6109ed32b6c69e23098a8bf1", "path": "Sources/FBLPromises/FBLPromiseError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1ac92115d190a85219e99416cd53fbf", "path": "Sources/FBLPromises/include/FBLPromisePrivate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bd341cdb772826320187144d23eab2d8", "path": "Sources/FBLPromises/include/FBLPromises.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e989dd9104bd2aec17c24cf3986f1993401", "path": "Sources/FBLPromises/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985cd0530cd3cbf70e24db7c0f1c2626a5", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98142c0c9392cb92032ba44ac429e22964", "path": "PromisesObjC.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987073779af36adb2fb969ad94b4a17bf2", "path": "PromisesObjC-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985d2256522f1ce8c0d228c314cdf55389", "path": "PromisesObjC-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986cea627951483e55cb399e2da1bdc704", "path": "PromisesObjC-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9836efbe74617740a796d5354876d07142", "path": "PromisesObjC.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c8391027a86d50dc99ea3a418d54ff61", "path": "PromisesObjC.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983c8c8fac798a75186d1234556266f6a4", "path": "ResourceBundle-FBLPromises_Privacy-PromisesObjC-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988c3899a7243e49c130188ec81a806742", "name": "Support Files", "path": "../Target Support Files/PromisesObjC", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d66e6d9d7575e267857da176933c3188", "name": "PromisesObjC", "path": "PromisesObjC", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a03778ec6e04dac6b4e25cd99943943a", "path": "RecaptchaEnterprise/RecaptchaInterop/placeholder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986bc9ccaff1ce5743c13b256d1dc27476", "path": "RecaptchaEnterprise/RecaptchaInterop/Public/RecaptchaInterop/RCAActionProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d4ecb17853d5946e898dce8448192fee", "path": "RecaptchaEnterprise/RecaptchaInterop/Public/RecaptchaInterop/RCARecaptchaClientProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985b913782a97d3981953cf6f67f82aff0", "path": "RecaptchaEnterprise/RecaptchaInterop/Public/RecaptchaInterop/RCARecaptchaProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985eba268c7fe72d93ad42ef0535e0d7eb", "path": "RecaptchaEnterprise/RecaptchaInterop/Public/RecaptchaInterop/RecaptchaInterop.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c8a30c02e554e668feb699a5c6142025", "path": "RecaptchaInterop.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98736546a4c83c7264fd8fb1ccbfdcba32", "path": "RecaptchaInterop-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f1da69b1356ac6c79ced4a05166b471e", "path": "RecaptchaInterop-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9833b1297dbf322601fcee1838dc4ab20d", "path": "RecaptchaInterop-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980f266f84fdd0dccbf11ca461ce257f6f", "path": "RecaptchaInterop-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9812de50bf5e71b18a382a20c8ae9a1cbb", "path": "RecaptchaInterop.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98291c5952974beb511af2d216235c9cda", "path": "RecaptchaInterop.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981787d259b4b05fa55e9b19a67efc2546", "name": "Support Files", "path": "../Target Support Files/RecaptchaInterop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c2c478d6ec50a5eafbb52b2ca40c3dbc", "name": "RecaptchaInterop", "path": "RecaptchaInterop", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98308eeb135b46e369903e6cc2b07eb2ba", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e98ecb6520b15fa4d16ef8998f63dfb59b7", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a3f4680c874ecbcb5b5a6338bc13a426", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9814181933c973297b0001d0e9ee64381d", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9887440869a53d6020d968ddf6b9b30aa0", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fed0970e703d355c9aff813f280aafdc", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e9826e505869a67d81656859ded68178c1d", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98de02ac5005ab1eb052abf25c63731e95", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98f6f4dcb116856f2da0fa97d3015cb1af", "path": "Pods-Runner-resources.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ec6fbca7264a6936f2adfc48dd7f5bc", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9850cc7fc2d23136fb4fac488d6c47df20", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98dc1a21852c7a085c7dd8f65cf0fa9907", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f17abf55d75f35efcaf45a1185b085b6", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cf3c1972df678a5e36df75a46391500d", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984c4f55ec853c945e234980557a98aed8", "path": "Pods-RunnerTests.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fc0f7e7242f459f81e455145932dcafd", "path": "Pods-RunnerTests-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985f8b68b152f46f18718da20c04e675cb", "path": "Pods-RunnerTests-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98022654f1ff78dd844d694dba2439dab2", "path": "Pods-RunnerTests-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989e5ad6b9a07953a12c7008a15bd9c99c", "path": "Pods-RunnerTests-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5e8bcdff29e5f8321be18f7989b4bc7", "path": "Pods-RunnerTests-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98144cd18850e477837c238075d5256ffe", "path": "Pods-RunnerTests.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981b663a2c82f0220040296818ba53477e", "path": "Pods-RunnerTests.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98965b92d39d30a7872295adc2841cd1b1", "path": "Pods-RunnerTests.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859551a2ccb1df711861b574920cd49bf", "name": "Pods-RunnerTests", "path": "Target Support Files/Pods-RunnerTests", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dafc421ff02609f2772b356038eb9849", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/CascadeProjects/windsurf-project/ellahai/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/CascadeProjects/windsurf-project/ellahai/ios/Pods", "targets": ["TARGET@v11_hash=671ceba818093a549e1e0a2197d00a39", "TARGET@v11_hash=c5f123c5d737010f27304da33aa9c3c6", "TARGET@v11_hash=db56a53eb553849add253163239232fa", "TARGET@v11_hash=e36762f2695007d5f8b032bd8c1152ea", "TARGET@v11_hash=7107630bfb152a55acc4241ee72eb5d1", "TARGET@v11_hash=f3d406271d4547bce2eb0fa314463e08", "TARGET@v11_hash=96e9884ead4efce25c2ca2c961bc92c9", "TARGET@v11_hash=e487ab9288eda7fa68d9dd3759d9a591", "TARGET@v11_hash=c1ea0f86ff4de4c0162929a52c716a24", "TARGET@v11_hash=780e796fa5cad0a38489429afc4f1580", "TARGET@v11_hash=48aafc216753bf48a63c9f9fe035bf92", "TARGET@v11_hash=d92ab048b875853ebe4881d8d127102d", "TARGET@v11_hash=f6c4bcf339eb96ebc5c9b7acdd97bf61", "TARGET@v11_hash=105be060582693982e84ef338ec9f2ad", "TARGET@v11_hash=7e102e27a5fae44825b3bb5e5f653022", "TARGET@v11_hash=0ee33340677186101c52b1c7af868703", "TARGET@v11_hash=1de56fb00fee2716cb1b1add707a4622", "TARGET@v11_hash=e6069b1eabd79866747df992166bdccc", "TARGET@v11_hash=dc8dc9ecde096535c58076e980d6ef48", "TARGET@v11_hash=25fac8b7228a9d2458203b217841d474", "TARGET@v11_hash=dbb7c4a68c580ef4cffdfdfda92e9976", "TARGET@v11_hash=6504b2f5f327157ae4d8b62f20cd4110", "TARGET@v11_hash=3beadee2438b1f8caf5bf4c8510dc804", "TARGET@v11_hash=08972cbb778899faa79114be50193950", "TARGET@v11_hash=e1c633e633267a575a5fdfb0589cb66d", "TARGET@v11_hash=31aa632437768c77c0635c25f7a3aac1", "TARGET@v11_hash=d1f2b31cb6d0fa98e96d30077ce22528", "TARGET@v11_hash=7e4d1779223657861fb402b8c36c5b07", "TARGET@v11_hash=201f308f7430718398766a83c391951c", "TARGET@v11_hash=281b4d3c341517b4c7f473d24d3d7a0f", "TARGET@v11_hash=9be4d8ba9409f95a92b2dc5cddb428d3", "TARGET@v11_hash=c3c2fcac1b5e20035c3163527537ca66", "TARGET@v11_hash=6b63b8fb5f058c92a4cbdca4a5d2c783", "TARGET@v11_hash=5e2179f588144ca7f7e687c98ba75329", "TARGET@v11_hash=c94fbcd0595029be041201a45b1b3cf7", "TARGET@v11_hash=a30bab4ea407ece0442d7a9360761003", "TARGET@v11_hash=9de35674290d1fc135daccfe7cbfa6d5", "TARGET@v11_hash=38c6327973deeb977b5970b0b52331b4", "TARGET@v11_hash=1cbab33fa218510debc6d69f44ee4598", "TARGET@v11_hash=da9e4e30b6476214ee7f4bc3f3da9441", "TARGET@v11_hash=2cb023086341052d13907712ee52af9b", "TARGET@v11_hash=cb66af052a5f0a4949416c1e7ed0213a", "TARGET@v11_hash=cfa030b697d31481157518827b4d94b4", "TARGET@v11_hash=b3c20ba94934dc280cb3bc8246e4530a", "TARGET@v11_hash=cdb0c114477d8c8937b760f3b6db89a6", "TARGET@v11_hash=76469a48473792aa0809db39e2ab5a27", "TARGET@v11_hash=8d54040ebcd2da0e8e58e4922ee4b461", "TARGET@v11_hash=aecda586cb6227fcc80f5d31c081eea9"]}