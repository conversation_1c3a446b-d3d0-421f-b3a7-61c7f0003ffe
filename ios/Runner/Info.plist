<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Ellahai</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>ellahai</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>

	<!-- Microphone permission for voice input and VAD -->
	<key>NSMicrophoneUsageDescription</key>
	<string>This app needs access to your microphone to record voice messages, detect voice activity, and enable real-time voice chat with the AI assistant.</string>

	<!-- Speech recognition permission for Whisper and speech-to-text -->
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>This app uses speech recognition to convert your voice messages to text for better communication with the AI assistant.</string>

	<!-- Audio session configuration for real-time processing -->
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
	</array>

	<key>GIDClientID</key>
	<!-- Copied from GoogleService-Info.plist key CLIENT_ID -->
	<string>389213018479-m9dtoaina93frjjo1iv1k02iu8lnanul.apps.googleusercontent.com</string>

	<!-- Google Sign-In URL Scheme - Replace with your REVERSED_CLIENT_ID from GoogleService-Info.plist -->
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>com.example.ellahai</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<!-- Replace this with your REVERSED_CLIENT_ID from GoogleService-Info.plist -->
				<string>com.googleusercontent.apps.389213018479-m9dtoaina93frjjo1iv1k02iu8lnanul</string>
			</array>
		</dict>
	</array>
</dict>
</plist>
