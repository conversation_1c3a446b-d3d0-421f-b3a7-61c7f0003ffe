# Manual Xcode Configuration Steps

After running this script, you need to manually configure Xcode:

## 1. Open Xcode Workspace
```bash
open ios/Runner.xcworkspace
```

## 2. Add Unity-iPhone Project
- Right-click in the Navigator (not on an item)
- Select "Add Files to Runner"
- Navigate to `ios/UnityLibrary/Unity-iPhone.xcodeproj`
- Click "Add"

## 3. Add UnityFramework to Runner Target
- Select Runner project in navigator
- Select Runner target
- Go to "General" tab
- In "Frameworks, Libraries, and Embedded Content":
  - Click "+" button
  - Select "UnityFramework.framework" from Unity-iPhone
  - Set to "Embed & Sign"

## 4. Configure Build Settings
- Select Runner target
- Go to "Build Settings" tab
- Search for "Other Linker Flags"
- Add: `-framework UnityFramework`
- Search for "Framework Search Paths"
- Add: `$(PROJECT_DIR)/UnityLibrary`

## 5. Architecture Settings
- In Build Settings, set:
  - `ONLY_ACTIVE_ARCH` = NO
  - `VALID_ARCHS` = arm64

## 6. Clean and Build
- Product > Clean Build Folder
- Product > Build

## Common Issues:
- If you get "mUnityPlayer" errors, see Unity setup step 3 in the documentation
- Make sure your Unity project has the flutter-unity integration package imported
- Verify that UnityMessageManager is added to your Unity scene
