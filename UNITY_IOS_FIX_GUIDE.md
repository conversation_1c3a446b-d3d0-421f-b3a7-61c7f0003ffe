# Unity iOS Integration Fix Guide

## Problem
You're encountering the error: `Undefined symbol: _SendMessageToFlutter`

This happens because the Unity native bridge isn't properly linked to your iOS project.

## Root Cause
The `_SendMessageToFlutter` symbol is defined in the Unity native iOS bridge but isn't being linked properly during the build process. This is a common issue with flutter_unity_widget integration.

## Complete Solution

### Step 1: Verify Unity Project Setup

1. **Check Unity Player Settings:**
   - Open Unity
   - Go to `File > Build Settings > Player Settings`
   - Under `Other Settings > Configuration`:
     - Set `Scripting Backend` to `IL2CPP`
     - Set `Target Device` to `iPhone + iPad`
     - Set `Architecture` to `ARM64`

2. **Verify Unity Scene Setup:**
   - Make sure your main Unity scene has a GameObject with the `UnityMessageManager` component
   - Your `EllahAIManager` script should be on a GameObject in the scene

### Step 2: Re-export Unity Project

1. In Unity, go to `Flutter > Export iOS`
2. This will update the `ios/UnityLibrary` folder with the latest build

### Step 3: Configure Xcode Project

**IMPORTANT: You must do these steps manually in Xcode**

1. **Open Xcode Workspace:**
   ```bash
   open ios/Runner.xcworkspace
   ```

2. **Add Unity-iPhone Project:**
   - Right-click in the Project Navigator (left panel, not on any item)
   - Select "Add Files to Runner"
   - Navigate to `ios/UnityLibrary/Unity-iPhone.xcodeproj`
   - Click "Add"

3. **Link UnityFramework:**
   - Select the Runner project in the navigator
   - Select the Runner target
   - Go to the "General" tab
   - In "Frameworks, Libraries, and Embedded Content" section:
     - Click the "+" button
     - Find and select `UnityFramework.framework` from the Unity-iPhone project
     - Set it to "Embed & Sign"

4. **Configure Build Settings:**
   - Select Runner target
   - Go to "Build Settings" tab
   - Search for "Other Linker Flags"
   - Add: `-framework UnityFramework`
   - Search for "Framework Search Paths"
   - Add: `$(PROJECT_DIR)/UnityLibrary`

5. **Set Architecture:**
   - In Build Settings, find:
     - `ONLY_ACTIVE_ARCH` = NO
     - `VALID_ARCHS` = arm64

### Step 4: Clean and Build

1. In Xcode: `Product > Clean Build Folder`
2. Try building again: `Product > Build`

## Alternative Solution: Check Native Bridge

If the above doesn't work, the issue might be with the native bridge implementation. Let's verify:

### Check iOS Plugin Files

1. **Verify flutter_unity_widget iOS files:**
   ```bash
   ls ios/.symlinks/plugins/flutter_unity_widget/ios/Classes/
   ```

2. **Check if the native bridge is properly implemented:**
   The plugin should have files that implement the `OnUnityMessage` function.

### Manual Native Bridge Fix

If the native bridge is missing, you might need to add it manually:

1. **Create a bridge file** (if missing):
   ```objc
   // In ios/Runner/UnityBridge.mm
   #import <Foundation/Foundation.h>
   #import <UnityFramework/UnityFramework.h>

   extern "C" {
       void OnUnityMessage(const char* message) {
           // Implementation to send message to Flutter
           NSString *messageString = [NSString stringWithUTF8String:message];
           // Send to Flutter channel
       }
   }
   ```

## Troubleshooting

### Common Issues:

1. **"mUnityPlayer" errors:**
   - This happens with newer Unity versions
   - Make sure you're using the correct flutter_unity_widget version
   - Check Unity Player Settings for IL2CPP backend

2. **Framework not found:**
   - Ensure UnityFramework.framework is properly linked
   - Check that the framework search paths include UnityLibrary

3. **Build fails with linker errors:**
   - Clean both Flutter and Xcode builds
   - Re-export from Unity
   - Verify all frameworks are properly embedded

### Verification Steps:

1. **Check if UnityFramework is properly linked:**
   ```bash
   # In your project root
   find ios -name "UnityFramework.framework" -type d
   ```

2. **Verify Unity export:**
   ```bash
   ls -la ios/UnityLibrary/UnityFramework/
   ```

3. **Check Xcode project structure:**
   - Unity-iPhone.xcodeproj should appear in your workspace
   - UnityFramework.framework should be in "Frameworks, Libraries, and Embedded Content"

## Final Notes

- Always use `Runner.xcworkspace`, not `Runner.xcodeproj`
- Make sure to clean builds after making changes
- The Unity project must be exported before building iOS
- Test on a physical device, not just simulator

If you're still having issues after following all these steps, the problem might be with the specific version compatibility between Unity, flutter_unity_widget, and your iOS setup.

## Quick Commands Summary

```bash
# Clean everything
flutter clean
rm -rf ios/build

# Re-get dependencies
flutter pub get
cd ios && pod install && cd ..

# Re-export Unity (do this in Unity Editor)
# Flutter > Export iOS

# Open Xcode and follow manual steps above
open ios/Runner.xcworkspace
```