import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'core/theme/app_theme.dart';
import 'core/constants/app_constants.dart';
import 'services/storage/storage_service.dart';
import 'providers/auth_provider.dart';
import 'screens/auth/splash_screen.dart';
import 'screens/auth/auth_screen.dart';
import 'screens/auth/email_verification_screen.dart';
import 'screens/auth/change_password_screen.dart';
import 'widgets/navigation/main_navigation.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Hive
  await Hive.initFlutter();
  await StorageService.init();
  
  runApp(const ProviderScope(child: EllahAIApp()));
}

class EllahAIApp extends ConsumerWidget {
  const EllahAIApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authProvider);
    
    return MaterialApp(
      title: AppConstants.appName,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.dark, // Default to dark theme for futuristic feel
      debugShowCheckedModeBanner: false,
      home: _buildHome(authState),
    );
  }
  
  Widget _buildHome(AuthState authState) {
    return authState.when(
      loading: () => const SplashScreen(),
      unauthenticated: () => const AuthScreen(),
      authenticated: (user) => const MainNavigation(),
      emailVerificationRequired: (email, isSignup) => EmailVerificationScreen(
        email: email,
        isSignup: isSignup,
      ),
      passwordResetVerificationRequired: (email) => EmailVerificationScreen(
        email: email,
        isSignup: false,
        isPasswordReset: true,
      ),
      changePasswordRequired: (email) => ChangePasswordScreen(email: email),
    );
  }
}
