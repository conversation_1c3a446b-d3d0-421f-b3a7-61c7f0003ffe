import 'package:hive/hive.dart';
import 'package:equatable/equatable.dart';

part 'message_model.g.dart';

@HiveType(typeId: 3)
enum MessageType {
  @HiveField(0)
  text,
  
  @HiveField(1)
  voice,
  
  @HiveField(2)
  image,
  
  @HiveField(3)
  system,
  
  @HiveField(4)
  typing,
}

@HiveType(typeId: 4)
class MessageModel extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String content;
  
  @HiveField(2)
  final MessageType type;
  
  @HiveField(3)
  final bool isFromUser;
  
  @HiveField(4)
  final DateTime timestamp;
  
  @HiveField(5)
  final String? audioPath;
  
  @HiveField(6)
  final String? imagePath;
  
  @HiveField(7)
  final Map<String, dynamic>? metadata;
  
  @HiveField(8)
  final bool isRead;
  
  @HiveField(9)
  final bool isDelivered;
  
  @HiveField(10)
  final String? replyToId;
  
  @HiveField(11)
  final String conversationId;

  const MessageModel({
    required this.id,
    required this.content,
    required this.type,
    required this.isFromUser,
    required this.timestamp,
    this.audioPath,
    this.imagePath,
    this.metadata,
    this.isRead = false,
    this.isDelivered = false,
    this.replyToId,
    required this.conversationId,
  });

  factory MessageModel.text({
    required String id,
    required String content,
    required bool isFromUser,
    String? replyToId,
    Map<String, dynamic>? metadata,
    String? conversationId,
  }) {
    return MessageModel(
      id: id,
      content: content,
      type: MessageType.text,
      isFromUser: isFromUser,
      timestamp: DateTime.now(),
      replyToId: replyToId,
      metadata: metadata,
      conversationId: conversationId ?? 'default',
    );
  }

  factory MessageModel.voice({
    required String id,
    required String content,
    required String audioPath,
    required bool isFromUser,
    String? replyToId,
    Map<String, dynamic>? metadata,
    String? conversationId,
  }) {
    return MessageModel(
      id: id,
      content: content,
      type: MessageType.voice,
      isFromUser: isFromUser,
      timestamp: DateTime.now(),
      audioPath: audioPath,
      replyToId: replyToId,
      metadata: metadata,
      conversationId: conversationId ?? 'default',
    );
  }

  factory MessageModel.image({
    required String id,
    required String content,
    required String imagePath,
    required bool isFromUser,
    String? replyToId,
    Map<String, dynamic>? metadata,
    String? conversationId,
  }) {
    return MessageModel(
      id: id,
      content: content,
      type: MessageType.image,
      isFromUser: isFromUser,
      timestamp: DateTime.now(),
      imagePath: imagePath,
      replyToId: replyToId,
      metadata: metadata,
      conversationId: conversationId ?? 'default',
    );
  }

  factory MessageModel.system({
    required String id,
    required String content,
    Map<String, dynamic>? metadata,
    String? conversationId,
  }) {
    return MessageModel(
      id: id,
      content: content,
      type: MessageType.system,
      isFromUser: false,
      timestamp: DateTime.now(),
      metadata: metadata,
      conversationId: conversationId ?? 'default',
    );
  }

  factory MessageModel.typing({
    required String id,
    String? conversationId,
  }) {
    return MessageModel(
      id: id,
      content: '',
      type: MessageType.typing,
      isFromUser: false,
      timestamp: DateTime.now(),
      conversationId: conversationId ?? 'default',
    );
  }

  factory MessageModel.fromJson(Map<String, dynamic> json) {
    return MessageModel(
      id: json['id'],
      content: json['content'] ?? '',
      type: MessageType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => MessageType.text,
      ),
      isFromUser: json['is_from_user'] ?? false,
      timestamp: DateTime.parse(json['timestamp']),
      audioPath: json['audio_path'],
      imagePath: json['image_path'],
      metadata: json['metadata'],
      isRead: json['is_read'] ?? false,
      isDelivered: json['is_delivered'] ?? false,
      replyToId: json['reply_to_id'],
      conversationId: json['conversation_id'] ?? 'default',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'type': type.name,
      'is_from_user': isFromUser,
      'timestamp': timestamp.toIso8601String(),
      'audio_path': audioPath,
      'image_path': imagePath,
      'metadata': metadata,
      'is_read': isRead,
      'is_delivered': isDelivered,
      'reply_to_id': replyToId,
      'conversation_id': conversationId,
    };
  }

  MessageModel copyWith({
    String? id,
    String? content,
    MessageType? type,
    bool? isFromUser,
    DateTime? timestamp,
    String? audioPath,
    String? imagePath,
    Map<String, dynamic>? metadata,
    bool? isRead,
    bool? isDelivered,
    String? replyToId,
    String? conversationId,
  }) {
    return MessageModel(
      id: id ?? this.id,
      content: content ?? this.content,
      type: type ?? this.type,
      isFromUser: isFromUser ?? this.isFromUser,
      timestamp: timestamp ?? this.timestamp,
      audioPath: audioPath ?? this.audioPath,
      imagePath: imagePath ?? this.imagePath,
      metadata: metadata ?? this.metadata,
      isRead: isRead ?? this.isRead,
      isDelivered: isDelivered ?? this.isDelivered,
      replyToId: replyToId ?? this.replyToId,
      conversationId: conversationId ?? this.conversationId,
    );
  }

  MessageModel markAsRead() {
    return copyWith(isRead: true);
  }

  MessageModel markAsDelivered() {
    return copyWith(isDelivered: true);
  }

  bool get hasAudio => audioPath != null && audioPath!.isNotEmpty;
  bool get hasImage => imagePath != null && imagePath!.isNotEmpty;
  bool get isTyping => type == MessageType.typing;
  bool get isSystemMessage => type == MessageType.system;

  String get displayTime {
    final now = DateTime.now();
    final diff = now.difference(timestamp);

    if (diff.inMinutes < 1) {
      return 'now';
    } else if (diff.inHours < 1) {
      return '${diff.inMinutes}m ago';
    } else if (diff.inDays < 1) {
      return '${diff.inHours}h ago';
    } else if (diff.inDays < 7) {
      return '${diff.inDays}d ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  @override
  List<Object?> get props => [
        id,
        content,
        type,
        isFromUser,
        timestamp,
        audioPath,
        imagePath,
        metadata,
        isRead,
        isDelivered,
        replyToId,
        conversationId,
      ];
}

// Conversation Model for organizing messages
@HiveType(typeId: 5)
class ConversationModel extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String title;
  
  @HiveField(2)
  final DateTime createdAt;
  
  @HiveField(3)
  final DateTime lastMessageAt;
  
  @HiveField(4)
  final List<String> messageIds;
  
  @HiveField(5)
  final Map<String, dynamic> metadata;

  const ConversationModel({
    required this.id,
    required this.title,
    required this.createdAt,
    required this.lastMessageAt,
    required this.messageIds,
    required this.metadata,
  });

  factory ConversationModel.create({
    required String id,
    required String title,
  }) {
    final now = DateTime.now();
    return ConversationModel(
      id: id,
      title: title,
      createdAt: now,
      lastMessageAt: now,
      messageIds: [],
      metadata: {},
    );
  }

  ConversationModel copyWith({
    String? id,
    String? title,
    DateTime? createdAt,
    DateTime? lastMessageAt,
    List<String>? messageIds,
    Map<String, dynamic>? metadata,
  }) {
    return ConversationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      createdAt: createdAt ?? this.createdAt,
      lastMessageAt: lastMessageAt ?? this.lastMessageAt,
      messageIds: messageIds ?? this.messageIds,
      metadata: metadata ?? this.metadata,
    );
  }

  ConversationModel addMessage(String messageId) {
    return copyWith(
      messageIds: [...messageIds, messageId],
      lastMessageAt: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        createdAt,
        lastMessageAt,
        messageIds,
        metadata,
      ];
}
