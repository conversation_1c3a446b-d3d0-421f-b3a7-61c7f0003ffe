import 'package:flutter/material.dart';

class ChatTool {
  final String id;
  final String name;
  final IconData icon;
  final bool isLocked;
  final bool isPremium;
  final String description;

  const ChatTool({
    required this.id,
    required this.name,
    required this.icon,
    this.isLocked = false,
    this.isPremium = false,
    required this.description,
  });

  static List<ChatTool> get defaultTools => [
    const ChatTool(
      id: 'general',
      name: 'General',
      icon: Icons.chat_rounded,
      description: 'General conversation and assistance',
    ),
    const ChatTool(
      id: 'coding',
      name: 'Coding',
      icon: Icons.code_rounded,
      description: 'Programming help and code generation',
    ),
    const ChatTool(
      id: 'search',
      name: 'Search',
      icon: Icons.search_rounded,
      description: 'Web search and information lookup',
    ),
    const ChatTool(
      id: 'podcast',
      name: 'Podcast',
      icon: Icons.podcasts_rounded,
      description: 'Podcast creation and audio content',
      isLocked: true,
      isPremium: true,
    ),
    const ChatTool(
      id: 'image_gen',
      name: 'Image Gen',
      icon: Icons.image_rounded,
      description: 'AI image generation and editing',
      isLocked: true,
      isPremium: true,
    ),
    const ChatTool(
      id: 'nsfw',
      name: 'NSFW',
      icon: Icons.warning_rounded,
      description: 'Adult content mode',
      isLocked: true,
      isPremium: true,
    ),
    const ChatTool(
      id: 'analysis',
      name: 'Analysis',
      icon: Icons.analytics_rounded,
      description: 'Data analysis and insights',
    ),
    const ChatTool(
      id: 'creative',
      name: 'Creative',
      icon: Icons.palette_rounded,
      description: 'Creative writing and brainstorming',
    ),
  ];
}