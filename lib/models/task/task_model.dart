import 'package:flutter/material.dart';

enum TaskStatus {
  pending,
  running,
  completed,
  failed,
  needsIntervention,
  cancelled,
}

enum TaskPhase {
  initialization,
  analysis,
  planning,
  execution,
  debugging,
  testing,
  finalization,
}

class TaskPhaseDetail {
  final TaskPhase phase;
  final String title;
  final String description;
  final bool isCompleted;
  final bool isCurrent;
  final List<String> subTasks;

  const TaskPhaseDetail({
    required this.phase,
    required this.title,
    required this.description,
    this.isCompleted = false,
    this.isCurrent = false,
    this.subTasks = const [],
  });

  TaskPhaseDetail copyWith({
    TaskPhase? phase,
    String? title,
    String? description,
    bool? isCompleted,
    bool? isCurrent,
    List<String>? subTasks,
  }) {
    return TaskPhaseDetail(
      phase: phase ?? this.phase,
      title: title ?? this.title,
      description: description ?? this.description,
      isCompleted: isCompleted ?? this.isCompleted,
      isCurrent: isCurrent ?? this.isCurrent,
      subTasks: subTasks ?? this.subTasks,
    );
  }
}

class TaskModel {
  final String id;
  final String title;
  final String description;
  final TaskStatus status;
  final double progress;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final List<TaskPhaseDetail> phases;
  final String? errorMessage;
  final String? interventionMessage;

  const TaskModel({
    required this.id,
    required this.title,
    required this.description,
    required this.status,
    required this.progress,
    required this.createdAt,
    this.updatedAt,
    this.phases = const [],
    this.errorMessage,
    this.interventionMessage,
  });

  TaskModel copyWith({
    String? id,
    String? title,
    String? description,
    TaskStatus? status,
    double? progress,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<TaskPhaseDetail>? phases,
    String? errorMessage,
    String? interventionMessage,
  }) {
    return TaskModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      status: status ?? this.status,
      progress: progress ?? this.progress,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      phases: phases ?? this.phases,
      errorMessage: errorMessage ?? this.errorMessage,
      interventionMessage: interventionMessage ?? this.interventionMessage,
    );
  }

  IconData get statusIcon {
    switch (status) {
      case TaskStatus.pending:
        return Icons.schedule_rounded;
      case TaskStatus.running:
        return Icons.play_circle_rounded;
      case TaskStatus.completed:
        return Icons.check_circle_rounded;
      case TaskStatus.failed:
        return Icons.error_rounded;
      case TaskStatus.needsIntervention:
        return Icons.help_rounded;
      case TaskStatus.cancelled:
        return Icons.cancel_rounded;
    }
  }

  Color get statusColor {
    switch (status) {
      case TaskStatus.pending:
        return Colors.orange;
      case TaskStatus.running:
        return Colors.blue;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.failed:
        return Colors.red;
      case TaskStatus.needsIntervention:
        return Colors.amber;
      case TaskStatus.cancelled:
        return Colors.grey;
    }
  }

  String get statusText {
    switch (status) {
      case TaskStatus.pending:
        return 'Pending';
      case TaskStatus.running:
        return 'Running';
      case TaskStatus.completed:
        return 'Completed';
      case TaskStatus.failed:
        return 'Failed';
      case TaskStatus.needsIntervention:
        return 'Needs Intervention';
      case TaskStatus.cancelled:
        return 'Cancelled';
    }
  }
}