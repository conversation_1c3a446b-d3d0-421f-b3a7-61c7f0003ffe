import 'package:equatable/equatable.dart';

enum ShopItemType {
  environment,
  outfit,
  accessory,
  voice,
  animation,
  companion,
  pet,
}

enum ShopItemRarity { common, rare, epic, legendary }

class ShopItemModel extends Equatable {
  final String id;
  final String name;
  final String description;
  final ShopItemType type;
  final ShopItemRarity rarity;
  final int price;
  final String? imageUrl;
  final String? previewUrl;
  final Map<String, dynamic> metadata;
  final bool isLimited;
  final DateTime? availableUntil;
  final List<String> tags;
  final int levelRequired;

  const ShopItemModel({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.rarity,
    required this.price,
    this.imageUrl,
    this.previewUrl,
    this.metadata = const {},
    this.isLimited = false,
    this.availableUntil,
    this.tags = const [],
    this.levelRequired = 0,
  });

  factory ShopItemModel.environment({
    required String id,
    required String name,
    required String description,
    required int price,
    String? imageUrl,
    ShopItemRarity rarity = ShopItemRarity.common,
    int levelRequired = 0,
    Map<String, dynamic> metadata = const {},
  }) {
    return ShopItemModel(
      id: id,
      name: name,
      description: description,
      type: ShopItemType.environment,
      rarity: rarity,
      price: price,
      imageUrl: imageUrl,
      levelRequired: levelRequired,
      metadata: metadata,
      tags: ['environment', 'background'],
    );
  }

  factory ShopItemModel.outfit({
    required String id,
    required String name,
    required String description,
    required int price,
    String? imageUrl,
    ShopItemRarity rarity = ShopItemRarity.common,
    int levelRequired = 0,
    Map<String, dynamic> metadata = const {},
  }) {
    return ShopItemModel(
      id: id,
      name: name,
      description: description,
      type: ShopItemType.outfit,
      rarity: rarity,
      price: price,
      imageUrl: imageUrl,
      levelRequired: levelRequired,
      metadata: metadata,
      tags: ['outfit', 'clothing'],
    );
  }

  factory ShopItemModel.accessory({
    required String id,
    required String name,
    required String description,
    required int price,
    String? imageUrl,
    ShopItemRarity rarity = ShopItemRarity.common,
    int levelRequired = 0,
    Map<String, dynamic> metadata = const {},
  }) {
    return ShopItemModel(
      id: id,
      name: name,
      description: description,
      type: ShopItemType.accessory,
      rarity: rarity,
      price: price,
      imageUrl: imageUrl,
      levelRequired: levelRequired,
      metadata: metadata,
      tags: ['accessory', 'decoration'],
    );
  }
  
  factory ShopItemModel.pet({
    required String id,
    required String name,
    required String description,
    required int price,
    String? imageUrl,
    ShopItemRarity rarity = ShopItemRarity.common,
    int levelRequired = 0,
    Map<String, dynamic> metadata = const {},
  }) {
    return ShopItemModel(
      id: id,
      name: name,
      description: description,
      type: ShopItemType.pet,
      rarity: rarity,
      price: price,
      imageUrl: imageUrl,
      levelRequired: levelRequired,
      metadata: metadata,
      tags: ['pet', 'companion'],
    );
  }

  ShopItemModel copyWith({
    String? id,
    String? name,
    String? description,
    ShopItemType? type,
    ShopItemRarity? rarity,
    int? price,
    String? imageUrl,
    String? previewUrl,
    Map<String, dynamic>? metadata,
    bool? isLimited,
    DateTime? availableUntil,
    List<String>? tags,
    int? levelRequired,
  }) {
    return ShopItemModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      rarity: rarity ?? this.rarity,
      price: price ?? this.price,
      imageUrl: imageUrl ?? this.imageUrl,
      previewUrl: previewUrl ?? this.previewUrl,
      metadata: metadata ?? this.metadata,
      isLimited: isLimited ?? this.isLimited,
      availableUntil: availableUntil ?? this.availableUntil,
      tags: tags ?? this.tags,
      levelRequired: levelRequired ?? this.levelRequired,
    );
  }

  bool get isAvailable {
    if (!isLimited) return true;
    if (availableUntil == null) return true;
    return DateTime.now().isBefore(availableUntil!);
  }

  String get rarityDisplayName {
    switch (rarity) {
      case ShopItemRarity.common:
        return 'Common';
      case ShopItemRarity.rare:
        return 'Rare';
      case ShopItemRarity.epic:
        return 'Epic';
      case ShopItemRarity.legendary:
        return 'Legendary';
    }
  }

  String get typeDisplayName {
    switch (type) {
      case ShopItemType.environment:
        return 'Environment';
      case ShopItemType.outfit:
        return 'Outfit';
      case ShopItemType.accessory:
        return 'Accessory';
      case ShopItemType.voice:
        return 'Voice';
      case ShopItemType.animation:
        return 'Animation';
      case ShopItemType.companion:
        return 'Companion';
      case ShopItemType.pet:
        return 'Pet';
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'rarity': rarity.name,
      'price': price,
      'imageUrl': imageUrl,
      'previewUrl': previewUrl,
      'metadata': metadata,
      'isLimited': isLimited,
      'availableUntil': availableUntil?.toIso8601String(),
      'tags': tags,
      'levelRequired': levelRequired,
    };
  }

  factory ShopItemModel.fromJson(Map<String, dynamic> json) {
    return ShopItemModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      type: ShopItemType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ShopItemType.environment,
      ),
      rarity: ShopItemRarity.values.firstWhere(
        (e) => e.name == json['rarity'],
        orElse: () => ShopItemRarity.common,
      ),
      price: json['price'] as int,
      imageUrl: json['imageUrl'] as String?,
      previewUrl: json['previewUrl'] as String?,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      isLimited: json['isLimited'] as bool? ?? false,
      availableUntil:
          json['availableUntil'] != null
              ? DateTime.parse(json['availableUntil'] as String)
              : null,
      tags: List<String>.from(json['tags'] ?? []),
      levelRequired: json['levelRequired'] as int? ?? 0,
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    type,
    rarity,
    price,
    imageUrl,
    previewUrl,
    metadata,
    isLimited,
    availableUntil,
    tags,
    levelRequired,
  ];
}

// Default shop items
class DefaultShopItems {
  static List<ShopItemModel> get environments => [
    ShopItemModel.environment(
      id: 'cozy_room',
      name: 'Cozy Room',
      description: 'A warm and comfortable living space',
      price: 0, // Free default
      imageUrl: 'assets/environments/cozy_room.jpg',
    ),
    ShopItemModel.environment(
      id: 'modern_apartment',
      name: 'Modern Apartment',
      description: 'Sleek and contemporary urban living',
      price: 100,
      imageUrl: 'assets/environments/modern_apartment.jpg',
      rarity: ShopItemRarity.rare,
    ),
    ShopItemModel.environment(
      id: 'garden_terrace',
      name: 'Garden Terrace',
      description: 'Beautiful outdoor space with plants',
      price: 150,
      imageUrl: 'assets/environments/garden_terrace.jpg',
      rarity: ShopItemRarity.rare,
    ),
    ShopItemModel.environment(
      id: 'starry_night',
      name: 'Starry Night',
      description: 'Magical nighttime sky view',
      price: 200,
      imageUrl: 'assets/environments/starry_night.jpg',
      rarity: ShopItemRarity.epic,
      levelRequired: 5,
    ),
    ShopItemModel.environment(
      id: 'cherry_blossom',
      name: 'Cherry Blossom',
      description: 'Serene Japanese garden in spring',
      price: 250,
      imageUrl: 'assets/environments/cherry_blossom.jpg',
      rarity: ShopItemRarity.epic,
      levelRequired: 8,
    ),
    ShopItemModel.environment(
      id: 'cyberpunk_city',
      name: 'Cyberpunk City',
      description: 'Futuristic neon-lit cityscape',
      price: 500,
      imageUrl: 'assets/environments/cyberpunk_city.jpg',
      rarity: ShopItemRarity.legendary,
      levelRequired: 15,
    ),
  ];

  static List<ShopItemModel> get outfits => [
    ShopItemModel.outfit(
      id: 'casual_wear',
      name: 'Casual Wear',
      description: 'Comfortable everyday clothing',
      price: 0, // Free default
      imageUrl: 'assets/outfits/casual_wear.jpg',
    ),
    ShopItemModel.outfit(
      id: 'elegant_dress',
      name: 'Elegant Dress',
      description: 'Beautiful formal attire',
      price: 80,
      imageUrl: 'assets/outfits/elegant_dress.jpg',
      rarity: ShopItemRarity.rare,
    ),
    ShopItemModel.outfit(
      id: 'sporty_outfit',
      name: 'Sporty Outfit',
      description: 'Athletic and active wear',
      price: 60,
      imageUrl: 'assets/outfits/sporty_outfit.jpg',
    ),
    ShopItemModel.outfit(
      id: 'winter_coat',
      name: 'Winter Coat',
      description: 'Warm and cozy winter clothing',
      price: 120,
      imageUrl: 'assets/outfits/winter_coat.jpg',
      rarity: ShopItemRarity.rare,
    ),
    ShopItemModel.outfit(
      id: 'party_dress',
      name: 'Party Dress',
      description: 'Glamorous outfit for special occasions',
      price: 200,
      imageUrl: 'assets/outfits/party_dress.jpg',
      rarity: ShopItemRarity.epic,
      levelRequired: 10,
    ),
  ];

  static List<ShopItemModel> get accessories => [
    ShopItemModel.accessory(
      id: 'simple_earrings',
      name: 'Simple Earrings',
      description: 'Classic and elegant earrings',
      price: 30,
      imageUrl: 'assets/accessories/simple_earrings.jpg',
    ),
    ShopItemModel.accessory(
      id: 'stylish_glasses',
      name: 'Stylish Glasses',
      description: 'Trendy eyewear accessory',
      price: 50,
      imageUrl: 'assets/accessories/stylish_glasses.jpg',
    ),
    ShopItemModel.accessory(
      id: 'flower_crown',
      name: 'Flower Crown',
      description: 'Beautiful floral headpiece',
      price: 75,
      imageUrl: 'assets/accessories/flower_crown.jpg',
      rarity: ShopItemRarity.rare,
    ),
    ShopItemModel.accessory(
      id: 'diamond_necklace',
      name: 'Diamond Necklace',
      description: 'Luxurious sparkling jewelry',
      price: 300,
      imageUrl: 'assets/accessories/diamond_necklace.jpg',
      rarity: ShopItemRarity.legendary,
      levelRequired: 20,
    ),
  ];

  static List<ShopItemModel> get companions => [
    ShopItemModel(
      id: 'alex_playful',
      name: 'Alex',
      description:
          'A fun-loving companion who brings energy and excitement to every conversation.',
      type: ShopItemType.companion,
      rarity: ShopItemRarity.rare,
      price: 200,
      levelRequired: 5,
      metadata: {'personality': 'playfulCompanion', 'relationshipRequired': 2},
    ),
    ShopItemModel(
      id: 'sage_mentor',
      name: 'Sage',
      description:
          'A wise mentor who offers thoughtful guidance and deep insights.',
      type: ShopItemType.companion,
      rarity: ShopItemRarity.epic,
      price: 400,
      levelRequired: 10,
      metadata: {'personality': 'wiseMentor', 'relationshipRequired': 3},
    ),
    ShopItemModel(
      id: 'valentine_romantic',
      name: 'Valentine',
      description:
          'A romantic companion for intimate conversations and emotional connection.',
      type: ShopItemType.companion,
      rarity: ShopItemRarity.legendary,
      price: 600,
      levelRequired: 15,
      metadata: {'personality': 'romanticPartner', 'relationshipRequired': 4},
    ),
    ShopItemModel(
      id: 'dr_hope_therapist',
      name: 'Dr. Hope',
      description:
          'A supportive therapist focused on your mental health and wellbeing.',
      type: ShopItemType.companion,
      rarity: ShopItemRarity.legendary,
      price: 800,
      levelRequired: 20,
      metadata: {
        'personality': 'supportiveTherapist',
        'relationshipRequired': 4,
        'premiumRequired': true,
      },
    ),
  ];
  
  static List<ShopItemModel> get pets => [
    ShopItemModel.pet(
      id: 'fluffy_cat',
      name: 'Fluffy Cat',
      description: 'A cute and playful feline companion that purrs when you talk to it.',
      price: 150,
      imageUrl: 'assets/pets/fluffy_cat.jpg',
      rarity: ShopItemRarity.common,
      metadata: {'petType': 'cat', 'behavior': 'playful', 'sound': 'purr'},
    ),
    ShopItemModel.pet(
      id: 'loyal_dog',
      name: 'Loyal Dog',
      description: 'A friendly and loyal canine that follows you everywhere.',
      price: 200,
      imageUrl: 'assets/pets/loyal_dog.jpg',
      rarity: ShopItemRarity.rare,
      metadata: {'petType': 'dog', 'behavior': 'loyal', 'sound': 'bark'},
    ),
    ShopItemModel.pet(
      id: 'colorful_parrot',
      name: 'Colorful Parrot',
      description: 'A vibrant parrot that can learn to repeat your words.',
      price: 250,
      imageUrl: 'assets/pets/colorful_parrot.jpg',
      rarity: ShopItemRarity.rare,
      metadata: {'petType': 'bird', 'behavior': 'talkative', 'sound': 'squawk'},
    ),
    ShopItemModel.pet(
      id: 'mini_dragon',
      name: 'Mini Dragon',
      description: 'A magical miniature dragon that breathes tiny flames.',
      price: 500,
      imageUrl: 'assets/pets/mini_dragon.jpg',
      rarity: ShopItemRarity.epic,
      levelRequired: 10,
      metadata: {'petType': 'fantasy', 'behavior': 'protective', 'sound': 'roar'},
    ),
    ShopItemModel.pet(
      id: 'spirit_fox',
      name: 'Spirit Fox',
      description: 'A mystical nine-tailed fox with magical abilities.',
      price: 800,
      imageUrl: 'assets/pets/spirit_fox.jpg',
      rarity: ShopItemRarity.legendary,
      levelRequired: 20,
      metadata: {'petType': 'fantasy', 'behavior': 'wise', 'sound': 'ethereal'},
    ),
  ];
}
