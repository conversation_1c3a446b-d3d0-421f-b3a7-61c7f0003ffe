import 'package:equatable/equatable.dart';
import 'shop_item_model.dart';

// Preview Camera State
enum CameraMode { 
  defaultMode, 
  preview 
}

class PreviewCameraState extends Equatable {
  final CameraMode mode;
  final bool isTransitioning;
  final Duration transitionDuration;

  const PreviewCameraState({
    this.mode = CameraMode.defaultMode,
    this.isTransitioning = false,
    this.transitionDuration = const Duration(milliseconds: 800),
  });

  PreviewCameraState copyWith({
    CameraMode? mode,
    bool? isTransitioning,
    Duration? transitionDuration,
  }) {
    return PreviewCameraState(
      mode: mode ?? this.mode,
      isTransitioning: isTransitioning ?? this.isTransitioning,
      transitionDuration: transitionDuration ?? this.transitionDuration,
    );
  }

  @override
  List<Object?> get props => [mode, isTransitioning, transitionDuration];
}

// Companion Reaction Model
class CompanionReaction extends Equatable {
  final String text;
  final String animation;
  final String emotion;
  final Duration duration;
  final ReactionType type;

  const CompanionReaction({
    required this.text,
    required this.animation,
    required this.emotion,
    this.duration = const Duration(seconds: 3),
    required this.type,
  });

  @override
  List<Object?> get props => [text, animation, emotion, duration, type];
}

enum ReactionType {
  preview,
  purchaseSuccess,
  purchaseDecline,
  lockedItem,
  categorySwitch,
}

// Cosmetic Application State
class CosmeticApplicationState extends Equatable {
  final Map<ShopItemType, String> appliedCosmetics;
  final Map<ShopItemType, String> equippedCosmetics;
  final bool isApplying;
  final String? currentlyApplying;

  const CosmeticApplicationState({
    this.appliedCosmetics = const {},
    this.equippedCosmetics = const {},
    this.isApplying = false,
    this.currentlyApplying,
  });

  CosmeticApplicationState copyWith({
    Map<ShopItemType, String>? appliedCosmetics,
    Map<ShopItemType, String>? equippedCosmetics,
    bool? isApplying,
    String? currentlyApplying,
  }) {
    return CosmeticApplicationState(
      appliedCosmetics: appliedCosmetics ?? this.appliedCosmetics,
      equippedCosmetics: equippedCosmetics ?? this.equippedCosmetics,
      isApplying: isApplying ?? this.isApplying,
      currentlyApplying: currentlyApplying ?? this.currentlyApplying,
    );
  }

  @override
  List<Object?> get props => [
    appliedCosmetics, 
    equippedCosmetics, 
    isApplying, 
    currentlyApplying
  ];
}

// Main Shop Preview State
class ShopPreviewState extends Equatable {
  final bool isInPreviewMode;
  final ShopItemType? currentCategory;
  final ShopItemModel? selectedItem;
  final List<ShopItemModel> categoryItems;
  final List<ShopItemModel> availableItems;
  final List<ShopItemModel> lockedItems;
  final PreviewCameraState cameraState;
  final CompanionReaction? currentReaction;
  final CosmeticApplicationState cosmeticState;
  final bool isTransitioning;
  final String? error;

  const ShopPreviewState({
    this.isInPreviewMode = false,
    this.currentCategory,
    this.selectedItem,
    this.categoryItems = const [],
    this.availableItems = const [],
    this.lockedItems = const [],
    this.cameraState = const PreviewCameraState(),
    this.currentReaction,
    this.cosmeticState = const CosmeticApplicationState(),
    this.isTransitioning = false,
    this.error,
  });

  ShopPreviewState copyWith({
    bool? isInPreviewMode,
    ShopItemType? currentCategory,
    ShopItemModel? selectedItem,
    List<ShopItemModel>? categoryItems,
    List<ShopItemModel>? availableItems,
    List<ShopItemModel>? lockedItems,
    PreviewCameraState? cameraState,
    CompanionReaction? currentReaction,
    CosmeticApplicationState? cosmeticState,
    bool? isTransitioning,
    String? error,
  }) {
    return ShopPreviewState(
      isInPreviewMode: isInPreviewMode ?? this.isInPreviewMode,
      currentCategory: currentCategory ?? this.currentCategory,
      selectedItem: selectedItem ?? this.selectedItem,
      categoryItems: categoryItems ?? this.categoryItems,
      availableItems: availableItems ?? this.availableItems,
      lockedItems: lockedItems ?? this.lockedItems,
      cameraState: cameraState ?? this.cameraState,
      currentReaction: currentReaction ?? this.currentReaction,
      cosmeticState: cosmeticState ?? this.cosmeticState,
      isTransitioning: isTransitioning ?? this.isTransitioning,
      error: error ?? this.error,
    );
  }

  @override
  List<Object?> get props => [
    isInPreviewMode,
    currentCategory,
    selectedItem,
    categoryItems,
    availableItems,
    lockedItems,
    cameraState,
    currentReaction,
    cosmeticState,
    isTransitioning,
    error,
  ];
}

// Relationship Level Enum
enum RelationshipLevel {
  acquaintance(1, 'Acquaintance'),
  friend(2, 'Friend'),
  closeFriend(3, 'Close Friend'),
  intimate(4, 'Intimate');

  const RelationshipLevel(this.level, this.displayName);
  
  final int level;
  final String displayName;

  static RelationshipLevel fromLevel(int level) {
    return RelationshipLevel.values.firstWhere(
      (r) => r.level == level,
      orElse: () => RelationshipLevel.acquaintance,
    );
  }
}