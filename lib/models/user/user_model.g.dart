// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserModelAdapter extends TypeAdapter<UserModel> {
  @override
  final int typeId = 0;

  @override
  UserModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserModel(
      id: fields[0] as String,
      name: fields[1] as String,
      email: fields[2] as String,
      photoURL: fields[3] as String?,
      createdAt: fields[4] as DateTime,
      progress: fields[5] as UserProgress,
      selectedPersonality: fields[6] as CompanionPersonality,
      selectedEnvironment: fields[7] as String,
      ownedEnvironments: (fields[8] as List).cast<String>(),
      ownedOutfits: (fields[9] as List).cast<String>(),
      preferences: (fields[10] as Map).cast<String, dynamic>(),
      lastLogin: fields[11] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, UserModel obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.email)
      ..writeByte(3)
      ..write(obj.photoURL)
      ..writeByte(4)
      ..write(obj.createdAt)
      ..writeByte(5)
      ..write(obj.progress)
      ..writeByte(6)
      ..write(obj.selectedPersonality)
      ..writeByte(7)
      ..write(obj.selectedEnvironment)
      ..writeByte(8)
      ..write(obj.ownedEnvironments)
      ..writeByte(9)
      ..write(obj.ownedOutfits)
      ..writeByte(10)
      ..write(obj.preferences)
      ..writeByte(11)
      ..write(obj.lastLogin);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UserProgressAdapter extends TypeAdapter<UserProgress> {
  @override
  final int typeId = 1;

  @override
  UserProgress read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserProgress(
      xp: fields[0] as int,
      level: fields[1] as int,
      hearts: fields[2] as int,
      messagesCount: fields[3] as int,
      voiceMessagesCount: fields[4] as int,
      lastDailyBonus: fields[5] as DateTime?,
      achievements: (fields[6] as List).cast<String>(),
      totalTimeSpent: fields[7] as int,
    );
  }

  @override
  void write(BinaryWriter writer, UserProgress obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.xp)
      ..writeByte(1)
      ..write(obj.level)
      ..writeByte(2)
      ..write(obj.hearts)
      ..writeByte(3)
      ..write(obj.messagesCount)
      ..writeByte(4)
      ..write(obj.voiceMessagesCount)
      ..writeByte(5)
      ..write(obj.lastDailyBonus)
      ..writeByte(6)
      ..write(obj.achievements)
      ..writeByte(7)
      ..write(obj.totalTimeSpent);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserProgressAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CompanionPersonalityAdapter extends TypeAdapter<CompanionPersonality> {
  @override
  final int typeId = 2;

  @override
  CompanionPersonality read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return CompanionPersonality.caringFriend;
      case 1:
        return CompanionPersonality.playfulCompanion;
      case 2:
        return CompanionPersonality.wiseMentor;
      case 3:
        return CompanionPersonality.romanticPartner;
      case 4:
        return CompanionPersonality.supportiveTherapist;
      default:
        return CompanionPersonality.caringFriend;
    }
  }

  @override
  void write(BinaryWriter writer, CompanionPersonality obj) {
    switch (obj) {
      case CompanionPersonality.caringFriend:
        writer.writeByte(0);
        break;
      case CompanionPersonality.playfulCompanion:
        writer.writeByte(1);
        break;
      case CompanionPersonality.wiseMentor:
        writer.writeByte(2);
        break;
      case CompanionPersonality.romanticPartner:
        writer.writeByte(3);
        break;
      case CompanionPersonality.supportiveTherapist:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CompanionPersonalityAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
