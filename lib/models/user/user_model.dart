import 'package:hive/hive.dart';
import 'package:equatable/equatable.dart';

part 'user_model.g.dart';

@HiveType(typeId: 0)
class UserModel extends Equatable {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String name;
  
  @HiveField(2)
  final String email;
  
  @HiveField(3)
  final String? photoURL;
  
  @HiveField(4)
  final DateTime createdAt;
  
  @HiveField(5)
  final UserProgress progress;
  
  @HiveField(6)
  final CompanionPersonality selectedPersonality;
  
  @HiveField(7)
  final String selectedEnvironment;
  
  @HiveField(8)
  final List<String> ownedEnvironments;
  
  @HiveField(9)
  final List<String> ownedOutfits;
  
  @HiveField(10)
  final Map<String, dynamic> preferences;
  
  @HiveField(11)
  final DateTime? lastLogin;

  const UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.photoURL,
    required this.createdAt,
    required this.progress,
    required this.selectedPersonality,
    required this.selectedEnvironment,
    required this.ownedEnvironments,
    required this.ownedOutfits,
    required this.preferences,
    this.lastLogin,
  });

  factory UserModel.initial({
    required String id,
    required String name,
    required String email,
    String? photoURL,
  }) {
    return UserModel(
      id: id,
      name: name,
      email: email,
      photoURL: photoURL,
      createdAt: DateTime.now(),
      progress: UserProgress.initial(),
      selectedPersonality: CompanionPersonality.caringFriend,
      selectedEnvironment: 'cozy_room',
      ownedEnvironments: ['cozy_room'],
      ownedOutfits: ['default'],
      preferences: {
        'voice_enabled': true,
        'notifications_enabled': true,
        'auto_play_voice': true,
        'dark_mode': true,
      },
    );
  }

  UserModel copyWith({
    String? id,
    String? name,
    String? email,
    String? photoURL,
    DateTime? createdAt,
    UserProgress? progress,
    CompanionPersonality? selectedPersonality,
    String? selectedEnvironment,
    List<String>? ownedEnvironments,
    List<String>? ownedOutfits,
    Map<String, dynamic>? preferences,
    DateTime? lastLogin,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      photoURL: photoURL ?? this.photoURL,
      createdAt: createdAt ?? this.createdAt,
      progress: progress ?? this.progress,
      selectedPersonality: selectedPersonality ?? this.selectedPersonality,
      selectedEnvironment: selectedEnvironment ?? this.selectedEnvironment,
      ownedEnvironments: ownedEnvironments ?? this.ownedEnvironments,
      ownedOutfits: ownedOutfits ?? this.ownedOutfits,
      preferences: preferences ?? this.preferences,
      lastLogin: lastLogin ?? this.lastLogin,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        photoURL,
        createdAt,
        progress,
        selectedPersonality,
        selectedEnvironment,
        ownedEnvironments,
        ownedOutfits,
        preferences,
        lastLogin,
      ];

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'photoURL': photoURL,
      'createdAt': createdAt.toIso8601String(),
      'progress': {
        'xp': progress.xp,
        'level': progress.level,
        'hearts': progress.hearts,
        'messagesCount': progress.messagesCount,
        'voiceMessagesCount': progress.voiceMessagesCount,
        'lastDailyBonus': progress.lastDailyBonus?.toIso8601String(),
        'achievements': progress.achievements,
        'totalTimeSpent': progress.totalTimeSpent,
      },
      'selectedPersonality': selectedPersonality.name,
      'selectedEnvironment': selectedEnvironment,
      'ownedEnvironments': ownedEnvironments,
      'ownedOutfits': ownedOutfits,
      'preferences': preferences,
    };
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      photoURL: json['photoURL'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      progress: UserProgress(
        xp: (json['progress']?['xp'] ?? 0) as int,
        level: (json['progress']?['level'] ?? 0) as int,
        hearts: (json['progress']?['hearts'] ?? 100) as int,
        messagesCount: (json['progress']?['messagesCount'] ?? 0) as int,
        voiceMessagesCount: (json['progress']?['voiceMessagesCount'] ?? 0) as int,
        lastDailyBonus: json['progress']?['lastDailyBonus'] != null 
            ? DateTime.parse(json['progress']['lastDailyBonus'] as String) 
            : null,
        achievements: List<String>.from((json['progress']?['achievements'] ?? []) as List),
        totalTimeSpent: (json['progress']?['totalTimeSpent'] ?? 0) as int,
      ),
      selectedPersonality: CompanionPersonality.values.firstWhere(
        (e) => e.name == (json['selectedPersonality'] ?? 'caringFriend'),
        orElse: () => CompanionPersonality.caringFriend,
      ),
      selectedEnvironment: json['selectedEnvironment'] as String? ?? 'default_environment',
      ownedEnvironments: List<String>.from((json['ownedEnvironments'] ?? []) as List),
      ownedOutfits: List<String>.from((json['ownedOutfits'] ?? []) as List),
      preferences: Map<String, dynamic>.from((json['preferences'] ?? {}) as Map),
    );
  }
}

@HiveType(typeId: 1)
class UserProgress {
  @HiveField(0)
  final int xp;
  
  @HiveField(1)
  final int level;
  
  @HiveField(2)
  final int hearts;
  
  @HiveField(3)
  final int messagesCount;
  
  @HiveField(4)
  final int voiceMessagesCount;
  
  @HiveField(5)
  final DateTime? lastDailyBonus;
  
  @HiveField(6)
  final List<String> achievements;
  
  @HiveField(7)
  final int totalTimeSpent; // in minutes

  const UserProgress({
    required this.xp,
    required this.level,
    required this.hearts,
    required this.messagesCount,
    required this.voiceMessagesCount,
    this.lastDailyBonus,
    required this.achievements,
    required this.totalTimeSpent,
  });

  factory UserProgress.initial() {
    return UserProgress(
      xp: 0,
      level: 0,
      hearts: 100,
      messagesCount: 0,
      voiceMessagesCount: 0,
      lastDailyBonus: null,
      achievements: [],
      totalTimeSpent: 0,
    );
  }

  UserProgress copyWith({
    int? xp,
    int? level,
    int? hearts,
    int? messagesCount,
    int? voiceMessagesCount,
    DateTime? lastDailyBonus,
    List<String>? achievements,
    int? totalTimeSpent,
  }) {
    return UserProgress(
      xp: xp ?? this.xp,
      level: level ?? this.level,
      hearts: hearts ?? this.hearts,
      messagesCount: messagesCount ?? this.messagesCount,
      voiceMessagesCount: voiceMessagesCount ?? this.voiceMessagesCount,
      lastDailyBonus: lastDailyBonus ?? this.lastDailyBonus,
      achievements: achievements ?? this.achievements,
      totalTimeSpent: totalTimeSpent ?? this.totalTimeSpent,
    );
  }

  double get progressToNextLevel {
    if (level >= 10) return 1.0; // Max level
    
    final currentLevelXP = level > 0 ? _levelThresholds[level - 1] : 0;
    final nextLevelXP = _levelThresholds[level];
    final progressXP = xp - currentLevelXP;
    final requiredXP = nextLevelXP - currentLevelXP;
    
    return progressXP / requiredXP;
  }

  int get xpToNextLevel {
    if (level >= 10) return 0;
    return _levelThresholds[level] - xp;
  }

  Map<String, dynamic> toJson() {
    return {
      'xp': xp,
      'level': level,
      'hearts': hearts,
      'messagesCount': messagesCount,
      'voiceMessagesCount': voiceMessagesCount,
      'lastDailyBonus': lastDailyBonus?.toIso8601String(),
      'achievements': achievements,
      'totalTimeSpent': totalTimeSpent,
    };
  }

  static const List<int> _levelThresholds = [
    0, 100, 250, 500, 1000, 2000, 4000, 8000, 15000, 30000, 50000
  ];
}

@HiveType(typeId: 2)
enum CompanionPersonality {
  @HiveField(0)
  caringFriend,
  
  @HiveField(1)
  playfulCompanion,
  
  @HiveField(2)
  wiseMentor,
  
  @HiveField(3)
  romanticPartner,
  
  @HiveField(4)
  supportiveTherapist,
}

extension CompanionPersonalityExtension on CompanionPersonality {
  String get displayName {
    switch (this) {
      case CompanionPersonality.caringFriend:
        return 'Caring Friend';
      case CompanionPersonality.playfulCompanion:
        return 'Playful Companion';
      case CompanionPersonality.wiseMentor:
        return 'Wise Mentor';
      case CompanionPersonality.romanticPartner:
        return 'Romantic Partner';
      case CompanionPersonality.supportiveTherapist:
        return 'Supportive Therapist';
    }
  }

  String get description {
    switch (this) {
      case CompanionPersonality.caringFriend:
        return 'A warm, understanding friend who always listens';
      case CompanionPersonality.playfulCompanion:
        return 'Fun-loving and energetic, always ready for adventure';
      case CompanionPersonality.wiseMentor:
        return 'Thoughtful and insightful, offering guidance and wisdom';
      case CompanionPersonality.romanticPartner:
        return 'Affectionate and romantic, sharing intimate moments';
      case CompanionPersonality.supportiveTherapist:
        return 'Patient and understanding, focused on your wellbeing';
    }
  }

  String get voiceStyle {
    switch (this) {
      case CompanionPersonality.caringFriend:
        return 'warm and gentle';
      case CompanionPersonality.playfulCompanion:
        return 'energetic and cheerful';
      case CompanionPersonality.wiseMentor:
        return 'calm and thoughtful';
      case CompanionPersonality.romanticPartner:
        return 'soft and intimate';
      case CompanionPersonality.supportiveTherapist:
        return 'patient and soothing';
    }
  }
}
