import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../providers/progress_provider.dart';
import '../../models/user/user_model.dart';
import '../../services/storage/storage_service.dart';
import '../../widgets/common/glassmorphic_container.dart';
import '../../widgets/profile/achievement_card.dart';
import '../../widgets/profile/progress_card.dart';
import '../../widgets/profile/stats_card.dart';

class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(currentUserProvider);
    final progressState = ref.watch(progressProvider);

    if (user == null) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(user),
              _buildProgressOverview(user.progress),
              _buildTabBar(),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildStatsTab(user, progressState),
                    _buildAchievementsTab(progressState.achievements),
                    _buildSettingsTab(user),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(UserModel user) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.arrow_back_rounded),
          ),
          Expanded(
            child: GlassmorphicContainer(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  // Profile Picture
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      gradient: AppTheme.primaryGradient,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 2,
                      ),
                    ),
                    child: user.photoURL != null
                        ? ClipOval(
                            child: Image.network(
                              user.photoURL!,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  const Icon(
                                Icons.person,
                                color: Colors.white,
                                size: 30,
                              ),
                            ),
                          )
                        : const Icon(
                            Icons.person,
                            color: Colors.white,
                            size: 30,
                          ),
                  ),
                  const SizedBox(width: 16),
                  
                  // User Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          user.name,
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Level ${user.progress.level}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppTheme.primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Text(
                              AppConstants.currencySymbol,
                              style: const TextStyle(fontSize: 14),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${user.progress.hearts}',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppTheme.textSecondaryColor,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  // Edit Button
                  IconButton(
                    onPressed: _showEditProfileDialog,
                    icon: const Icon(Icons.edit_rounded),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressOverview(UserProgress progress) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ProgressCard(
        level: progress.level,
        currentXP: progress.xp,
        progressToNext: progress.progressToNextLevel,
        xpToNext: progress.xpToNextLevel,
      ).animate().slideX(duration: 600.ms, curve: Curves.easeOutBack),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(12),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.textSecondaryColor,
        tabs: const [
          Tab(text: 'Stats'),
          Tab(text: 'Achievements'),
          Tab(text: 'Settings'),
        ],
      ),
    );
  }

  Widget _buildStatsTab(UserModel user, dynamic progressState) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          StatsCard(
            title: 'Chat Statistics',
            stats: {
              'Messages Sent': user.progress.messagesCount,
              'Voice Messages': user.progress.voiceMessagesCount,
              'Time Spent': '${user.progress.totalTimeSpent} min',
              'Days Active': progressState.daysActive,
            },
          ).animate().fadeIn(delay: 100.ms),
          
          const SizedBox(height: 16),
          
          StatsCard(
            title: 'Progress',
            stats: {
              'Current Level': user.progress.level,
              'Total XP': user.progress.xp,
              'Hearts Earned': progressState.totalHeartsEarned,
              'Items Purchased': user.ownedEnvironments.length + user.ownedOutfits.length,
            },
          ).animate().fadeIn(delay: 200.ms),
          
          const SizedBox(height: 16),
          
          StatsCard(
            title: 'Companion',
            stats: {
              'Personality': _getPersonalityDisplayName(user.selectedPersonality),
              'Environment': user.selectedEnvironment.replaceAll('_', ' ').toUpperCase(),
              'Conversations': progressState.conversationCount,
              'Favorite Time': progressState.favoriteTimeOfDay,
            },
          ).animate().fadeIn(delay: 300.ms),
        ],
      ),
    );
  }

  Widget _buildAchievementsTab(List<String> achievements) {
    final allAchievements = _getAllAchievements();
    
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.2,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: allAchievements.length,
      itemBuilder: (context, index) {
        final achievement = allAchievements[index];
        final isUnlocked = achievements.contains(achievement['id']);
        
        return AchievementCard(
          title: achievement['title'],
          description: achievement['description'],
          icon: achievement['icon'],
          isUnlocked: isUnlocked,
          rarity: achievement['rarity'],
        ).animate(delay: Duration(milliseconds: index * 100))
            .slideY(begin: 0.3, curve: Curves.easeOutBack);
      },
    );
  }

  Widget _buildSettingsTab(UserModel user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildSettingsSection(
            'Companion Settings',
            [
              _buildSettingsTile(
                'Personality',
                _getPersonalityDisplayName(user.selectedPersonality),
                Icons.psychology_rounded,
                () => _showPersonalitySelector(),
              ),
              _buildSettingsTile(
                'Voice Settings',
                'Configure voice preferences',
                Icons.record_voice_over_rounded,
                () => _showVoiceSettings(),
              ),
              _buildSettingsTile(
                'Notifications',
                'Manage notification preferences',
                Icons.notifications_rounded,
                () => _showNotificationSettings(),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          _buildSettingsSection(
            'Account',
            [
              _buildSettingsTile(
                'Privacy',
                'Data and privacy settings',
                Icons.privacy_tip_rounded,
                () => _showPrivacySettings(),
              ),
              _buildSettingsTile(
                'Export Data',
                'Download your conversation history',
                Icons.download_rounded,
                () => _exportData(),
              ),
              _buildSettingsTile(
                'Fix Data Issues',
                'Fix personality display issues',
                Icons.build_rounded,
                () => _fixDataIssues(),
              ),
              _buildSettingsTile(
                'Sign Out',
                'Sign out of your account',
                Icons.logout_rounded,
                () => _showSignOutDialog(),
                isDestructive: true,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection(String title, List<Widget> children) {
    return GlassmorphicContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSettingsTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap, {
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? AppTheme.errorColor : AppTheme.primaryColor,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isDestructive ? AppTheme.errorColor : null,
        ),
      ),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.chevron_right_rounded),
      onTap: onTap,
    );
  }

  List<Map<String, dynamic>> _getAllAchievements() {
    return [
      {
        'id': 'first_chat',
        'title': 'First Words',
        'description': 'Send your first message',
        'icon': Icons.chat_rounded,
        'rarity': 'common',
      },
      {
        'id': 'voice_pioneer',
        'title': 'Voice Pioneer',
        'description': 'Send your first voice message',
        'icon': Icons.mic_rounded,
        'rarity': 'common',
      },
      {
        'id': 'level_5',
        'title': 'Getting Started',
        'description': 'Reach level 5',
        'icon': Icons.star_rounded,
        'rarity': 'common',
      },
      {
        'id': 'shopaholic',
        'title': 'Shopaholic',
        'description': 'Purchase 5 items from the shop',
        'icon': Icons.shopping_bag_rounded,
        'rarity': 'rare',
      },
      {
        'id': 'chatterbox',
        'title': 'Chatterbox',
        'description': 'Send 100 messages',
        'icon': Icons.forum_rounded,
        'rarity': 'rare',
      },
      {
        'id': 'level_10',
        'title': 'Dedicated Friend',
        'description': 'Reach level 10',
        'icon': Icons.favorite_rounded,
        'rarity': 'epic',
      },
    ];
  }

  void _showEditProfileDialog() {
    // TODO: Implement edit profile dialog
  }

  void _showPersonalitySelector() {
    // TODO: Implement personality selector
  }

  void _showVoiceSettings() {
    // TODO: Implement voice settings
  }

  void _showNotificationSettings() {
    // TODO: Implement notification settings
  }

  void _showPrivacySettings() {
    // TODO: Implement privacy settings
  }

  void _exportData() {
    // TODO: Implement data export
  }

  String _getPersonalityDisplayName(dynamic personality) {
    if (personality is CompanionPersonality) {
      return _getPersonalityName(personality);
    }
    // Fallback for dynamic type - cast it
    try {
      return _getPersonalityName(personality as CompanionPersonality);
    } catch (e) {
      // Ultimate fallback
      return 'Caring Friend';
    }
  }

  String _getPersonalityName(CompanionPersonality personality) {
    switch (personality) {
      case CompanionPersonality.caringFriend:
        return 'Caring Friend';
      case CompanionPersonality.playfulCompanion:
        return 'Playful Companion';
      case CompanionPersonality.wiseMentor:
        return 'Wise Mentor';
      case CompanionPersonality.romanticPartner:
        return 'Romantic Partner';
      case CompanionPersonality.supportiveTherapist:
        return 'Supportive Therapist';
    }
  }

  Future<void> _fixDataIssues() async {
    try {
      await StorageService.fixPersonalityTypeIssue();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Data issues fixed! Please restart the app.'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error fixing data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showSignOutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.surfaceColor,
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(authProvider.notifier).signOut();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }
}
