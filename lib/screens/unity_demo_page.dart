import 'package:flutter/material.dart';
import 'package:flutter_unity_widget/flutter_unity_widget.dart';

void main() {
  runApp(
    const MaterialApp(
      home: UnityDemoScreen(),
    ),
  );
}

class UnityDemoScreen extends StatefulWidget {
  const UnityDemoScreen({super.key});

  @override
  State<UnityDemoScreen> createState() => _UnityDemoScreenState();
}

class _UnityDemoScreenState extends State<UnityDemoScreen> {
  static final GlobalKey<ScaffoldState> _scaffoldKey =
      GlobalKey<ScaffoldState>();
  UnityWidgetController? _unityWidgetController;
  bool _isUnityLoaded = false;
  String? _errorMessage;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        title: const Text('Unity Demo'),
        backgroundColor: Colors.blue,
      ),
      body: SafeArea(
        child: _errorMessage != null
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error, color: Colors.red, size: 64),
                    const SizedBox(height: 16),
                    Text(
                      'Unity Error:',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _errorMessage!,
                      textAlign: TextAlign.center,
                      style: const TextStyle(color: Colors.red),
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _retryUnity,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
            : Stack(
                children: [
                  Container(
                    color: Colors.black,
                    child: UnityWidget(
                      onUnityCreated: onUnityCreated,
                      onUnitySceneLoaded: onUnitySceneLoaded,
                      onUnityMessage: onUnityMessage,
                      fullscreen: false,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  // Callback that connects the created controller to the unity controller
  void onUnityCreated(UnityWidgetController controller) {
    print('Unity Created');
    _unityWidgetController = controller;
    
    // Add error handling
    try {
      // You can load a specific scene here if needed
      // controller.postMessage('GameManager', 'LoadScene', 'SampleScene');
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to initialize Unity: $e';
      });
    }
  }

  // Callback for when Unity scene is loaded
  void onUnitySceneLoaded(SceneLoaded? scene) {
    print('Unity Scene Loaded: ${scene?.name}');
    setState(() {
      _isUnityLoaded = true;
    });
  }

  // Callback for Unity messages
  void onUnityMessage(message) {
    print('Unity Message: $message');
  }

  // Retry Unity initialization
  void _retryUnity() {
    setState(() {
      _errorMessage = null;
      _isUnityLoaded = false;
    });
  }

  @override
  void dispose() {
    _unityWidgetController?.dispose();
    super.dispose();
  }
}