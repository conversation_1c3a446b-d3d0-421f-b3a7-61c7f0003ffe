import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:ui';

import '../../core/theme/app_theme.dart';
import '../../providers/auth_provider.dart';
import '../../models/user/user_model.dart';
import '../../widgets/common/glassmorphic_container.dart';

class PrivacySettingsPage extends ConsumerStatefulWidget {
  const PrivacySettingsPage({super.key});

  @override
  ConsumerState<PrivacySettingsPage> createState() => _PrivacySettingsPageState();
}

class _PrivacySettingsPageState extends ConsumerState<PrivacySettingsPage> {
  bool _dataCollection = true;
  bool _analyticsEnabled = true;
  bool _crashReporting = true;
  bool _personalizedAds = false;
  bool _shareUsageData = false;
  bool _conversationHistory = true;
  bool _voiceDataStorage = true;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _loadPrivacySettings();
  }

  void _loadPrivacySettings() {
    final user = ref.read(currentUserProvider);
    if (user != null) {
      setState(() {
        _dataCollection = user.preferences['data_collection'] as bool? ?? true;
        _analyticsEnabled = user.preferences['analytics_enabled'] as bool? ?? true;
        _crashReporting = user.preferences['crash_reporting'] as bool? ?? true;
        _personalizedAds = user.preferences['personalized_ads'] as bool? ?? false;
        _shareUsageData = user.preferences['share_usage_data'] as bool? ?? false;
        _conversationHistory = user.preferences['conversation_history'] as bool? ?? true;
        _voiceDataStorage = user.preferences['voice_data_storage'] as bool? ?? true;
      });
    }
  }

  void _onSettingChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  Future<void> _saveSettings() async {
    try {
      final user = ref.read(currentUserProvider);
      if (user == null) return;

      final updatedUser = user.copyWith(
        preferences: {
          ...user.preferences,
          'data_collection': _dataCollection,
          'analytics_enabled': _analyticsEnabled,
          'crash_reporting': _crashReporting,
          'personalized_ads': _personalizedAds,
          'share_usage_data': _shareUsageData,
          'conversation_history': _conversationHistory,
          'voice_data_storage': _voiceDataStorage,
        },
      );

      await ref.read(authProvider.notifier).updateUser(updatedUser);
      
      setState(() {
        _hasChanges = false;
      });

      _showSuccessSnackBar('Privacy settings saved successfully');
    } catch (e) {
      _showErrorSnackBar('Failed to save settings: $e');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(isTablet),
              Expanded(
                child: _buildContent(isTablet),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(isTablet ? 24 : 16),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: Icon(
                Icons.arrow_back_rounded,
                color: Colors.white,
                size: isTablet ? 26 : 24,
              ),
              padding: EdgeInsets.all(isTablet ? 10 : 8),
            ),
          ),
          SizedBox(width: isTablet ? 20 : 16),
          Expanded(
            child: Text(
              'Privacy & Data',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: isTablet ? 24 : 20,
              ),
            ),
          ),
          if (_hasChanges)
            Container(
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(12),
              ),
              child: TextButton(
                onPressed: _saveSettings,
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: isTablet ? 20 : 16,
                    vertical: isTablet ? 12 : 8,
                  ),
                ),
                child: Text(
                  'Save',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: isTablet ? 16 : 14,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildContent(bool isTablet) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(isTablet ? 24 : 16),
      child: Column(
        children: [
          _buildDataCollection(isTablet),
          SizedBox(height: isTablet ? 24 : 16),
          _buildConversationData(isTablet),
          SizedBox(height: isTablet ? 24 : 16),
          _buildDataManagement(isTablet),
        ],
      ),
    );
  }

  Widget _buildDataCollection(bool isTablet) {
    return GlassmorphicContainer(
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 28 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Data Collection',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: isTablet ? 20 : 18,
              ),
            ),
            SizedBox(height: isTablet ? 24 : 16),
            _buildToggleTile(
              title: 'Analytics & Performance',
              subtitle: 'Help improve the app with usage analytics',
              value: _analyticsEnabled,
              onChanged: (value) {
                setState(() {
                  _analyticsEnabled = value;
                });
                _onSettingChanged();
              },
              icon: Icons.analytics_rounded,
              isTablet: isTablet,
            ),
            SizedBox(height: isTablet ? 16 : 12),
            _buildToggleTile(
              title: 'Crash Reporting',
              subtitle: 'Send crash reports to help fix bugs',
              value: _crashReporting,
              onChanged: (value) {
                setState(() {
                  _crashReporting = value;
                });
                _onSettingChanged();
              },
              icon: Icons.bug_report_rounded,
              isTablet: isTablet,
            ),
            SizedBox(height: isTablet ? 16 : 12),
            _buildToggleTile(
              title: 'Personalized Ads',
              subtitle: 'Show ads based on your interests',
              value: _personalizedAds,
              onChanged: (value) {
                setState(() {
                  _personalizedAds = value;
                });
                _onSettingChanged();
              },
              icon: Icons.ads_click_rounded,
              isTablet: isTablet,
            ),
            SizedBox(height: isTablet ? 16 : 12),
            _buildToggleTile(
              title: 'Share Usage Data',
              subtitle: 'Share anonymized usage data for research',
              value: _shareUsageData,
              onChanged: (value) {
                setState(() {
                  _shareUsageData = value;
                });
                _onSettingChanged();
              },
              icon: Icons.share_rounded,
              isTablet: isTablet,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToggleTile({
    required String title,
    required String subtitle,
    required bool value,
    required void Function(bool) onChanged,
    required IconData icon,
    required bool isTablet,
  }) {
    return Container(
      padding: EdgeInsets.all(isTablet ? 16 : 12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(isTablet ? 12 : 10),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: AppTheme.primaryColor,
              size: isTablet ? 24 : 20,
            ),
          ),
          SizedBox(width: isTablet ? 16 : 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                    fontSize: isTablet ? 16 : 14,
                  ),
                ),
                SizedBox(height: isTablet ? 4 : 2),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textSecondaryColor,
                    fontSize: isTablet ? 14 : 12,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.primaryColor,
            inactiveThumbColor: AppTheme.textSecondaryColor,
            inactiveTrackColor: Colors.white.withValues(alpha: 0.1),
          ),
        ],
      ),
    );
  }

  Widget _buildConversationData(bool isTablet) {
    return GlassmorphicContainer(
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 28 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Conversation Data',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: isTablet ? 20 : 18,
              ),
            ),
            SizedBox(height: isTablet ? 24 : 16),
            _buildToggleTile(
              title: 'Save Conversation History',
              subtitle: 'Store your conversations for context and memory',
              value: _conversationHistory,
              onChanged: (value) {
                setState(() {
                  _conversationHistory = value;
                });
                _onSettingChanged();
              },
              icon: Icons.history_rounded,
              isTablet: isTablet,
            ),
            SizedBox(height: isTablet ? 16 : 12),
            _buildToggleTile(
              title: 'Voice Data Storage',
              subtitle: 'Store voice recordings for improved responses',
              value: _voiceDataStorage,
              onChanged: (value) {
                setState(() {
                  _voiceDataStorage = value;
                });
                _onSettingChanged();
              },
              icon: Icons.mic_rounded,
              isTablet: isTablet,
            ),
            SizedBox(height: isTablet ? 20 : 16),
            Container(
              padding: EdgeInsets.all(isTablet ? 16 : 12),
              decoration: BoxDecoration(
                color: AppTheme.warningColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.warningColor.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline_rounded,
                    color: AppTheme.warningColor,
                    size: isTablet ? 24 : 20,
                  ),
                  SizedBox(width: isTablet ? 12 : 8),
                  Expanded(
                    child: Text(
                      'Disabling conversation history will reset your relationship progress and memories with Ella.',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.warningColor,
                        fontSize: isTablet ? 14 : 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataManagement(bool isTablet) {
    return GlassmorphicContainer(
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 28 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Data Management',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: isTablet ? 20 : 18,
              ),
            ),
            SizedBox(height: isTablet ? 24 : 16),
            _buildActionTile(
              title: 'Download My Data',
              subtitle: 'Get a copy of all your data',
              icon: Icons.download_rounded,
              onTap: _downloadData,
              isTablet: isTablet,
            ),
            SizedBox(height: isTablet ? 16 : 12),
            _buildActionTile(
              title: 'Clear Conversation History',
              subtitle: 'Delete all conversation data',
              icon: Icons.delete_outline_rounded,
              onTap: _clearConversationHistory,
              isTablet: isTablet,
              isDestructive: true,
            ),
            SizedBox(height: isTablet ? 16 : 12),
            _buildActionTile(
              title: 'Delete Account',
              subtitle: 'Permanently delete your account and all data',
              icon: Icons.delete_forever_rounded,
              onTap: _deleteAccount,
              isTablet: isTablet,
              isDestructive: true,
            ),
            SizedBox(height: isTablet ? 20 : 16),
            Container(
              padding: EdgeInsets.all(isTablet ? 16 : 12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.1),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Privacy Policy',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                      fontSize: isTablet ? 16 : 14,
                    ),
                  ),
                  SizedBox(height: isTablet ? 8 : 4),
                  Text(
                    'Learn more about how we collect, use, and protect your data.',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.textSecondaryColor,
                      fontSize: isTablet ? 14 : 12,
                    ),
                  ),
                  SizedBox(height: isTablet ? 12 : 8),
                  TextButton(
                    onPressed: _viewPrivacyPolicy,
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.zero,
                    ),
                    child: Text(
                      'View Privacy Policy',
                      style: TextStyle(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.w600,
                        fontSize: isTablet ? 14 : 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    required bool isTablet,
    bool isDestructive = false,
  }) {
    final color = isDestructive ? AppTheme.errorColor : AppTheme.primaryColor;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: EdgeInsets.all(isTablet ? 16 : 12),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(isTablet ? 12 : 10),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: isTablet ? 24 : 20,
                ),
              ),
              SizedBox(width: isTablet ? 16 : 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isDestructive ? AppTheme.errorColor : Colors.white,
                        fontSize: isTablet ? 16 : 14,
                      ),
                    ),
                    SizedBox(height: isTablet ? 4 : 2),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondaryColor,
                        fontSize: isTablet ? 14 : 12,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.chevron_right_rounded,
                color: AppTheme.textSecondaryColor,
                size: isTablet ? 24 : 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _downloadData() {
    // TODO: Implement data download functionality
    _showSuccessSnackBar('Data download request submitted');
  }

  void _clearConversationHistory() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.surfaceColor,
        title: const Text('Clear Conversation History'),
        content: const Text(
          'This will permanently delete all your conversations with Ella. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement clear conversation history
              _showSuccessSnackBar('Conversation history cleared');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _deleteAccount() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.surfaceColor,
        title: const Text('Delete Account'),
        content: const Text(
          'This will permanently delete your account and all associated data. This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement account deletion
              _showErrorSnackBar('Account deletion not implemented yet');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _viewPrivacyPolicy() {
    // TODO: Implement privacy policy view
    _showSuccessSnackBar('Opening privacy policy...');
  }
}
