import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:ui';

import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import '../../widgets/common/glassmorphic_container.dart';

class HelpSupportPage extends ConsumerStatefulWidget {
  const HelpSupportPage({super.key});

  @override
  ConsumerState<HelpSupportPage> createState() => _HelpSupportPageState();
}

class _HelpSupportPageState extends ConsumerState<HelpSupportPage> {
  final List<FAQItem> _faqItems = [
    FAQItem(
      question: 'How do I change <PERSON>\'s voice?',
      answer: 'Go to Settings > Voice Settings and select from the available voice options. You can also adjust speech rate and pitch to customize how <PERSON> sounds.',
    ),
    FAQItem(
      question: 'Why can\'t I access certain features?',
      answer: 'Some features are unlocked as your relationship with <PERSON> grows. Continue chatting and engaging to unlock new conversation topics, outfits, and environments.',
    ),
    FAQItem(
      question: 'How do I backup my conversations?',
      answer: 'Your conversations are automatically saved to your account. You can download a copy of your data from Settings > Privacy & Data > Download My Data.',
    ),
    FAQItem(
      question: 'Can I use Ella offline?',
      answer: 'Ella requires an internet connection to provide responses. However, you can view your conversation history and change settings while offline.',
    ),
    FAQItem(
      question: 'How do I reset my relationship progress?',
      answer: 'You can reset your progress by clearing conversation history in Settings > Privacy & Data. Note that this action cannot be undone.',
    ),
    FAQItem(
      question: 'Is my data secure?',
      answer: 'Yes, we use industry-standard encryption to protect your data. You can learn more about our privacy practices in our Privacy Policy.',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(isTablet),
              Expanded(
                child: _buildContent(isTablet),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(isTablet ? 24 : 16),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: Icon(
                Icons.arrow_back_rounded,
                color: Colors.white,
                size: isTablet ? 26 : 24,
              ),
              padding: EdgeInsets.all(isTablet ? 10 : 8),
            ),
          ),
          SizedBox(width: isTablet ? 20 : 16),
          Expanded(
            child: Text(
              'Help & Support',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: isTablet ? 24 : 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(bool isTablet) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(isTablet ? 24 : 16),
      child: Column(
        children: [
          _buildQuickActions(isTablet),
          SizedBox(height: isTablet ? 24 : 16),
          _buildFAQ(isTablet),
          SizedBox(height: isTablet ? 24 : 16),
          _buildContactSupport(isTablet),
          SizedBox(height: isTablet ? 24 : 16),
          _buildAppInfo(isTablet),
        ],
      ),
    );
  }

  Widget _buildQuickActions(bool isTablet) {
    return GlassmorphicContainer(
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 28 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: isTablet ? 20 : 18,
              ),
            ),
            SizedBox(height: isTablet ? 24 : 16),
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionCard(
                    title: 'Restart Tutorial',
                    subtitle: 'Learn the basics again',
                    icon: Icons.school_rounded,
                    onTap: _restartTutorial,
                    isTablet: isTablet,
                  ),
                ),
                SizedBox(width: isTablet ? 16 : 12),
                Expanded(
                  child: _buildQuickActionCard(
                    title: 'Report Bug',
                    subtitle: 'Found an issue?',
                    icon: Icons.bug_report_rounded,
                    onTap: _reportBug,
                    isTablet: isTablet,
                  ),
                ),
              ],
            ),
            SizedBox(height: isTablet ? 16 : 12),
            Row(
              children: [
                Expanded(
                  child: _buildQuickActionCard(
                    title: 'Feature Request',
                    subtitle: 'Suggest improvements',
                    icon: Icons.lightbulb_outline_rounded,
                    onTap: _featureRequest,
                    isTablet: isTablet,
                  ),
                ),
                SizedBox(width: isTablet ? 16 : 12),
                Expanded(
                  child: _buildQuickActionCard(
                    title: 'Rate App',
                    subtitle: 'Share your feedback',
                    icon: Icons.star_outline_rounded,
                    onTap: _rateApp,
                    isTablet: isTablet,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    required bool isTablet,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: EdgeInsets.all(isTablet ? 16 : 12),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(isTablet ? 12 : 10),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  icon,
                  color: AppTheme.primaryColor,
                  size: isTablet ? 28 : 24,
                ),
              ),
              SizedBox(height: isTablet ? 12 : 8),
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                  fontSize: isTablet ? 14 : 12,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: isTablet ? 4 : 2),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.textSecondaryColor,
                  fontSize: isTablet ? 12 : 10,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFAQ(bool isTablet) {
    return GlassmorphicContainer(
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 28 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Frequently Asked Questions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: isTablet ? 20 : 18,
              ),
            ),
            SizedBox(height: isTablet ? 24 : 16),
            ..._faqItems.map((item) => _buildFAQItem(item, isTablet)),
          ],
        ),
      ),
    );
  }

  Widget _buildFAQItem(FAQItem item, bool isTablet) {
    return Container(
      margin: EdgeInsets.only(bottom: isTablet ? 16 : 12),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
        ),
        child: ExpansionTile(
          title: Text(
            item.question,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.white,
              fontSize: isTablet ? 16 : 14,
            ),
          ),
          iconColor: AppTheme.primaryColor,
          collapsedIconColor: AppTheme.textSecondaryColor,
          children: [
            Padding(
              padding: EdgeInsets.fromLTRB(
                isTablet ? 16 : 12,
                0,
                isTablet ? 16 : 12,
                isTablet ? 16 : 12,
              ),
              child: Text(
                item.answer,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.textSecondaryColor,
                  fontSize: isTablet ? 14 : 12,
                  height: 1.5,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactSupport(bool isTablet) {
    return GlassmorphicContainer(
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 28 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Contact Support',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: isTablet ? 20 : 18,
              ),
            ),
            SizedBox(height: isTablet ? 24 : 16),
            _buildContactOption(
              title: 'Email Support',
              subtitle: 'Get help via email',
              icon: Icons.email_rounded,
              onTap: _emailSupport,
              isTablet: isTablet,
            ),
            SizedBox(height: isTablet ? 16 : 12),
            _buildContactOption(
              title: 'Live Chat',
              subtitle: 'Chat with our support team',
              icon: Icons.chat_rounded,
              onTap: _liveChat,
              isTablet: isTablet,
            ),
            SizedBox(height: isTablet ? 16 : 12),
            _buildContactOption(
              title: 'Community Forum',
              subtitle: 'Connect with other users',
              icon: Icons.forum_rounded,
              onTap: _communityForum,
              isTablet: isTablet,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactOption({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    required bool isTablet,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: EdgeInsets.all(isTablet ? 16 : 12),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.1),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(isTablet ? 12 : 10),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  icon,
                  color: AppTheme.primaryColor,
                  size: isTablet ? 24 : 20,
                ),
              ),
              SizedBox(width: isTablet ? 16 : 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                        fontSize: isTablet ? 16 : 14,
                      ),
                    ),
                    SizedBox(height: isTablet ? 4 : 2),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondaryColor,
                        fontSize: isTablet ? 14 : 12,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.chevron_right_rounded,
                color: AppTheme.textSecondaryColor,
                size: isTablet ? 24 : 20,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppInfo(bool isTablet) {
    return GlassmorphicContainer(
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 28 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'App Information',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: isTablet ? 20 : 18,
              ),
            ),
            SizedBox(height: isTablet ? 24 : 16),
            _buildInfoRow('Version', AppConstants.appVersion, isTablet),
            SizedBox(height: isTablet ? 12 : 8),
            _buildInfoRow('Build', '1.0.0+1', isTablet),
            SizedBox(height: isTablet ? 12 : 8),
            _buildInfoRow('Platform', 'Flutter', isTablet),
            SizedBox(height: isTablet ? 20 : 16),
            Row(
              children: [
                Expanded(
                  child: _buildLinkButton(
                    title: 'Terms of Service',
                    onTap: _viewTerms,
                    isTablet: isTablet,
                  ),
                ),
                SizedBox(width: isTablet ? 12 : 8),
                Expanded(
                  child: _buildLinkButton(
                    title: 'Privacy Policy',
                    onTap: _viewPrivacy,
                    isTablet: isTablet,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, bool isTablet) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppTheme.textSecondaryColor,
            fontSize: isTablet ? 16 : 14,
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Colors.white,
            fontSize: isTablet ? 16 : 14,
          ),
        ),
      ],
    );
  }

  Widget _buildLinkButton({
    required String title,
    required VoidCallback onTap,
    required bool isTablet,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: isTablet ? 16 : 12,
            vertical: isTablet ? 12 : 8,
          ),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppTheme.primaryColor.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Text(
            title,
            style: TextStyle(
              color: AppTheme.primaryColor,
              fontWeight: FontWeight.w600,
              fontSize: isTablet ? 14 : 12,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }

  // Action methods
  void _restartTutorial() {
    // TODO: Implement tutorial restart
    _showSuccessSnackBar('Tutorial will restart on next app launch');
  }

  void _reportBug() {
    _launchEmail(
      subject: 'Bug Report - ${AppConstants.appName}',
      body: 'Please describe the bug you encountered:\n\n',
    );
  }

  void _featureRequest() {
    _launchEmail(
      subject: 'Feature Request - ${AppConstants.appName}',
      body: 'Please describe the feature you would like to see:\n\n',
    );
  }

  void _rateApp() {
    // TODO: Implement app store rating
    _showSuccessSnackBar('Opening app store...');
  }

  void _emailSupport() {
    _launchEmail(
      subject: 'Support Request - ${AppConstants.appName}',
      body: 'Please describe your issue:\n\n',
    );
  }

  void _liveChat() {
    // TODO: Implement live chat
    _showSuccessSnackBar('Live chat coming soon!');
  }

  void _communityForum() {
    // TODO: Implement community forum link
    _showSuccessSnackBar('Opening community forum...');
  }

  void _viewTerms() {
    // TODO: Implement terms of service view
    _showSuccessSnackBar('Opening terms of service...');
  }

  void _viewPrivacy() {
    // TODO: Implement privacy policy view
    _showSuccessSnackBar('Opening privacy policy...');
  }

  void _launchEmail({required String subject, required String body}) async {
    // TODO: Implement email launcher with url_launcher package
    _showSuccessSnackBar('Email support: <EMAIL>');
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

class FAQItem {
  final String question;
  final String answer;

  const FAQItem({
    required this.question,
    required this.answer,
  });
}
