import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:ui';

import '../../core/theme/app_theme.dart';
import '../../providers/auth_provider.dart';
import '../../models/user/user_model.dart';
import '../../widgets/common/glassmorphic_container.dart';
import '../../widgets/debug/audio_debug_widget.dart';

class VoiceSettingsPage extends ConsumerStatefulWidget {
  const VoiceSettingsPage({super.key});

  @override
  ConsumerState<VoiceSettingsPage> createState() => _VoiceSettingsPageState();
}

class _VoiceSettingsPageState extends ConsumerState<VoiceSettingsPage> {
  bool _voiceEnabled = true;
  bool _autoPlayVoice = true;
  double _speechRate = 1.0;
  double _pitch = 1.0;
  String _selectedVoice = 'en-US-Neural2-F';
  bool _hasChanges = false;

  final List<VoiceOption> _availableVoices = [
    VoiceOption('en-US-Neural2-F', '<PERSON> (Female)', 'Warm and friendly'),
    VoiceOption('en-US-Neural2-C', '<PERSON> (Female)', 'Professional and clear'),
    VoiceOption('en-US-Neural2-A', 'Aria (Female)', 'Youthful and energetic'),
    VoiceOption('en-US-Neural2-D', 'James (Male)', 'Deep and confident'),
    VoiceOption('en-US-Neural2-J', 'Alex (Male)', 'Casual and approachable'),
  ];

  @override
  void initState() {
    super.initState();
    _loadVoiceSettings();
  }

  void _loadVoiceSettings() {
    final user = ref.read(currentUserProvider);
    if (user != null) {
      setState(() {
        _voiceEnabled = user.preferences['voice_enabled'] as bool? ?? true;
        _autoPlayVoice = user.preferences['auto_play_voice'] as bool? ?? true;
        _speechRate = (user.preferences['speech_rate'] as double?) ?? 1.0;
        _pitch = (user.preferences['pitch'] as double?) ?? 1.0;
        _selectedVoice = user.preferences['selected_voice'] as String? ?? 'en-US-Neural2-F';
      });
    }
  }

  void _onSettingChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  Future<void> _saveSettings() async {
    try {
      final user = ref.read(currentUserProvider);
      if (user == null) return;

      final updatedUser = user.copyWith(
        preferences: {
          ...user.preferences,
          'voice_enabled': _voiceEnabled,
          'auto_play_voice': _autoPlayVoice,
          'speech_rate': _speechRate,
          'pitch': _pitch,
          'selected_voice': _selectedVoice,
        },
      );

      await ref.read(authProvider.notifier).updateUser(updatedUser);
      
      setState(() {
        _hasChanges = false;
      });

      _showSuccessSnackBar('Voice settings saved successfully');
    } catch (e) {
      _showErrorSnackBar('Failed to save settings: $e');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(isTablet),
              Expanded(
                child: _buildContent(isTablet),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(isTablet ? 24 : 16),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: Icon(
                Icons.arrow_back_rounded,
                color: Colors.white,
                size: isTablet ? 26 : 24,
              ),
              padding: EdgeInsets.all(isTablet ? 10 : 8),
            ),
          ),
          SizedBox(width: isTablet ? 20 : 16),
          Expanded(
            child: Text(
              'Voice Settings',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: isTablet ? 24 : 20,
              ),
            ),
          ),
          if (_hasChanges)
            Container(
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(12),
              ),
              child: TextButton(
                onPressed: _saveSettings,
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: isTablet ? 20 : 16,
                    vertical: isTablet ? 12 : 8,
                  ),
                ),
                child: Text(
                  'Save',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: isTablet ? 16 : 14,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildContent(bool isTablet) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(isTablet ? 24 : 16),
      child: Column(
        children: [
          _buildGeneralSettings(isTablet),
          SizedBox(height: isTablet ? 24 : 16),
          _buildVoiceSelection(isTablet),
          SizedBox(height: isTablet ? 24 : 16),
          _buildVoiceParameters(isTablet),
          SizedBox(height: isTablet ? 24 : 16),
          _buildDebugSection(isTablet),
        ],
      ),
    );
  }

  Widget _buildGeneralSettings(bool isTablet) {
    return GlassmorphicContainer(
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 28 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'General Settings',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: isTablet ? 20 : 18,
              ),
            ),
            SizedBox(height: isTablet ? 24 : 16),
            _buildToggleTile(
              title: 'Enable Voice Responses',
              subtitle: 'Allow Ella to respond with voice messages',
              value: _voiceEnabled,
              onChanged: (value) {
                setState(() {
                  _voiceEnabled = value;
                });
                _onSettingChanged();
              },
              icon: Icons.record_voice_over_rounded,
              isTablet: isTablet,
            ),
            SizedBox(height: isTablet ? 16 : 12),
            _buildToggleTile(
              title: 'Auto-play Voice Messages',
              subtitle: 'Automatically play voice responses',
              value: _autoPlayVoice,
              onChanged: _voiceEnabled ? (value) {
                setState(() {
                  _autoPlayVoice = value;
                });
                _onSettingChanged();
              } : null,
              icon: Icons.play_circle_outline_rounded,
              isTablet: isTablet,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToggleTile({
    required String title,
    required String subtitle,
    required bool value,
    required void Function(bool)? onChanged,
    required IconData icon,
    required bool isTablet,
  }) {
    final isEnabled = onChanged != null;
    
    return Container(
      padding: EdgeInsets.all(isTablet ? 16 : 12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: isEnabled ? 0.05 : 0.02),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: isEnabled ? 0.1 : 0.05),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(isTablet ? 12 : 10),
            decoration: BoxDecoration(
              color: (isEnabled ? AppTheme.primaryColor : AppTheme.textSecondaryColor)
                  .withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: isEnabled ? AppTheme.primaryColor : AppTheme.textSecondaryColor,
              size: isTablet ? 24 : 20,
            ),
          ),
          SizedBox(width: isTablet ? 16 : 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isEnabled ? Colors.white : AppTheme.textSecondaryColor,
                    fontSize: isTablet ? 16 : 14,
                  ),
                ),
                SizedBox(height: isTablet ? 4 : 2),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textSecondaryColor,
                    fontSize: isTablet ? 14 : 12,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.primaryColor,
            inactiveThumbColor: AppTheme.textSecondaryColor,
            inactiveTrackColor: Colors.white.withValues(alpha: 0.1),
          ),
        ],
      ),
    );
  }

  Widget _buildVoiceSelection(bool isTablet) {
    return GlassmorphicContainer(
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 28 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Voice Selection',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: isTablet ? 20 : 18,
              ),
            ),
            SizedBox(height: isTablet ? 24 : 16),
            ..._availableVoices.map((voice) => _buildVoiceOption(voice, isTablet)),
          ],
        ),
      ),
    );
  }

  Widget _buildVoiceOption(VoiceOption voice, bool isTablet) {
    final isSelected = _selectedVoice == voice.id;

    return Container(
      margin: EdgeInsets.only(bottom: isTablet ? 12 : 8),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _voiceEnabled ? () {
            setState(() {
              _selectedVoice = voice.id;
            });
            _onSettingChanged();
          } : null,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: EdgeInsets.all(isTablet ? 16 : 12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(
                alpha: isSelected ? 0.1 : (_voiceEnabled ? 0.05 : 0.02),
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isSelected
                    ? AppTheme.primaryColor
                    : Colors.white.withValues(alpha: _voiceEnabled ? 0.1 : 0.05),
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isTablet ? 12 : 10),
                  decoration: BoxDecoration(
                    color: (isSelected ? AppTheme.primaryColor : AppTheme.textSecondaryColor)
                        .withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    voice.name.contains('Female') ? Icons.face_3 : Icons.face,
                    color: isSelected ? AppTheme.primaryColor : AppTheme.textSecondaryColor,
                    size: isTablet ? 24 : 20,
                  ),
                ),
                SizedBox(width: isTablet ? 16 : 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        voice.name,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: _voiceEnabled ? Colors.white : AppTheme.textSecondaryColor,
                          fontSize: isTablet ? 16 : 14,
                        ),
                      ),
                      SizedBox(height: isTablet ? 4 : 2),
                      Text(
                        voice.description,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.textSecondaryColor,
                          fontSize: isTablet ? 14 : 12,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isSelected)
                  Container(
                    padding: EdgeInsets.all(isTablet ? 8 : 6),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.check_rounded,
                      color: Colors.white,
                      size: isTablet ? 16 : 14,
                    ),
                  ),
                if (_voiceEnabled)
                  IconButton(
                    onPressed: () => _playVoicePreview(voice),
                    icon: Icon(
                      Icons.play_circle_outline_rounded,
                      color: AppTheme.primaryColor,
                      size: isTablet ? 28 : 24,
                    ),
                    tooltip: 'Preview voice',
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildVoiceParameters(bool isTablet) {
    return GlassmorphicContainer(
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 28 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Voice Parameters',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: isTablet ? 20 : 18,
              ),
            ),
            SizedBox(height: isTablet ? 24 : 16),
            _buildSliderSetting(
              title: 'Speech Rate',
              subtitle: 'How fast Ella speaks',
              value: _speechRate,
              min: 0.5,
              max: 2.0,
              divisions: 15,
              onChanged: _voiceEnabled ? (value) {
                setState(() {
                  _speechRate = value;
                });
                _onSettingChanged();
              } : null,
              icon: Icons.speed_rounded,
              isTablet: isTablet,
            ),
            SizedBox(height: isTablet ? 24 : 16),
            _buildSliderSetting(
              title: 'Pitch',
              subtitle: 'How high or low Ella\'s voice sounds',
              value: _pitch,
              min: 0.5,
              max: 2.0,
              divisions: 15,
              onChanged: _voiceEnabled ? (value) {
                setState(() {
                  _pitch = value;
                });
                _onSettingChanged();
              } : null,
              icon: Icons.tune_rounded,
              isTablet: isTablet,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSliderSetting({
    required String title,
    required String subtitle,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required void Function(double)? onChanged,
    required IconData icon,
    required bool isTablet,
  }) {
    final isEnabled = onChanged != null;

    return Container(
      padding: EdgeInsets.all(isTablet ? 16 : 12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: isEnabled ? 0.05 : 0.02),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: isEnabled ? 0.1 : 0.05),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(isTablet ? 12 : 10),
                decoration: BoxDecoration(
                  color: (isEnabled ? AppTheme.primaryColor : AppTheme.textSecondaryColor)
                      .withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  icon,
                  color: isEnabled ? AppTheme.primaryColor : AppTheme.textSecondaryColor,
                  size: isTablet ? 24 : 20,
                ),
              ),
              SizedBox(width: isTablet ? 16 : 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isEnabled ? Colors.white : AppTheme.textSecondaryColor,
                        fontSize: isTablet ? 16 : 14,
                      ),
                    ),
                    SizedBox(height: isTablet ? 4 : 2),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondaryColor,
                        fontSize: isTablet ? 14 : 12,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: isTablet ? 12 : 8,
                  vertical: isTablet ? 6 : 4,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${value.toStringAsFixed(1)}x',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                    fontSize: isTablet ? 14 : 12,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: isTablet ? 16 : 12),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: AppTheme.primaryColor,
              inactiveTrackColor: Colors.white.withValues(alpha: 0.1),
              thumbColor: AppTheme.primaryColor,
              overlayColor: AppTheme.primaryColor.withValues(alpha: 0.2),
              trackHeight: isTablet ? 6 : 4,
              thumbShape: RoundSliderThumbShape(
                enabledThumbRadius: isTablet ? 12 : 10,
              ),
            ),
            child: Slider(
              value: value,
              min: min,
              max: max,
              divisions: divisions,
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  void _playVoicePreview(VoiceOption voice) {
    // TODO: Implement voice preview functionality
    _showSuccessSnackBar('Playing preview for ${voice.name}');
  }

  Widget _buildDebugSection(bool isTablet) {
    return GlassmorphicContainer(
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 28 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Debug & Testing',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: isTablet ? 20 : 18,
              ),
            ),
            SizedBox(height: isTablet ? 16 : 12),
            Text(
              'Audio recording and transcription debugging tools',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white.withValues(alpha: 0.7),
                fontSize: isTablet ? 16 : 14,
              ),
            ),
            SizedBox(height: isTablet ? 20 : 16),
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(12),
              ),
              child: TextButton.icon(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const AudioDebugWidget(),
                    ),
                  );
                },
                icon: Icon(
                  Icons.bug_report_rounded,
                  color: Colors.white,
                  size: isTablet ? 20 : 18,
                ),
                label: Text(
                  'Open Audio Debug Tool',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: isTablet ? 16 : 14,
                  ),
                ),
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: isTablet ? 20 : 16,
                    vertical: isTablet ? 16 : 12,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class VoiceOption {
  final String id;
  final String name;
  final String description;

  const VoiceOption(this.id, this.name, this.description);
}
