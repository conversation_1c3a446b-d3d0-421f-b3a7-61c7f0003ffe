import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:ui';

import '../../core/theme/app_theme.dart';
import '../../providers/auth_provider.dart';
import '../../models/user/user_model.dart';
import '../../widgets/common/glassmorphic_container.dart';

class NotificationsSettingsPage extends ConsumerStatefulWidget {
  const NotificationsSettingsPage({super.key});

  @override
  ConsumerState<NotificationsSettingsPage> createState() => _NotificationsSettingsPageState();
}

class _NotificationsSettingsPageState extends ConsumerState<NotificationsSettingsPage> {
  bool _notificationsEnabled = true;
  bool _pushNotifications = true;
  bool _emailNotifications = true;
  bool _messageNotifications = true;
  bool _reminderNotifications = true;
  bool _achievementNotifications = true;
  bool _dailyBonusNotifications = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;
  TimeOfDay _quietHoursStart = const TimeOfDay(hour: 22, minute: 0);
  TimeOfDay _quietHoursEnd = const TimeOfDay(hour: 8, minute: 0);
  bool _quietHoursEnabled = false;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _loadNotificationSettings();
  }

  void _loadNotificationSettings() {
    final user = ref.read(currentUserProvider);
    if (user != null) {
      setState(() {
        _notificationsEnabled = user.preferences['notifications_enabled'] as bool? ?? true;
        _pushNotifications = user.preferences['push_notifications'] as bool? ?? true;
        _emailNotifications = user.preferences['email_notifications'] as bool? ?? true;
        _messageNotifications = user.preferences['message_notifications'] as bool? ?? true;
        _reminderNotifications = user.preferences['reminder_notifications'] as bool? ?? true;
        _achievementNotifications = user.preferences['achievement_notifications'] as bool? ?? true;
        _dailyBonusNotifications = user.preferences['daily_bonus_notifications'] as bool? ?? true;
        _soundEnabled = user.preferences['notification_sound'] as bool? ?? true;
        _vibrationEnabled = user.preferences['notification_vibration'] as bool? ?? true;
        _quietHoursEnabled = user.preferences['quiet_hours_enabled'] as bool? ?? false;
        
        // Load quiet hours
        final quietStart = user.preferences['quiet_hours_start'] as String?;
        final quietEnd = user.preferences['quiet_hours_end'] as String?;
        if (quietStart != null) {
          final parts = quietStart.split(':');
          _quietHoursStart = TimeOfDay(hour: int.parse(parts[0]), minute: int.parse(parts[1]));
        }
        if (quietEnd != null) {
          final parts = quietEnd.split(':');
          _quietHoursEnd = TimeOfDay(hour: int.parse(parts[0]), minute: int.parse(parts[1]));
        }
      });
    }
  }

  void _onSettingChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  Future<void> _saveSettings() async {
    try {
      final user = ref.read(currentUserProvider);
      if (user == null) return;

      final updatedUser = user.copyWith(
        preferences: {
          ...user.preferences,
          'notifications_enabled': _notificationsEnabled,
          'push_notifications': _pushNotifications,
          'email_notifications': _emailNotifications,
          'message_notifications': _messageNotifications,
          'reminder_notifications': _reminderNotifications,
          'achievement_notifications': _achievementNotifications,
          'daily_bonus_notifications': _dailyBonusNotifications,
          'notification_sound': _soundEnabled,
          'notification_vibration': _vibrationEnabled,
          'quiet_hours_enabled': _quietHoursEnabled,
          'quiet_hours_start': '${_quietHoursStart.hour}:${_quietHoursStart.minute}',
          'quiet_hours_end': '${_quietHoursEnd.hour}:${_quietHoursEnd.minute}',
        },
      );

      await ref.read(authProvider.notifier).updateUser(updatedUser);
      
      setState(() {
        _hasChanges = false;
      });

      _showSuccessSnackBar('Notification settings saved successfully');
    } catch (e) {
      _showErrorSnackBar('Failed to save settings: $e');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;

    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: SafeArea(
          child: Column(
            children: [
              _buildHeader(isTablet),
              Expanded(
                child: _buildContent(isTablet),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(bool isTablet) {
    return Container(
      padding: EdgeInsets.all(isTablet ? 24 : 16),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: Icon(
                Icons.arrow_back_rounded,
                color: Colors.white,
                size: isTablet ? 26 : 24,
              ),
              padding: EdgeInsets.all(isTablet ? 10 : 8),
            ),
          ),
          SizedBox(width: isTablet ? 20 : 16),
          Expanded(
            child: Text(
              'Notifications',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: isTablet ? 24 : 20,
              ),
            ),
          ),
          if (_hasChanges)
            Container(
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(12),
              ),
              child: TextButton(
                onPressed: _saveSettings,
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: isTablet ? 20 : 16,
                    vertical: isTablet ? 12 : 8,
                  ),
                ),
                child: Text(
                  'Save',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: isTablet ? 16 : 14,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildContent(bool isTablet) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(isTablet ? 24 : 16),
      child: Column(
        children: [
          _buildGeneralSettings(isTablet),
          SizedBox(height: isTablet ? 24 : 16),
          _buildNotificationTypes(isTablet),
          SizedBox(height: isTablet ? 24 : 16),
          _buildNotificationBehavior(isTablet),
          SizedBox(height: isTablet ? 24 : 16),
          _buildQuietHours(isTablet),
        ],
      ),
    );
  }

  Widget _buildGeneralSettings(bool isTablet) {
    return GlassmorphicContainer(
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 28 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'General Settings',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: isTablet ? 20 : 18,
              ),
            ),
            SizedBox(height: isTablet ? 24 : 16),
            _buildToggleTile(
              title: 'Enable Notifications',
              subtitle: 'Allow the app to send notifications',
              value: _notificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _notificationsEnabled = value;
                });
                _onSettingChanged();
              },
              icon: Icons.notifications_rounded,
              isTablet: isTablet,
            ),
            SizedBox(height: isTablet ? 16 : 12),
            _buildToggleTile(
              title: 'Push Notifications',
              subtitle: 'Receive notifications on your device',
              value: _pushNotifications,
              onChanged: _notificationsEnabled ? (value) {
                setState(() {
                  _pushNotifications = value;
                });
                _onSettingChanged();
              } : null,
              icon: Icons.phone_android_rounded,
              isTablet: isTablet,
            ),
            SizedBox(height: isTablet ? 16 : 12),
            _buildToggleTile(
              title: 'Email Notifications',
              subtitle: 'Receive notifications via email',
              value: _emailNotifications,
              onChanged: _notificationsEnabled ? (value) {
                setState(() {
                  _emailNotifications = value;
                });
                _onSettingChanged();
              } : null,
              icon: Icons.email_rounded,
              isTablet: isTablet,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildToggleTile({
    required String title,
    required String subtitle,
    required bool value,
    required void Function(bool)? onChanged,
    required IconData icon,
    required bool isTablet,
  }) {
    final isEnabled = onChanged != null;
    
    return Container(
      padding: EdgeInsets.all(isTablet ? 16 : 12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: isEnabled ? 0.05 : 0.02),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: isEnabled ? 0.1 : 0.05),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(isTablet ? 12 : 10),
            decoration: BoxDecoration(
              color: (isEnabled ? AppTheme.primaryColor : AppTheme.textSecondaryColor)
                  .withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: isEnabled ? AppTheme.primaryColor : AppTheme.textSecondaryColor,
              size: isTablet ? 24 : 20,
            ),
          ),
          SizedBox(width: isTablet ? 16 : 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isEnabled ? Colors.white : AppTheme.textSecondaryColor,
                    fontSize: isTablet ? 16 : 14,
                  ),
                ),
                SizedBox(height: isTablet ? 4 : 2),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textSecondaryColor,
                    fontSize: isTablet ? 14 : 12,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.primaryColor,
            inactiveThumbColor: AppTheme.textSecondaryColor,
            inactiveTrackColor: Colors.white.withValues(alpha: 0.1),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationTypes(bool isTablet) {
    return GlassmorphicContainer(
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 28 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notification Types',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: isTablet ? 20 : 18,
              ),
            ),
            SizedBox(height: isTablet ? 24 : 16),
            _buildToggleTile(
              title: 'Message Notifications',
              subtitle: 'When Ella sends you a message',
              value: _messageNotifications,
              onChanged: _notificationsEnabled ? (value) {
                setState(() {
                  _messageNotifications = value;
                });
                _onSettingChanged();
              } : null,
              icon: Icons.message_rounded,
              isTablet: isTablet,
            ),
            SizedBox(height: isTablet ? 16 : 12),
            _buildToggleTile(
              title: 'Reminder Notifications',
              subtitle: 'Daily check-in reminders',
              value: _reminderNotifications,
              onChanged: _notificationsEnabled ? (value) {
                setState(() {
                  _reminderNotifications = value;
                });
                _onSettingChanged();
              } : null,
              icon: Icons.schedule_rounded,
              isTablet: isTablet,
            ),
            SizedBox(height: isTablet ? 16 : 12),
            _buildToggleTile(
              title: 'Achievement Notifications',
              subtitle: 'When you unlock new achievements',
              value: _achievementNotifications,
              onChanged: _notificationsEnabled ? (value) {
                setState(() {
                  _achievementNotifications = value;
                });
                _onSettingChanged();
              } : null,
              icon: Icons.emoji_events_rounded,
              isTablet: isTablet,
            ),
            SizedBox(height: isTablet ? 16 : 12),
            _buildToggleTile(
              title: 'Daily Bonus Notifications',
              subtitle: 'Reminders to claim daily rewards',
              value: _dailyBonusNotifications,
              onChanged: _notificationsEnabled ? (value) {
                setState(() {
                  _dailyBonusNotifications = value;
                });
                _onSettingChanged();
              } : null,
              icon: Icons.card_giftcard_rounded,
              isTablet: isTablet,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationBehavior(bool isTablet) {
    return GlassmorphicContainer(
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 28 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notification Behavior',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: isTablet ? 20 : 18,
              ),
            ),
            SizedBox(height: isTablet ? 24 : 16),
            _buildToggleTile(
              title: 'Sound',
              subtitle: 'Play sound for notifications',
              value: _soundEnabled,
              onChanged: _notificationsEnabled ? (value) {
                setState(() {
                  _soundEnabled = value;
                });
                _onSettingChanged();
              } : null,
              icon: Icons.volume_up_rounded,
              isTablet: isTablet,
            ),
            SizedBox(height: isTablet ? 16 : 12),
            _buildToggleTile(
              title: 'Vibration',
              subtitle: 'Vibrate for notifications',
              value: _vibrationEnabled,
              onChanged: _notificationsEnabled ? (value) {
                setState(() {
                  _vibrationEnabled = value;
                });
                _onSettingChanged();
              } : null,
              icon: Icons.vibration_rounded,
              isTablet: isTablet,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuietHours(bool isTablet) {
    return GlassmorphicContainer(
      child: Padding(
        padding: EdgeInsets.all(isTablet ? 28 : 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quiet Hours',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: isTablet ? 20 : 18,
              ),
            ),
            SizedBox(height: isTablet ? 24 : 16),
            _buildToggleTile(
              title: 'Enable Quiet Hours',
              subtitle: 'Silence notifications during specified hours',
              value: _quietHoursEnabled,
              onChanged: _notificationsEnabled ? (value) {
                setState(() {
                  _quietHoursEnabled = value;
                });
                _onSettingChanged();
              } : null,
              icon: Icons.bedtime_rounded,
              isTablet: isTablet,
            ),
            if (_quietHoursEnabled && _notificationsEnabled) ...[
              SizedBox(height: isTablet ? 20 : 16),
              Row(
                children: [
                  Expanded(
                    child: _buildTimePicker(
                      title: 'Start Time',
                      time: _quietHoursStart,
                      onChanged: (time) {
                        setState(() {
                          _quietHoursStart = time;
                        });
                        _onSettingChanged();
                      },
                      isTablet: isTablet,
                    ),
                  ),
                  SizedBox(width: isTablet ? 16 : 12),
                  Expanded(
                    child: _buildTimePicker(
                      title: 'End Time',
                      time: _quietHoursEnd,
                      onChanged: (time) {
                        setState(() {
                          _quietHoursEnd = time;
                        });
                        _onSettingChanged();
                      },
                      isTablet: isTablet,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTimePicker({
    required String title,
    required TimeOfDay time,
    required void Function(TimeOfDay) onChanged,
    required bool isTablet,
  }) {
    return Container(
      padding: EdgeInsets.all(isTablet ? 16 : 12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Colors.white,
              fontSize: isTablet ? 14 : 12,
            ),
          ),
          SizedBox(height: isTablet ? 12 : 8),
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => _selectTime(time, onChanged),
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: isTablet ? 16 : 12,
                  vertical: isTablet ? 12 : 8,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: AppTheme.primaryColor.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.access_time_rounded,
                      color: AppTheme.primaryColor,
                      size: isTablet ? 20 : 18,
                    ),
                    SizedBox(width: isTablet ? 8 : 6),
                    Text(
                      time.format(context),
                      style: TextStyle(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.w600,
                        fontSize: isTablet ? 16 : 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectTime(TimeOfDay currentTime, void Function(TimeOfDay) onChanged) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: currentTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: AppTheme.primaryColor,
              onSurface: Colors.white,
            ),
            timePickerTheme: TimePickerThemeData(
              backgroundColor: AppTheme.surfaceColor,
              hourMinuteTextColor: Colors.white,
              dayPeriodTextColor: Colors.white,
              dialHandColor: AppTheme.primaryColor,
              dialTextColor: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != currentTime) {
      onChanged(picked);
    }
  }
}
