import 'package:ellahai/models/shop/shop_preview_models.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import '../../providers/shop_provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/shop_preview_provider.dart';
import '../../services/relationship/relationship_gate_controller.dart';
import '../../widgets/common/glassmorphic_container.dart';
import '../../widgets/shop/preview_mode_overlay.dart';
import '../../models/shop/shop_item_model.dart';

class ShopScreen extends ConsumerStatefulWidget {
  const ShopScreen({super.key});

  @override
  ConsumerState<ShopScreen> createState() => _ShopScreenState();
}

class _ShopScreenState extends ConsumerState<ShopScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(currentUserProvider);
    final shopState = ref.watch(shopProvider);
    final previewState = ref.watch(shopPreviewProvider);
    
    print('Shop: ${shopState.environments.length + shopState.outfits.length + shopState.accessories.length} items, Preview: ${previewState.isInPreviewMode}');

    return Scaffold(
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: SafeArea(
          child: Stack(
            children: [
              // Main shop content - fade out when preview mode is active
              AnimatedOpacity(
                opacity: previewState.isInPreviewMode ? 0.0 : 1.0,
                duration: const Duration(milliseconds: 300),
                child: IgnorePointer(
                  ignoring: previewState.isInPreviewMode,
                  child: Column(
                    children: [
                      _buildHeader(user?.progress.hearts ?? 0),
                      _buildTabBar(),
                      Expanded(
                        child: TabBarView(
                          controller: _tabController,
                          children: [
                            _buildEnvironmentsTab(shopState.environments),
                            _buildOutfitsTab(shopState.outfits),
                            _buildAccessoriesTab(shopState.accessories),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Preview mode overlay - fade in when active
              if (previewState.isInPreviewMode) 
                AnimatedOpacity(
                  opacity: 1.0,
                  duration: const Duration(milliseconds: 300),
                  child: const PreviewModeOverlay(),
                ),

            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(int hearts) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.arrow_back_rounded),
          ),
          Expanded(
            child: Text(
              'Shop',
              style: Theme.of(
                context,
              ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),

          GlassmorphicContainer(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  AppConstants.currencySymbol,
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(width: 4),
                Text(
                  hearts.toString(),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(12),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.textSecondaryColor,
        tabs: const [
          Tab(text: 'Environments'),
          Tab(text: 'Outfits'),
          Tab(text: 'Accessories'),
        ],
      ),
    );
  }

  Widget _buildEnvironmentsTab(List<ShopItemModel> environments) {
    if (environments.isEmpty) {
      return const Center(
        child: Text('No environments available', style: TextStyle(color: Colors.white)),
      );
    }
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: environments.length,
      itemBuilder: (context, index) {
        final item = environments[index];
        return _buildShopItem(item);
      },
    );
  }

  Widget _buildOutfitsTab(List<ShopItemModel> outfits) {
    if (outfits.isEmpty) {
      return const Center(
        child: Text('No outfits available', style: TextStyle(color: Colors.white)),
      );
    }
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.7,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: outfits.length,
      itemBuilder: (context, index) {
        final item = outfits[index];
        return _buildShopItem(item);
      },
    );
  }

  Widget _buildAccessoriesTab(List<ShopItemModel> accessories) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 0.8,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: accessories.length,
      itemBuilder: (context, index) {
        final item = accessories[index];
        return _buildShopItem(item);
      },
    );
  }

  Widget _buildShopItem(ShopItemModel item) {
    return _ShopItemWidget(
      item: item,
      onTap: (ShopItemModel tappedItem) {
        print('Opening preview for: ${tappedItem.name}');
        ref.read(shopPreviewProvider.notifier).enterPreviewMode(tappedItem, tappedItem.type);
      },
    );
  }
}

class _ShopItemWidget extends ConsumerStatefulWidget {
  final ShopItemModel item;
  final Function(ShopItemModel) onTap;

  const _ShopItemWidget({
    required this.item,
    required this.onTap,
  });

  @override
  ConsumerState<_ShopItemWidget> createState() => _ShopItemWidgetState();
}

class _ShopItemWidgetState extends ConsumerState<_ShopItemWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(currentUserProvider);
    final relationshipGate = ref.read(relationshipGateControllerProvider);
    final currentRelationshipLevel =
        relationshipGate.getCurrentRelationshipLevel();

    final isOwned = _isItemOwned(widget.item, user);
    final canAfford = (user?.progress.hearts ?? 0) >= widget.item.price;
    final isLocked =
        !relationshipGate.isItemUnlocked(widget.item, currentRelationshipLevel);

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTapDown: (_) => _scaleController.forward(),
      onTapUp: (_) => _scaleController.reverse(),
      onTapCancel: () => _scaleController.reverse(),
      onTap: () => widget.onTap(widget.item),
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: GlassmorphicContainer(
                child: Stack(
                  children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Item Image
                      Expanded(
                        flex: 3,
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(12),
                            ),
                            image: widget.item.imageUrl != null
                                ? DecorationImage(
                                    image: NetworkImage(widget.item.imageUrl!),
                                    fit: BoxFit.cover,
                                  )
                                : null,
                            gradient: widget.item.imageUrl == null
                                ? _getGradientForType(widget.item.type)
                                : null,
                          ),
                          child: Stack(
                            children: [
                              if (widget.item.imageUrl == null)
                                Center(
                                  child: Icon(
                                    _getIconForType(widget.item.type),
                                    size: 40,
                                    color: Colors.white,
                                  ),
                                ),

                              // Rarity indicator
                              Positioned(
                                top: 8,
                                left: 8,
                                child: _buildRarityBadge(widget.item.rarity),
                              ),

                              // Preview button
                              Positioned(
                                top: 8,
                                right: 8,
                                child: Container(
                                  padding: const EdgeInsets.all(6),
                                  decoration: BoxDecoration(
                                    color: Colors.black.withValues(alpha: 0.5),
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.visibility_rounded,
                                    size: 16,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Item Info
                      Expanded(
                        flex: 2,
                        child: Padding(
                          padding: const EdgeInsets.all(12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.item.name,
                                style: Theme.of(context).textTheme.titleSmall
                                    ?.copyWith(fontWeight: FontWeight.w600),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                widget.item.description,
                                style: Theme.of(context).textTheme.bodySmall
                                    ?.copyWith(color: AppTheme.textSecondaryColor),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const Spacer(),
                              _buildItemStatus(
                                widget.item,
                                isOwned,
                                canAfford,
                                isLocked,
                                currentRelationshipLevel,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),

                  // Lock overlay
                  if (isLocked)
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.6),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.lock_rounded,
                              color: AppTheme.warningColor,
                              size: 32,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Locked',
                              style: TextStyle(
                                color: AppTheme.warningColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              relationshipGate.getUnlockRequirement(widget.item),
                              style: TextStyle(
                                color: AppTheme.warningColor,
                                fontSize: 10,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ).animate().fadeIn(
              delay: Duration(milliseconds: 100 * (widget.item.hashCode % 10)),
            ),
          );
        },
      ),
    );
  }

  Widget _buildItemStatus(
    ShopItemModel item,
    bool isOwned,
    bool canAfford,
    bool isLocked,
    int relationshipLevel,
  ) {
    if (isOwned) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: AppTheme.successColor.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppTheme.successColor),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.check_rounded, size: 16, color: AppTheme.successColor),
            const SizedBox(width: 4),
            Text(
              'Owned',
              style: TextStyle(
                color: AppTheme.successColor,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ],
        ),
      );
    }

    if (isLocked) {
      final relationshipGate = ref.read(relationshipGateControllerProvider);
      final requiredLevel = relationshipGate.getRequiredRelationshipLevel(item);
      final relationshipLevelEnum = RelationshipLevel.fromLevel(requiredLevel);

      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: AppTheme.warningColor.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppTheme.warningColor),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.lock_rounded, size: 16, color: AppTheme.warningColor),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                relationshipLevelEnum.displayName,
                style: TextStyle(
                  color: AppTheme.warningColor,
                  fontWeight: FontWeight.w600,
                  fontSize: 10,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      );
    }

    return GestureDetector(
      onTap: canAfford ? () => _purchaseItem(item) : null,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          gradient: canAfford ? AppTheme.primaryGradient : null,
          color: canAfford ? null : Colors.grey.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              AppConstants.currencySymbol,
              style: TextStyle(
                color: canAfford ? Colors.white : Colors.grey,
                fontSize: 12,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              item.price.toString(),
              style: TextStyle(
                color: canAfford ? Colors.white : Colors.grey,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _purchaseItem(ShopItemModel item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.surfaceColor,
        title: Text('Purchase ${item.name}?'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'This will cost ${item.price} ${AppConstants.currencyName}.',
            ),
            const SizedBox(height: 16),
            if (item.imageUrl != null)
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  image: DecorationImage(
                    image: NetworkImage(item.imageUrl!),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(shopProvider.notifier).purchaseItem(item.id);
            },
            child: const Text('Purchase'),
          ),
        ],
      ),
    );
  }

  // Helper methods
  bool _isItemOwned(ShopItemModel item, user) {
    if (user == null) return false;

    switch (item.type) {
      case ShopItemType.environment:
        return user.ownedEnvironments.contains(item.id);
      case ShopItemType.outfit:
      case ShopItemType.accessory:
        return user.ownedOutfits.contains(item.id);
      default:
        return false;
    }
  }

  Widget _buildRarityBadge(ShopItemRarity rarity) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: _getRarityColor(rarity).withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        _getRarityText(rarity),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 8,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  LinearGradient _getGradientForType(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return const LinearGradient(
          colors: [Color(0xFF4ECDC4), Color(0xFF44A08D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case ShopItemType.outfit:
        return const LinearGradient(
          colors: [Color(0xFF6C63FF), Color(0xFF9C27B0)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case ShopItemType.accessory:
        return const LinearGradient(
          colors: [Color(0xFFFF6B6B), Color(0xFFFFE66D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      default:
        return AppTheme.primaryGradient;
    }
  }

  IconData _getIconForType(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return Icons.landscape_rounded;
      case ShopItemType.outfit:
        return Icons.checkroom_rounded;
      case ShopItemType.accessory:
        return Icons.diamond_rounded;
      default:
        return Icons.shopping_bag_rounded;
    }
  }

  Color _getRarityColor(ShopItemRarity rarity) {
    switch (rarity) {
      case ShopItemRarity.common:
        return Colors.grey;
      case ShopItemRarity.rare:
        return Colors.blue;
      case ShopItemRarity.epic:
        return Colors.purple;
      case ShopItemRarity.legendary:
        return Colors.orange;
    }
  }

  String _getRarityText(ShopItemRarity rarity) {
    switch (rarity) {
      case ShopItemRarity.common:
        return 'C';
      case ShopItemRarity.rare:
        return 'R';
      case ShopItemRarity.epic:
        return 'E';
      case ShopItemRarity.legendary:
        return 'L';
    }
  }
}