import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:async';

import '../../core/theme/app_theme.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/common/glassmorphic_container.dart';

class EmailVerificationScreen extends ConsumerStatefulWidget {
  final String email;
  final bool isSignup;
  final String? name; // Required for signup flow
  final bool isPasswordReset; // true for password reset flow

  const EmailVerificationScreen({
    super.key,
    required this.email,
    required this.isSignup,
    this.name,
    this.isPasswordReset = false,
  });

  @override
  ConsumerState<EmailVerificationScreen> createState() => _EmailVerificationScreenState();
}

class _EmailVerificationScreenState extends ConsumerState<EmailVerificationScreen>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  final _codeController = TextEditingController();
  final _passwordController = TextEditingController();
  final _codeFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  bool _isLoading = false;
  bool _showPasswordOption = false;
  bool _obscurePassword = true;
  int _resendCountdown = 0;
  Timer? _countdownTimer;

  @override
  void initState() {
    super.initState();
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _startAnimations();
    _startResendCountdown();
  }

  void _startAnimations() {
    _fadeController.forward();
    _slideController.forward();
  }

  void _startResendCountdown() {
    _resendCountdown = 60;
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _resendCountdown--;
          if (_resendCountdown <= 0) {
            timer.cancel();
          }
        });
      } else {
        timer.cancel();
      }
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _codeController.dispose();
    _passwordController.dispose();
    _codeFocusNode.dispose();
    _passwordFocusNode.dispose();
    _countdownTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: AppTheme.backgroundDecoration,
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24),
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildHeader(),
                      const SizedBox(height: 40),
                      _buildVerificationCard(),
                      const SizedBox(height: 24),
                      _buildBackButton(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // Email Icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryColor.withOpacity(0.3),
                blurRadius: 20,
                spreadRadius: 5,
              ),
            ],
          ),
          child: const Icon(
            Icons.email_rounded,
            size: 40,
            color: Colors.white,
          ),
        ).animate().scale(
          duration: 600.ms,
          curve: Curves.elasticOut,
        ),
        
        const SizedBox(height: 24),
        
        // Title
        Text(
          widget.isPasswordReset
            ? 'Reset Your Password'
            : widget.isSignup
              ? 'Verify Your Email'
              : 'Email Verification',
          style: Theme.of(context).textTheme.displaySmall?.copyWith(
            fontWeight: FontWeight.bold,
            letterSpacing: 1,
          ),
          textAlign: TextAlign.center,
        ).animate(delay: 200.ms).slideY(
          begin: 0.3,
          duration: 600.ms,
          curve: Curves.easeOutBack,
        ),

        const SizedBox(height: 12),

        // Subtitle
        Text(
          widget.isPasswordReset
            ? 'We\'ve sent a password reset code to\n${widget.email}'
            : widget.isSignup
              ? 'We\'ve sent a verification code to\n${widget.email}'
              : 'Please verify your email to continue\n${widget.email}',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: AppTheme.textSecondaryColor,
          ),
          textAlign: TextAlign.center,
        ).animate(delay: 400.ms).slideY(
          begin: 0.3,
          duration: 600.ms,
          curve: Curves.easeOutBack,
        ),
      ],
    );
  }

  Widget _buildVerificationCard() {
    return GlassmorphicContainer(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (!_showPasswordOption) ...[
              _buildCodeInput(),
              const SizedBox(height: 24),
              _buildVerifyButton(),
              const SizedBox(height: 16),
              _buildResendButton(),
              if (!widget.isSignup && !widget.isPasswordReset) ...[
                const SizedBox(height: 16),
                _buildPasswordOptionButton(),
              ],
            ] else ...[
              _buildPasswordInput(),
              const SizedBox(height: 24),
              _buildPasswordSignInButton(),
              const SizedBox(height: 16),
              _buildBackToCodeButton(),
            ],
          ],
        ),
      ),
    ).animate(delay: 600.ms).slideY(
      begin: 0.5,
      duration: 800.ms,
      curve: Curves.easeOutBack,
    );
  }

  Widget _buildCodeInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Verification Code',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _codeController,
          focusNode: _codeFocusNode,
          keyboardType: TextInputType.number,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            letterSpacing: 8,
            fontWeight: FontWeight.bold,
          ),
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(6),
          ],
          decoration: InputDecoration(
            hintText: '000000',
            hintStyle: TextStyle(
              color: AppTheme.textSecondaryColor.withOpacity(0.5),
              letterSpacing: 8,
            ),
            contentPadding: const EdgeInsets.symmetric(vertical: 16),
          ),
          onChanged: (value) {
            if (value.length == 6) {
              _codeFocusNode.unfocus();
            }
          },
        ),
      ],
    );
  }

  Widget _buildPasswordInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Password',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _passwordController,
          focusNode: _passwordFocusNode,
          obscureText: _obscurePassword,
          decoration: InputDecoration(
            hintText: 'Enter your password',
            suffixIcon: IconButton(
              icon: Icon(
                _obscurePassword ? Icons.visibility : Icons.visibility_off,
                color: AppTheme.textSecondaryColor,
              ),
              onPressed: () {
                setState(() {
                  _obscurePassword = !_obscurePassword;
                });
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildVerifyButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isLoading || _codeController.text.length != 6
          ? null
          : _verifyCode,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Text(
              'Verify Code',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
      ),
    );
  }

  Widget _buildPasswordSignInButton() {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: _isLoading || _passwordController.text.isEmpty
          ? null
          : _signInWithPassword,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isLoading
          ? const SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : Text(
              'Sign In with Password',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
      ),
    );
  }

  Widget _buildResendButton() {
    return TextButton(
      onPressed: _resendCountdown > 0 ? null : _resendCode,
      child: Text(
        _resendCountdown > 0
          ? 'Resend code in ${_resendCountdown}s'
          : 'Resend verification code',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: _resendCountdown > 0
            ? AppTheme.textSecondaryColor
            : AppTheme.primaryColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildPasswordOptionButton() {
    return TextButton(
      onPressed: _isLoading ? null : () {
        setState(() {
          _showPasswordOption = true;
        });
      },
      child: Text(
        'Use password instead',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: AppTheme.primaryColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildBackToCodeButton() {
    return TextButton(
      onPressed: _isLoading ? null : () {
        setState(() {
          _showPasswordOption = false;
          _passwordController.clear();
        });
      },
      child: Text(
        'Back to verification code',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: AppTheme.textSecondaryColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildBackButton() {
    return TextButton.icon(
      onPressed: _isLoading ? null : () {
        Navigator.of(context).pop();
      },
      icon: const Icon(Icons.arrow_back, size: 18),
      label: const Text('Back to sign in'),
      style: TextButton.styleFrom(
        foregroundColor: AppTheme.textSecondaryColor,
      ),
    );
  }

  // Action methods
  Future<void> _verifyCode() async {
    if (_isLoading || _codeController.text.length != 6) return;

    setState(() => _isLoading = true);

    try {
      if (widget.isPasswordReset) {
        await ref.read(authProvider.notifier).verifyPasswordResetCode(
          email: widget.email,
          code: _codeController.text,
        );
      } else {
        await ref.read(authProvider.notifier).verifyEmailWithCode(
          email: widget.email,
          code: _codeController.text,
          name: widget.name,
        );
      }
    } catch (e) {
      _showErrorSnackBar(e.toString());
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _signInWithPassword() async {
    if (_isLoading || _passwordController.text.isEmpty) return;

    setState(() => _isLoading = true);

    try {
      await ref.read(authProvider.notifier).usePasswordInstead(
        email: widget.email,
        password: _passwordController.text,
      );
    } catch (e) {
      _showErrorSnackBar(e.toString());
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _resendCode() async {
    try {
      await ref.read(authProvider.notifier).resendVerificationCode(widget.email);
      _startResendCountdown();
      _showSuccessSnackBar('Verification code sent!');
    } catch (e) {
      _showErrorSnackBar('Failed to resend code: ${e.toString()}');
    }
  }

  void _showErrorSnackBar(String message) {
    if (!mounted) return;

    HapticFeedback.lightImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.errorColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    if (!mounted) return;

    HapticFeedback.lightImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.successColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }
}
