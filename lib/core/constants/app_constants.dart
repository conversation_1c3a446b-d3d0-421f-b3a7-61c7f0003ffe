class AppConstants {
  // App Info
  static const String appName = 'EllahAI';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Your caring AI companion';

  // API Endpoints
  static const String baseUrl = 'https://russian-besides-soma-wells.trycloudflare.com'; // ngrok tunnel for development
  static const String websocketUrl = 'wss://russian-besides-soma-wells.trycloudflare.com'; // ngrok tunnel for WebSocket

  // Debug URLs (uncomment for local testing without ngrok)
  // static const String baseUrl = 'http://127.0.0.1:9000';
  // static const String websocketUrl = 'ws://127.0.0.1:9000';
  
  // Storage Keys
  static const String userBox = 'user_box';
  static const String chatBox = 'chat_box';
  static const String settingsBox = 'settings_box';
  static const String progressBox = 'progress_box';
  
  // User Preferences Keys
  static const String isDarkModeKey = 'is_dark_mode';
  static const String selectedEnvironmentKey = 'selected_environment';
  static const String notificationsEnabledKey = 'notifications_enabled';
  static const String voiceEnabledKey = 'voice_enabled';
  static const String autoPlayVoiceKey = 'auto_play_voice';
  
  // Gaming & Progress
  static const int xpPerLevel = 100;
  static const int baseXpPerMessage = 10;
  static const int baseXpPerVoiceMessage = 15;
  static const int dailyBonusXp = 50;
  static const List<int> levelThresholds = [
    0, 100, 250, 500, 1000, 2000, 4000, 8000, 15000, 30000, 50000
  ];
  
  // Shop & Currency
  static const String currencyName = 'Hearts';
  static const String currencySymbol = '💖';
  static const int dailyHearts = 20;
  static const int heartsPerLevel = 50;
  
  // Voice Settings
  static const double defaultSpeechRate = 1.0;
  static const double defaultPitch = 1.0;
  static const String defaultVoice = 'en-US-Neural2-F'; // Google Cloud TTS voice

  // Audio Recording Settings
  static const int audioSampleRate = 16000;
  static const int audioChannels = 1;
  static const int audioBitRate = 256000;
  static const double minAudioAmplitude = 0.02;
  static const double minAudioDuration = 0.5; // seconds
  static const double minAvgAmplitude = 0.005;
  
  // Unity Communication
  static const String unityStartTalkingCommand = 'startTalking';
  static const String unityStopTalkingCommand = 'stopTalking';
  static const String unityChangeExpressionCommand = 'changeExpression';
  static const String unityChangeEnvironmentCommand = 'changeEnvironment';
  static const String unityChangeOutfitCommand = 'changeOutfit';
  
  // Animations & UI
  static const Duration defaultAnimationDuration = Duration(milliseconds: 300);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  static const Duration shortAnimationDuration = Duration(milliseconds: 150);
  
  // Chat Settings
  static const int maxMessagesInChat = 50; // For performance
  static const int maxMessageLength = 1000;
  static const Duration typingIndicatorDelay = Duration(milliseconds: 800);
  
  // Environments
  static const List<String> defaultEnvironments = [
    'cozy_room',
    'modern_apartment', 
    'garden_terrace',
    'starry_night',
    'cherry_blossom',
    'cyberpunk_city',
  ];
  
  // Companion Personalities
  static const List<String> companionPersonalities = [
    'caring_friend',
    'playful_companion',
    'wise_mentor',
    'romantic_partner',
    'supportive_therapist',
  ];
  
  // Error Messages
  static const String networkError = 'Please check your internet connection';
  static const String serverError = 'Server is currently unavailable';
  static const String authError = 'Authentication failed';
  static const String voicePermissionError = 'Microphone permission required';
  static const String genericError = 'Something went wrong. Please try again.';
}
