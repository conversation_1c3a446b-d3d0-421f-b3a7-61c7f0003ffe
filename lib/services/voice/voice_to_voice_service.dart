import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../chat/websocket_service.dart';
import '../chat/chat_service.dart';
import 'realtime_voice_service.dart';
import '../../providers/chat_provider.dart';

/// Service that connects VAD (Voice Activity Detection) to backend processing
/// This creates a complete voice-to-voice pipeline:
/// VAD detects speech → Sends audio to backend → Backend processes with Groq Whisper + Hume AI → Returns TTS audio
class VoiceToVoiceService {
  final WebSocketService _webSocketService;
  final ChatService _chatService;
  final RealtimeVoiceService _voiceService;
  
  // State management
  bool _isInitialized = false;
  bool _isListening = false;
  String? _currentConversationId;
  
  // Stream controllers for voice-to-voice events
  final _transcriptionController = StreamController<String>.broadcast();
  final _emotionController = StreamController<Map<String, dynamic>>.broadcast();
  final _responseController = StreamController<String>.broadcast();
  final _errorController = StreamController<String>.broadcast();
  
  VoiceToVoiceService({
    required WebSocketService webSocketService,
    required ChatService chatService,
    required RealtimeVoiceService voiceService,
  }) : _webSocketService = webSocketService,
       _chatService = chatService,
       _voiceService = voiceService {
    _setupListeners();
  }

  // Getters
  Stream<String> get transcriptionStream => _transcriptionController.stream;
  Stream<Map<String, dynamic>> get emotionStream => _emotionController.stream;
  Stream<String> get responseStream => _responseController.stream;
  Stream<String> get errorStream => _errorController.stream;
  bool get isInitialized => _isInitialized;
  bool get isListening => _isListening;

  /// Initialize the voice-to-voice service
  Future<bool> initialize({String? conversationId}) async {
    if (_isInitialized) return true;

    try {
      debugPrint('VoiceToVoiceService: Initializing...');
      
      // Initialize the voice service
      final voiceInitialized = await _voiceService.initialize();
      if (!voiceInitialized) {
        _handleError('Failed to initialize voice service');
        return false;
      }

      // Set up audio processor callback to send audio to backend
      _voiceService.setAudioProcessor(_processAudioChunk);
      
      // Store conversation ID
      _currentConversationId = conversationId;
      
      _isInitialized = true;
      debugPrint('VoiceToVoiceService: Initialization complete');
      return true;
      
    } catch (e) {
      _handleError('Failed to initialize voice-to-voice service: $e');
      return false;
    }
  }

  /// Set up listeners for backend responses
  void _setupListeners() {
    // Listen to chat service streams for backend responses
    _chatService.transcriptionStream.listen((transcription) {
      debugPrint('VoiceToVoiceService: Received transcription: "$transcription"');
      _transcriptionController.add(transcription);
    });

    _chatService.emotionStream.listen((emotion) {
      debugPrint('VoiceToVoiceService: Received emotion: $emotion');
      _emotionController.add(emotion);
    });

    // Listen to WebSocket messages for AI responses
    _webSocketService.messageStream.listen((wsMessage) {
      switch (wsMessage.type) {
        case 'llm_response_chunk':
          final content = wsMessage.data['content'] as String? ?? '';
          if (content.isNotEmpty) {
            _responseController.add(content);
          }
          break;
        case 'transcription_partial':
          final text = wsMessage.data['text'] as String? ?? '';
          if (text.isNotEmpty) {
            _transcriptionController.add(text);
          }
          break;
        case 'emotion_detected':
          _emotionController.add(wsMessage.data);
          break;
        case 'audio_chunk':
          // TTS audio chunks are handled by ChatService -> AudioPlaybackService
          // We just log here for debugging
          final chunkId = wsMessage.data['chunk_id'] as String? ?? 'unknown';
          final isFinal = wsMessage.data['is_final'] as bool? ?? false;
          debugPrint('VoiceToVoiceService: Received TTS audio chunk $chunkId (final: $isFinal)');
          break;
      }
    });

    // Listen to WebSocket errors
    _webSocketService.errorStream.listen((error) {
      _handleError('WebSocket error: $error');
    });
  }

  /// Process audio chunk by sending to backend
  Future<void> _processAudioChunk(Uint8List audioData, String chunkId, bool isFinal) async {
    if (!_webSocketService.isConnected) {
      _handleError('WebSocket not connected - cannot send audio');
      return;
    }

    try {
      debugPrint('VoiceToVoiceService: Sending audio chunk to backend (${audioData.length} bytes, final: $isFinal)');
      
      // Send audio chunk to backend via WebSocket
      _webSocketService.sendAudioChunk(
        audioData: audioData,
        chunkId: chunkId,
        isFinal: isFinal,
        conversationId: _currentConversationId,
      );
      
      debugPrint('VoiceToVoiceService: Audio chunk sent successfully');
      
    } catch (e) {
      _handleError('Failed to send audio chunk: $e');
    }
  }

  /// Start voice listening
  Future<bool> startListening({String? conversationId}) async {
    if (!_isInitialized) {
      _handleError('Service not initialized');
      return false;
    }

    if (_isListening) {
      debugPrint('VoiceToVoiceService: Already listening');
      return true;
    }

    try {
      // Update conversation ID if provided
      if (conversationId != null) {
        _currentConversationId = conversationId;
      }

      // Start VAD listening
      final success = await _voiceService.startListening();
      if (success) {
        _isListening = true;
        debugPrint('VoiceToVoiceService: Started listening');
      } else {
        _handleError('Failed to start VAD listening');
      }
      
      return success;
      
    } catch (e) {
      _handleError('Failed to start listening: $e');
      return false;
    }
  }

  /// Stop voice listening
  Future<void> stopListening() async {
    if (!_isListening) return;

    try {
      await _voiceService.stopListening();
      _isListening = false;
      debugPrint('VoiceToVoiceService: Stopped listening');
      
    } catch (e) {
      _handleError('Failed to stop listening: $e');
    }
  }

  /// Handle errors
  void _handleError(String error) {
    debugPrint('VoiceToVoiceService Error: $error');
    _errorController.add(error);
  }

  /// Dispose resources
  void dispose() {
    debugPrint('VoiceToVoiceService: Disposing...');
    _isListening = false;
    _isInitialized = false;
    
    _transcriptionController.close();
    _emotionController.close();
    _responseController.close();
    _errorController.close();
  }
}

/// Provider for VoiceToVoiceService
final voiceToVoiceServiceProvider = Provider<VoiceToVoiceService>((ref) {
  final webSocketService = ref.watch(webSocketServiceProvider);
  final chatService = ref.watch(chatServiceProvider);
  final voiceService = RealtimeVoiceService(); // Singleton

  final service = VoiceToVoiceService(
    webSocketService: webSocketService,
    chatService: chatService,
    voiceService: voiceService,
  );

  // Dispose when provider is disposed
  ref.onDispose(() {
    service.dispose();
  });

  return service;
});
