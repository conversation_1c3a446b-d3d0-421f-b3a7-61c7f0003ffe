import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:audioplayers/audioplayers.dart';
import '../chat/websocket_service.dart';

class EmotionContext {
  final String primaryEmotion;
  final double intensity;

  EmotionContext({
    required this.primaryEmotion,
    required this.intensity,
  });

  Map<String, dynamic> toJson() {
    return {
      'primary_emotion': primaryEmotion,
      'intensity': intensity,
    };
  }
}

class VoiceSettings {
  final String voiceName;
  final EmotionContext? emotionContext;
  final double speed;
  final String? actingInstructions;

  VoiceSettings({
    required this.voiceName,
    this.emotionContext,
    this.speed = 1.0,
    this.actingInstructions,
  });

  Map<String, dynamic> toJson() {
    return {
      'voice_name': voiceName,
      if (emotionContext != null) 'emotion_context': emotionContext!.toJson(),
      'speed': speed,
      if (actingInstructions != null) 'acting_instructions': actingInstructions,
    };
  }
}

class AudioChunk {
  final Uint8List audioData;
  final String chunkId;
  final bool isFinal;
  final VoiceSettings voiceSettings;
  final int timestamp;
  final String? requestId;

  AudioChunk({
    required this.audioData,
    required this.chunkId,
    required this.isFinal,
    required this.voiceSettings,
    required this.timestamp,
    this.requestId,
  });

  factory AudioChunk.fromJson(Map<String, dynamic> json) {
    final audioDataBase64 = json['data'] as String;
    final audioData = base64Decode(audioDataBase64);
    
    return AudioChunk(
      audioData: audioData,
      chunkId: json['chunk_id'],
      isFinal: json['is_final'] ?? false,
      voiceSettings: VoiceSettings(
        voiceName: json['voice_settings']['voice_name'] ?? 'nova',
        emotionContext: json['voice_settings']['emotion_context'] != null
            ? EmotionContext(
                primaryEmotion: json['voice_settings']['emotion_context']['primary_emotion'],
                intensity: json['voice_settings']['emotion_context']['intensity'].toDouble(),
              )
            : null,
        speed: json['voice_settings']['speed']?.toDouble() ?? 1.0,
        actingInstructions: json['voice_settings']['acting_instructions'],
      ),
      timestamp: json['timestamp'],
      requestId: json['request_id'],
    );
  }
}

enum TTSState {
  idle,
  requesting,
  streaming,
  playing,
  error,
}

class TTSStreamingService {
  final WebSocketService _webSocketService;
  final AudioPlayer _audioPlayer = AudioPlayer();
  
  TTSState _state = TTSState.idle;
  final _stateController = StreamController<TTSState>.broadcast();
  final _errorController = StreamController<String>.broadcast();
  final _audioChunkController = StreamController<AudioChunk>.broadcast();
  
  // Audio streaming management
  final List<Uint8List> _audioBuffer = [];
  bool _isPlaying = false;
  String? _currentRequestId;
  Timer? _playbackTimer;
  
  TTSStreamingService(this._webSocketService) {
    _setupWebSocketListeners();
    _setupAudioPlayer();
  }

  // Getters
  Stream<TTSState> get stateStream => _stateController.stream;
  Stream<String> get errorStream => _errorController.stream;
  Stream<AudioChunk> get audioChunkStream => _audioChunkController.stream;
  TTSState get state => _state;
  bool get isActive => _state == TTSState.streaming || _state == TTSState.playing;

  void _setupWebSocketListeners() {
    _webSocketService.messageStream.listen((wsMessage) {
      if (wsMessage.type == 'audio_chunk') {
        _handleAudioChunk(wsMessage.data);
      } else if (wsMessage.type == 'tts_error') {
        _handleTTSError(wsMessage.data);
      }
    });
  }

  void _setupAudioPlayer() {
    _audioPlayer.onPlayerStateChanged.listen((state) {
      if (state == PlayerState.completed) {
        _onAudioPlaybackCompleted();
      }
    });
  }

  void _handleAudioChunk(Map<String, dynamic> data) {
    try {
      final audioChunk = AudioChunk.fromJson(data);
      
      // Only process chunks for current request
      if (_currentRequestId != null && audioChunk.requestId != _currentRequestId) {
        return;
      }
      
      _audioChunkController.add(audioChunk);
      _audioBuffer.add(audioChunk.audioData);
      
      // Start playback if not already playing
      if (!_isPlaying && _audioBuffer.isNotEmpty) {
        _startStreamingPlayback();
      }
      
      // If this is the final chunk, mark streaming as complete
      if (audioChunk.isFinal) {
        _setState(TTSState.playing);
      }
      
    } catch (e) {
      _errorController.add('Failed to process audio chunk: $e');
      _setState(TTSState.error);
    }
  }

  void _handleTTSError(Map<String, dynamic> data) {
    final errorMessage = data['message'] ?? 'TTS streaming error';
    _errorController.add(errorMessage);
    _setState(TTSState.error);
    _cleanup();
  }

  Future<void> streamTTSResponse({
    required String text,
    VoiceSettings? voiceSettings,
    String? requestId,
  }) async {
    if (!_webSocketService.isConnected) {
      _errorController.add('WebSocket not connected');
      return;
    }

    // Stop any current playback
    await stopTTS();
    
    _currentRequestId = requestId ?? DateTime.now().millisecondsSinceEpoch.toString();
    _setState(TTSState.requesting);
    
    final settings = voiceSettings ?? VoiceSettings(voiceName: 'nova');
    
    _webSocketService.sendMessage('tts_request', {
      'text': text,
      'voice_settings': settings.toJson(),
      'request_id': _currentRequestId,
    });
    
    _setState(TTSState.streaming);
  }

  Future<void> _startStreamingPlayback() async {
    if (_isPlaying || _audioBuffer.isEmpty) return;
    
    _isPlaying = true;
    
    try {
      // Combine audio chunks for playback
      final combinedAudio = _combineAudioChunks(_audioBuffer);
      
      // Create a temporary audio source from bytes
      final source = BytesSource(combinedAudio);
      await _audioPlayer.play(source);
      
      // Clear played chunks but keep some buffer
      if (_audioBuffer.length > 3) {
        _audioBuffer.removeRange(0, _audioBuffer.length - 2);
      }
      
    } catch (e) {
      _errorController.add('Failed to play audio: $e');
      _setState(TTSState.error);
      _isPlaying = false;
    }
  }

  Uint8List _combineAudioChunks(List<Uint8List> chunks) {
    final totalLength = chunks.fold<int>(0, (sum, chunk) => sum + chunk.length);
    final combined = Uint8List(totalLength);
    
    int offset = 0;
    for (final chunk in chunks) {
      combined.setRange(offset, offset + chunk.length, chunk);
      offset += chunk.length;
    }
    
    return combined;
  }

  void _onAudioPlaybackCompleted() {
    _isPlaying = false;
    
    // If there are more chunks to play, continue
    if (_audioBuffer.isNotEmpty && _state == TTSState.streaming) {
      _startStreamingPlayback();
    } else if (_state == TTSState.playing) {
      // All chunks played and streaming complete
      _setState(TTSState.idle);
      _cleanup();
    }
  }

  Future<void> stopTTS() async {
    await _audioPlayer.stop();
    _setState(TTSState.idle);
    _cleanup();
  }

  Future<void> pauseTTS() async {
    await _audioPlayer.pause();
  }

  Future<void> resumeTTS() async {
    await _audioPlayer.resume();
  }

  void _setState(TTSState newState) {
    if (_state != newState) {
      _state = newState;
      _stateController.add(_state);
    }
  }

  void _cleanup() {
    _audioBuffer.clear();
    _isPlaying = false;
    _currentRequestId = null;
    _playbackTimer?.cancel();
  }

  // Voice configuration
  Future<List<Map<String, dynamic>>> getAvailableVoices() async {
    // This would typically come from the backend
    return [
      {
        'name': 'nova',
        'description': 'Warm, friendly tone',
        'gender': 'neutral',
        'languages': ['en'],
        'emotion_range': ['happy', 'calm', 'excited'],
      },
      {
        'name': 'shimmer',
        'description': 'Energetic, youthful voice',
        'gender': 'female',
        'languages': ['en'],
        'emotion_range': ['playful', 'enthusiastic'],
      },
      {
        'name': 'onyx',
        'description': 'Deep, authoritative voice',
        'gender': 'male',
        'languages': ['en'],
        'emotion_range': ['confident', 'serious'],
      },
    ];
  }

  void dispose() {
    _audioPlayer.dispose();
    _stateController.close();
    _errorController.close();
    _audioChunkController.close();
    _playbackTimer?.cancel();
  }
}
