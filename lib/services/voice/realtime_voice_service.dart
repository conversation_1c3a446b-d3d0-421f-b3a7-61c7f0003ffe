import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:vad/vad.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:audio_waveforms/audio_waveforms.dart';

enum VoiceState {
  idle,
  listening,
  processing,
  speaking,
  error,
}

class VoiceProcessingResult {
  final String transcription;
  final double confidence;
  final Duration processingTime;
  final List<double> audioSamples;

  const VoiceProcessingResult({
    required this.transcription,
    required this.confidence,
    required this.processingTime,
    required this.audioSamples,
  });
}

class RealtimeVoiceService {
  static final RealtimeVoiceService _instance = RealtimeVoiceService._internal();
  factory RealtimeVoiceService() => _instance;
  RealtimeVoiceService._internal();

  // VAD Handler
  final _vadHandler = VadHandler.create(isDebug: kDebugMode);

  // Speech to Text Controller
  final _speechToText = SpeechToText();

  // Audio Recorder for fallback
  RecorderController? _recorderController;
  
  // State management
  VoiceState _state = VoiceState.idle;
  bool _isInitialized = false;
  
  // Stream controllers
  final _stateController = StreamController<VoiceState>.broadcast();
  final _transcriptionController = StreamController<VoiceProcessingResult>.broadcast();
  final _errorController = StreamController<String>.broadcast();
  final _vadEventsController = StreamController<String>.broadcast();
  
  // Audio processing
  List<double> _currentAudioBuffer = [];
  String? _tempAudioPath;

  // VAD logging state
  int _frameCount = 0;
  double _lastSpeechProb = 0.0;

  // Backend audio processing callbacks (support multiple)
  final List<Future<void> Function(Uint8List audioData, String chunkId, bool isFinal)> _audioProcessors = [];
  
  // Configuration - Adjusted for better speech detection
  static const double _vadPositiveThreshold = 0.7;  // Higher threshold to reduce false positives
  static const double _vadNegativeThreshold = 0.4;  // Higher threshold to reduce noise triggering
  static const int _vadMinSpeechFrames = 10;        // Require more frames before considering speech
  static const int _vadRedemptionFrames = 15;       // More frames to confirm end of speech

  // Getters
  Stream<VoiceState> get stateStream => _stateController.stream;
  Stream<VoiceProcessingResult> get transcriptionStream => _transcriptionController.stream;
  Stream<String> get errorStream => _errorController.stream;
  Stream<String> get vadEventsStream => _vadEventsController.stream;
  VoiceState get currentState => _state;
  bool get isInitialized => _isInitialized;

  // Setters
  void setAudioProcessor(Future<void> Function(Uint8List audioData, String chunkId, bool isFinal) processor) {
    _audioProcessors.clear(); // Clear existing processors for backward compatibility
    _audioProcessors.add(processor);
    debugPrint('RealtimeVoiceService: Audio processor callback configured');
  }

  void addAudioProcessor(Future<void> Function(Uint8List audioData, String chunkId, bool isFinal) processor) {
    _audioProcessors.add(processor);
    debugPrint('RealtimeVoiceService: Additional audio processor added (${_audioProcessors.length} total)');
  }

  void removeAudioProcessor(Future<void> Function(Uint8List audioData, String chunkId, bool isFinal) processor) {
    _audioProcessors.remove(processor);
    debugPrint('RealtimeVoiceService: Audio processor removed (${_audioProcessors.length} remaining)');
  }

  /// Check if audio processor is set
  bool get hasAudioProcessor => _audioProcessors.isNotEmpty;

  /// Enable debug mode to bypass audio validation
  bool _debugMode = false;
  void setDebugMode(bool enabled) {
    _debugMode = enabled;
    debugPrint('RealtimeVoiceService: Debug mode ${enabled ? 'enabled' : 'disabled'}');
  }

  /// Test audio recording and processing (for debugging)
  Future<void> testAudioRecording() async {
    debugPrint('🧪 TESTING: Starting audio recording test...');

    if (!_isInitialized) {
      debugPrint('❌ TESTING: Service not initialized');
      return;
    }

    // Start listening for 5 seconds
    final success = await startListening();
    if (!success) {
      debugPrint('❌ TESTING: Failed to start listening');
      return;
    }

    debugPrint('🧪 TESTING: Recording for 5 seconds...');
    await Future.delayed(const Duration(seconds: 5));

    await stopListening();
    debugPrint('🧪 TESTING: Audio recording test completed');
  }

  /// Initialize the voice service with VAD and Whisper
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      _setState(VoiceState.idle);
      
      // Request permissions
      final permissionGranted = await _requestPermissions();
      if (!permissionGranted) {
        _handleError('Microphone permission denied');
        return false;
      }

      // Initialize VAD
      await _initializeVAD();

      // Initialize Speech to Text
      await _initializeSpeechToText();
      
      // Initialize audio recorder as fallback
      _recorderController = RecorderController();
      
      _isInitialized = true;
      debugPrint('RealtimeVoiceService: Initialization complete');
      return true;
      
    } catch (e) {
      _handleError('Failed to initialize voice service: $e');
      return false;
    }
  }

  /// Start real-time voice listening with VAD
  Future<bool> startListening() async {
    if (!_isInitialized) {
      _handleError('Voice service not initialized');
      return false;
    }

    if (_state == VoiceState.listening) {
      debugPrint('RealtimeVoiceService: Already listening');
      return true;
    }

    try {
      _setState(VoiceState.listening);
      _currentAudioBuffer.clear();
      
      // Start VAD listening with detailed configuration logging
      debugPrint('🎯 VAD: Starting listening with configuration:');
      debugPrint('🎯   Frame samples: 1536 (legacy model)');
      debugPrint('🎯   Min speech frames: $_vadMinSpeechFrames');
      debugPrint('🎯   Redemption frames: $_vadRedemptionFrames');
      debugPrint('🎯   Positive speech threshold: $_vadPositiveThreshold');
      debugPrint('🎯   Negative speech threshold: $_vadNegativeThreshold');
      debugPrint('🎯   Model: legacy');
      debugPrint('🎯   Submit on pause: false');

      await _vadHandler.startListening(
        frameSamples: 1536, // For legacy model
        minSpeechFrames: _vadMinSpeechFrames,
        preSpeechPadFrames: 0,
        redemptionFrames: _vadRedemptionFrames,
        positiveSpeechThreshold: _vadPositiveThreshold,
        negativeSpeechThreshold: _vadNegativeThreshold,
        submitUserSpeechOnPause: false,
        model: 'legacy',
        baseAssetPath: 'assets/packages/vad/assets/',
        onnxWASMBasePath: 'assets/packages/vad/assets/',
      );

      debugPrint('✅ VAD: Successfully started listening');
      debugPrint('✅ VAD: Microphone is now active and monitoring for speech');
      
      debugPrint('RealtimeVoiceService: Started listening');
      return true;
      
    } catch (e) {
      _handleError('Failed to start listening: $e');
      return false;
    }
  }

  /// Stop voice listening
  Future<void> stopListening() async {
    if (_state != VoiceState.listening) {
      debugPrint('🛑 VAD: Stop listening called but not currently listening (state: $_state)');
      return;
    }

    try {
      debugPrint('🛑 VAD: Stopping voice listening...');
      await _vadHandler.stopListening();
      _setState(VoiceState.idle);

      // Reset frame counting
      _frameCount = 0;
      _lastSpeechProb = 0.0;

      debugPrint('✅ VAD: Successfully stopped listening');
      debugPrint('✅ VAD: Microphone is now inactive');
    } catch (e) {
      debugPrint('💥 VAD: Error stopping listening: $e');
      _handleError('Failed to stop listening: $e');
    }
  }

  /// Process audio samples by sending to backend via WebSocket
  Future<void> _processAudioWithBackend(List<double> audioSamples) async {
    if (audioSamples.isEmpty) {
      debugPrint('⚠️ PROCESSING: No audio samples to process');
      return;
    }

    try {
      _setState(VoiceState.processing);
      final startTime = DateTime.now();

      debugPrint('🔄 PROCESSING: Starting audio transcription...');
      debugPrint('🔄 PROCESSING: Audio samples: ${audioSamples.length}');
      debugPrint('🔄 PROCESSING: Estimated duration: ${(audioSamples.length / 16000).toStringAsFixed(2)}s');

      // Analyze audio quality and validate before sending
      if (audioSamples.isNotEmpty) {
        final maxAmplitude = audioSamples.map((s) => s.abs()).reduce((a, b) => a > b ? a : b);
        final avgAmplitude = audioSamples.map((s) => s.abs()).reduce((a, b) => a + b) / audioSamples.length;
        final rmsAmplitude = sqrt(audioSamples.map((s) => s * s).reduce((a, b) => a + b) / audioSamples.length);

        debugPrint('🔄 PROCESSING: Audio quality analysis:');
        debugPrint('🔄   Max amplitude: ${maxAmplitude.toStringAsFixed(4)}');
        debugPrint('🔄   Avg amplitude: ${avgAmplitude.toStringAsFixed(4)}');
        debugPrint('🔄   RMS amplitude: ${rmsAmplitude.toStringAsFixed(4)}');
        debugPrint('🔄   Duration: ${(audioSamples.length / 16000).toStringAsFixed(2)}s');

        // Validate audio quality before sending (unless in debug mode)
        if (!_debugMode) {
          if (maxAmplitude < 0.02) {
            debugPrint('❌ PROCESSING: Rejecting audio - amplitude too low (${maxAmplitude.toStringAsFixed(4)} < 0.02)');
            return;
          }

          if (audioSamples.length < 8000) { // Less than 0.5 seconds at 16kHz
            debugPrint('❌ PROCESSING: Rejecting audio - duration too short (${(audioSamples.length / 16000).toStringAsFixed(2)}s < 0.5s)');
            return;
          }

          if (avgAmplitude < 0.005) {
            debugPrint('❌ PROCESSING: Rejecting audio - average amplitude too low (${avgAmplitude.toStringAsFixed(4)} < 0.005)');
            return;
          }
        } else {
          debugPrint('🐛 DEBUG MODE: Bypassing audio quality validation');
        }

        if (maxAmplitude > 0.9) {
          debugPrint('⚠️ PROCESSING: Warning - Very high audio amplitude (possible clipping)');
        }
      } else {
        debugPrint('❌ PROCESSING: Rejecting audio - no samples');
        return;
      }

      // Convert audio samples to PCM16 format for backend
      final audioBytes = _convertSamplesToBytes(audioSamples);

      // Send audio to backend via callbacks
      if (_audioProcessors.isNotEmpty) {
        final chunkId = 'vad_${DateTime.now().millisecondsSinceEpoch}';
        debugPrint('🔄 PROCESSING: Sending validated audio to ${_audioProcessors.length} processor(s) (chunk: $chunkId)');
        debugPrint('🔄 PROCESSING: Audio bytes: ${audioBytes.length} bytes');

        // Call all registered audio processors
        for (final processor in _audioProcessors) {
          try {
            await processor(audioBytes, chunkId, true); // isFinal = true for VAD chunks
          } catch (e) {
            debugPrint('❌ PROCESSING: Error in audio processor: $e');
          }
        }

        final processingTime = DateTime.now().difference(startTime);
        debugPrint('✅ PROCESSING: Audio sent to backend successfully');
        debugPrint('✅ PROCESSING: Processing time: ${processingTime.inMilliseconds}ms');

        // Create a result indicating audio was sent to backend
        final voiceResult = VoiceProcessingResult(
          transcription: 'Audio sent to backend for processing...',
          confidence: 1.0,
          processingTime: processingTime,
          audioSamples: audioSamples,
        );

        _transcriptionController.add(voiceResult);
      } else {
        debugPrint('⚠️ PROCESSING: No audio processor callback configured');
      }

    } catch (e) {
      debugPrint('💥 PROCESSING: Error processing audio: $e');
      _handleError('Failed to process audio: $e');
    } finally {
      if (_state == VoiceState.processing) {
        _setState(VoiceState.listening);
        debugPrint('🔄 PROCESSING: Returning to listening state');
      }
    }
  }

  /// Convert audio samples to PCM16 bytes
  Uint8List _convertSamplesToBytes(List<double> samples) {
    final bytes = <int>[];
    for (final sample in samples) {
      // Convert from [-1.0, 1.0] to [-32768, 32767]
      final intSample = (sample * 32767).round().clamp(-32768, 32767);
      // Little-endian 16-bit
      bytes.add(intSample & 0xFF);
      bytes.add((intSample >> 8) & 0xFF);
    }
    return Uint8List.fromList(bytes);
  }

  /// Initialize VAD with event handlers
  Future<void> _initializeVAD() async {
    // VAD handler is already created in the field declaration
    debugPrint('RealtimeVoiceService: Setting up VAD event handlers...');

    // Speech start detection
    _vadHandler.onSpeechStart.listen((_) {
      final timestamp = DateTime.now().toIso8601String();
      _vadEventsController.add('Speech start detected');
      debugPrint('🎤 VAD: Speech start detected at $timestamp');
      debugPrint('🎤 VAD: Transitioning from silence to potential speech');
    });

    // Real speech start (confirmed)
    _vadHandler.onRealSpeechStart.listen((_) {
      final timestamp = DateTime.now().toIso8601String();
      _vadEventsController.add('Real speech confirmed');
      debugPrint('🗣️ VAD: Real speech confirmed at $timestamp');
      debugPrint('🗣️ VAD: Speech detection confidence threshold met');
      debugPrint('🗣️ VAD: Starting audio buffer collection...');
    });

    // Speech end with audio samples
    _vadHandler.onSpeechEnd.listen((List<double> samples) {
      final timestamp = DateTime.now().toIso8601String();
      final duration = samples.length / 16000; // Assuming 16kHz sample rate
      _vadEventsController.add('Speech end detected (${samples.length} samples)');

      debugPrint('🛑 VAD: Speech end detected at $timestamp');
      debugPrint('🛑 VAD: Audio samples collected: ${samples.length}');
      debugPrint('🛑 VAD: Estimated speech duration: ${duration.toStringAsFixed(2)}s');
      debugPrint('🛑 VAD: Sample rate: 16kHz');

      if (samples.isNotEmpty) {
        final maxAmplitude = samples.map((s) => s.abs()).reduce((a, b) => a > b ? a : b);
        final avgAmplitude = samples.map((s) => s.abs()).reduce((a, b) => a + b) / samples.length;
        debugPrint('🛑 VAD: Max amplitude: ${maxAmplitude.toStringAsFixed(4)}');
        debugPrint('🛑 VAD: Avg amplitude: ${avgAmplitude.toStringAsFixed(4)}');

        // Check for silence (very low amplitude)
        if (maxAmplitude < 0.01) {
          debugPrint('⚠️ VAD: Warning - Very low audio amplitude detected (possible silence)');
        }
      }

      debugPrint('🔄 VAD: Sending audio for transcription processing...');

      // Process the audio samples with backend
      _processAudioWithBackend(samples);
    });

    // VAD misfire detection
    _vadHandler.onVADMisfire.listen((_) {
      final timestamp = DateTime.now().toIso8601String();
      _vadEventsController.add('VAD misfire detected');
      debugPrint('❌ VAD: Misfire detected at $timestamp');
      debugPrint('❌ VAD: False positive speech detection - ignoring');
      debugPrint('❌ VAD: This usually happens with background noise or very short sounds');
    });

    // Frame processing for real-time feedback
    _vadHandler.onFrameProcessed.listen((frameData) {
      // Log detailed frame information periodically
      _frameCount++;

      // Log every 100th frame to avoid spam, but always log high confidence frames
      final shouldLog = _frameCount % 100 == 0 || frameData.isSpeech > 0.8;

      if (shouldLog) {
        debugPrint('📊 VAD Frame #$_frameCount:');
        debugPrint('📊   Speech probability: ${frameData.isSpeech.toStringAsFixed(3)}');
        debugPrint('📊   Non-speech probability: ${frameData.notSpeech.toStringAsFixed(3)}');
        debugPrint('📊   Frame size: ${frameData.frame.length} samples');

        if (frameData.frame.isNotEmpty) {
          final frameMax = frameData.frame.map((s) => s.abs()).reduce((a, b) => a > b ? a : b);
          final frameAvg = frameData.frame.map((s) => s.abs()).reduce((a, b) => a + b) / frameData.frame.length;
          debugPrint('📊   Frame max amplitude: ${frameMax.toStringAsFixed(4)}');
          debugPrint('📊   Frame avg amplitude: ${frameAvg.toStringAsFixed(4)}');
        }
      }

      // Always log high confidence speech detection
      if (frameData.isSpeech > 0.8) {
        debugPrint('🔥 VAD: High speech confidence detected: ${frameData.isSpeech.toStringAsFixed(3)}');
      }

      // Log when transitioning between speech/non-speech
      if ((_lastSpeechProb < 0.5 && frameData.isSpeech >= 0.5) ||
          (_lastSpeechProb >= 0.5 && frameData.isSpeech < 0.5)) {
        debugPrint('🔄 VAD: Speech probability transition: ${_lastSpeechProb.toStringAsFixed(3)} → ${frameData.isSpeech.toStringAsFixed(3)}');
      }
      _lastSpeechProb = frameData.isSpeech;
    });

    // Error handling
    _vadHandler.onError.listen((String message) {
      final timestamp = DateTime.now().toIso8601String();
      debugPrint('💥 VAD ERROR at $timestamp: $message');
      debugPrint('💥 VAD: This may indicate microphone access issues or VAD model problems');
      _handleError('VAD Error: $message');
    });

    debugPrint('RealtimeVoiceService: VAD event handlers configured successfully');
  }

  /// Initialize Speech to Text controller
  Future<void> _initializeSpeechToText() async {
    // Check if already initialized
    if (_speechToText.isAvailable) {
      debugPrint('RealtimeVoiceService: Speech to Text already initialized');
      return;
    }

    final available = await _speechToText.initialize(
      onError: (error) => debugPrint('RealtimeVoiceService: Speech to text initialization error: $error'),
      onStatus: (status) => debugPrint('RealtimeVoiceService: Speech to text initialization status: $status'),
    );

    if (!available) {
      throw Exception('Speech to Text not available');
    }
    debugPrint('RealtimeVoiceService: Speech to Text initialized successfully');
  }

  /// Request necessary permissions
  Future<bool> _requestPermissions() async {
    // First check if permission is already granted using permission_handler
    final currentStatus = await Permission.microphone.status;
    debugPrint('RealtimeVoiceService: Current microphone permission status: $currentStatus');

    if (currentStatus == PermissionStatus.granted) {
      debugPrint('RealtimeVoiceService: Microphone permission already granted via permission_handler');
      return true;
    }

    // Try using speech_to_text permission system as fallback
    try {
      final speechAvailable = await _speechToText.initialize(
        onError: (error) => debugPrint('RealtimeVoiceService: Speech to text error: $error'),
        onStatus: (status) => debugPrint('RealtimeVoiceService: Speech to text status: $status'),
      );

      if (speechAvailable) {
        debugPrint('RealtimeVoiceService: Speech to text initialized successfully - permissions OK');
        return true;
      }
    } catch (e) {
      debugPrint('RealtimeVoiceService: Speech to text initialization failed: $e');
    }

    // If speech_to_text fails, try permission_handler request
    final microphoneStatus = await Permission.microphone.request();
    debugPrint('RealtimeVoiceService: Microphone permission request result: $microphoneStatus');

    if (microphoneStatus != PermissionStatus.granted) {
      debugPrint('RealtimeVoiceService: Microphone permission denied - status: $microphoneStatus');
      return false;
    }

    debugPrint('RealtimeVoiceService: Microphone permission granted via permission_handler');
    return true;
  }

  /// Save audio samples to a temporary WAV file
  Future<String> _saveAudioSamplesToFile(List<double> samples) async {
    final tempDir = await getTemporaryDirectory();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final filePath = '${tempDir.path}/voice_$timestamp.wav';
    
    // Convert samples to 16-bit PCM
    final int16Samples = samples.map((sample) => (sample * 32767).round().clamp(-32768, 32767)).toList();
    
    // Create WAV file header
    final header = _createWavHeader(int16Samples.length);
    final audioData = Uint8List.fromList([
      ...header,
      ...int16Samples.expand((sample) => [sample & 0xFF, (sample >> 8) & 0xFF]),
    ]);
    
    final file = File(filePath);
    await file.writeAsBytes(audioData);
    
    _tempAudioPath = filePath;
    return filePath;
  }

  /// Create WAV file header
  List<int> _createWavHeader(int numSamples) {
    const int sampleRate = 16000;
    const int numChannels = 1;
    const int bitsPerSample = 16;
    const int byteRate = sampleRate * numChannels * bitsPerSample ~/ 8;
    const int blockAlign = numChannels * bitsPerSample ~/ 8;
    final int dataSize = numSamples * blockAlign;
    final int fileSize = 36 + dataSize;
    
    return [
      // RIFF header
      0x52, 0x49, 0x46, 0x46, // "RIFF"
      fileSize & 0xFF, (fileSize >> 8) & 0xFF, (fileSize >> 16) & 0xFF, (fileSize >> 24) & 0xFF,
      0x57, 0x41, 0x56, 0x45, // "WAVE"
      
      // fmt chunk
      0x66, 0x6D, 0x74, 0x20, // "fmt "
      16, 0, 0, 0, // chunk size
      1, 0, // audio format (PCM)
      numChannels, 0, // num channels
      sampleRate & 0xFF, (sampleRate >> 8) & 0xFF, (sampleRate >> 16) & 0xFF, (sampleRate >> 24) & 0xFF,
      byteRate & 0xFF, (byteRate >> 8) & 0xFF, (byteRate >> 16) & 0xFF, (byteRate >> 24) & 0xFF,
      blockAlign, 0, // block align
      bitsPerSample, 0, // bits per sample
      
      // data chunk
      0x64, 0x61, 0x74, 0x61, // "data"
      dataSize & 0xFF, (dataSize >> 8) & 0xFF, (dataSize >> 16) & 0xFF, (dataSize >> 24) & 0xFF,
    ];
  }

  /// Clean up temporary audio file
  Future<void> _cleanupTempFile(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await file.delete();
      }
    } catch (e) {
      debugPrint('RealtimeVoiceService: Failed to cleanup temp file: $e');
    }
  }

  /// Set the current state and notify listeners
  void _setState(VoiceState newState) {
    if (_state != newState) {
      _state = newState;
      _stateController.add(_state);
      debugPrint('RealtimeVoiceService: State changed to $newState');
    }
  }

  /// Handle errors
  void _handleError(String message) {
    debugPrint('RealtimeVoiceService: Error - $message');
    _errorController.add(message);
    _setState(VoiceState.error);
  }

  /// Dispose of all resources
  Future<void> dispose() async {
    try {
      if (_state == VoiceState.listening) {
        await stopListening();
      }
      
      await _vadHandler.dispose();
      _recorderController?.dispose();
      
      // Clean up temp files
      if (_tempAudioPath != null) {
        await _cleanupTempFile(_tempAudioPath!);
      }
      
      // Close stream controllers
      await _stateController.close();
      await _transcriptionController.close();
      await _errorController.close();
      await _vadEventsController.close();
      
      _isInitialized = false;
      debugPrint('RealtimeVoiceService: Disposed');
    } catch (e) {
      debugPrint('RealtimeVoiceService: Error during disposal: $e');
    }
  }
}
