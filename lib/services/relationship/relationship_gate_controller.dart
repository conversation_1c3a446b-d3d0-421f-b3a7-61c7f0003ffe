import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/shop/shop_item_model.dart';
import '../../models/shop/shop_preview_models.dart';
import '../../models/user/user_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/companion/companion_reaction_service.dart';

/// Relationship Gate Controller
/// Manages access to cosmetics based on user-companion relationship level
class RelationshipGateController {
  final Ref _ref;

  RelationshipGateController(this._ref);

  /// Filter items based on relationship level
  ({List<ShopItemModel> available, List<ShopItemModel> locked}) filterItemsByRelationship(
    List<ShopItemModel> items, 
    int relationshipLevel
  ) {
    final available = <ShopItemModel>[];
    final locked = <ShopItemModel>[];

    for (final item in items) {
      if (isItemUnlocked(item, relationshipLevel)) {
        available.add(item);
      } else {
        locked.add(item);
      }
    }

    return (available: available, locked: locked);
  }

  /// Check if an item is unlocked for the current relationship level
  bool isItemUnlocked(ShopItemModel item, int relationshipLevel) {
    final requiredLevel = getRequiredRelationshipLevel(item);
    return relationshipLevel >= requiredLevel;
  }

  /// Get the required relationship level for an item
  int getRequiredRelationshipLevel(ShopItemModel item) {
    // Check item metadata for relationship requirement
    if (item.metadata.containsKey('relationshipRequired')) {
      return item.metadata['relationshipRequired'] as int;
    }

    // Default relationship requirements based on item type and rarity
    return _getDefaultRelationshipRequirement(item);
  }

  /// Get unlock requirement description for an item
  String getUnlockRequirement(ShopItemModel item) {
    final requiredLevel = getRequiredRelationshipLevel(item);
    final relationshipLevel = RelationshipLevel.fromLevel(requiredLevel);
    
    return 'Requires ${relationshipLevel.displayName} relationship level';
  }

  /// Get current user relationship level
  int getCurrentRelationshipLevel() {
    final user = _ref.read(currentUserProvider);
    if (user == null) return 1;

    // For now, use user level as relationship level
    // This should be replaced with actual relationship tracking
    return _calculateRelationshipLevel(user);
  }

  /// Generate companion dialogue for locked item
  Future<String> generateLockedItemDialogue(ShopItemModel item) async {
    // This will be generated through websocket to backend as specified
    final user = _ref.read(currentUserProvider);
    final personality = user?.selectedPersonality ?? CompanionPersonality.caringFriend;
    final requiredLevel = getRequiredRelationshipLevel(item);
    final currentLevel = getCurrentRelationshipLevel();
    
    // For now, return a placeholder that would be replaced by websocket response
    return _getPlaceholderLockedDialogue(personality, item, requiredLevel, currentLevel);
  }

  /// Check if user can preview locked items
  bool canPreviewLockedItems(int relationshipLevel) {
    // Allow preview of items up to 1 level above current relationship
    return true; // Most locked items can be previewed
  }

  /// Get relationship progress towards next level
  double getRelationshipProgress() {
    final user = _ref.read(currentUserProvider);
    if (user == null) return 0.0;

    final currentLevel = getCurrentRelationshipLevel();
    final nextLevelThreshold = _getRelationshipThreshold(currentLevel + 1);
    final currentLevelThreshold = _getRelationshipThreshold(currentLevel);
    
    final userProgress = _getUserRelationshipPoints(user);
    
    if (nextLevelThreshold == currentLevelThreshold) return 1.0;
    
    final progress = (userProgress - currentLevelThreshold) / 
                    (nextLevelThreshold - currentLevelThreshold);
    
    return progress.clamp(0.0, 1.0);
  }

  /// Get items that will unlock at the next relationship level
  List<ShopItemModel> getItemsUnlockingAtNextLevel(List<ShopItemModel> allItems) {
    final currentLevel = getCurrentRelationshipLevel();
    final nextLevel = currentLevel + 1;
    
    return allItems.where((item) {
      final requiredLevel = getRequiredRelationshipLevel(item);
      return requiredLevel == nextLevel;
    }).toList();
  }

  /// Get relationship level display info
  RelationshipLevelInfo getRelationshipLevelInfo(int level) {
    final relationshipLevel = RelationshipLevel.fromLevel(level);
    
    return RelationshipLevelInfo(
      level: level,
      name: relationshipLevel.displayName,
      description: _getRelationshipLevelDescription(relationshipLevel),
      unlockedFeatures: _getUnlockedFeatures(level),
      nextLevelRequirement: _getNextLevelRequirement(level),
    );
  }

  // Private helper methods

  int _getDefaultRelationshipRequirement(ShopItemModel item) {
    // Base requirements on item type and rarity
    switch (item.type) {
      case ShopItemType.environment:
        switch (item.rarity) {
          case ShopItemRarity.common:
            return 1; // Acquaintance
          case ShopItemRarity.rare:
            return 2; // Friend
          case ShopItemRarity.epic:
            return 3; // Close Friend
          case ShopItemRarity.legendary:
            return 4; // Intimate
        }
      case ShopItemType.outfit:
        switch (item.rarity) {
          case ShopItemRarity.common:
            return 1; // Acquaintance
          case ShopItemRarity.rare:
            return 2; // Friend
          case ShopItemRarity.epic:
            return 3; // Close Friend
          case ShopItemRarity.legendary:
            return 4; // Intimate
        }
      case ShopItemType.accessory:
        switch (item.rarity) {
          case ShopItemRarity.common:
            return 1; // Acquaintance
          case ShopItemRarity.rare:
            return 2; // Friend
          case ShopItemRarity.epic:
            return 3; // Close Friend
          case ShopItemRarity.legendary:
            return 4; // Intimate
        }
      default:
        return 1;
    }
  }

  int _calculateRelationshipLevel(UserModel user) {
    // Calculate relationship level based on user interactions
    // This is a simplified calculation - should be enhanced with actual relationship metrics
    
    final points = _getUserRelationshipPoints(user);
    
    if (points >= 1000) return 4; // Intimate
    if (points >= 500) return 3;  // Close Friend
    if (points >= 100) return 2;  // Friend
    return 1; // Acquaintance
  }

  int _getUserRelationshipPoints(UserModel user) {
    // Calculate relationship points based on user activity
    // This should be replaced with actual relationship tracking
    
    int points = 0;
    
    // Points from messages
    points += user.progress.messagesCount * 2;
    
    // Points from voice messages (more intimate)
    points += user.progress.voiceMessagesCount * 5;
    
    // Points from time spent
    points += (user.progress.totalTimeSpent / 60).round(); // 1 point per hour
    
    // Points from purchases (showing care)
    points += user.ownedEnvironments.length * 10;
    points += user.ownedOutfits.length * 15;
    
    return points;
  }

  int _getRelationshipThreshold(int level) {
    switch (level) {
      case 1: return 0;    // Acquaintance
      case 2: return 100;  // Friend
      case 3: return 500;  // Close Friend
      case 4: return 1000; // Intimate
      default: return 1000;
    }
  }

  String _getPlaceholderLockedDialogue(
    CompanionPersonality personality,
    ShopItemModel item,
    int requiredLevel,
    int currentLevel,
  ) {
    final relationshipLevel = RelationshipLevel.fromLevel(requiredLevel);
    
    final levelName = relationshipLevel.displayName;
    switch (personality) {
      case CompanionPersonality.caringFriend:
        return "This ${item.name.toLowerCase()} looks beautiful, but I think we should get to know each other better first. Maybe when we reach $levelName level?";
      case CompanionPersonality.playfulCompanion:
        return "Ooh, this ${item.name.toLowerCase()} looks fun! But I think we need to be ${levelName.toLowerCase()} first. Let's spend more time together!";
      case CompanionPersonality.wiseMentor:
        return "This ${item.name.toLowerCase()} requires a deeper connection. Patience - when we reach $levelName level, it will be worth the wait.";
      case CompanionPersonality.romanticPartner:
        return "This ${item.name.toLowerCase()} is quite intimate, darling. Perhaps when our hearts are closer and we're ${levelName.toLowerCase()}?";
      case CompanionPersonality.supportiveTherapist:
        return "I appreciate your interest in this ${item.name.toLowerCase()}, but trust develops naturally. When we reach $levelName level, we can explore this together.";
    }
  }

  String _getRelationshipLevelDescription(RelationshipLevel level) {
    switch (level) {
      case RelationshipLevel.acquaintance:
        return "Getting to know each other through basic conversation and general topics.";
      case RelationshipLevel.friend:
        return "Comfortable with personal topics and mild flirtation.";
      case RelationshipLevel.closeFriend:
        return "Deep emotional connection with intimate conversation and support.";
      case RelationshipLevel.intimate:
        return "Closest bond with full trust and intimate content access.";
    }
  }

  List<String> _getUnlockedFeatures(int level) {
    switch (level) {
      case 1:
        return ["Basic cosmetics", "Common environments", "General conversation"];
      case 2:
        return ["Rare cosmetics", "Personal topics", "Mild flirtation", "Special outfits"];
      case 3:
        return ["Epic cosmetics", "Intimate conversation", "Emotional support", "Exclusive accessories"];
      case 4:
        return ["Legendary cosmetics", "Adult content", "Full intimacy", "Premium features"];
      default:
        return [];
    }
  }

  String _getNextLevelRequirement(int currentLevel) {
    if (currentLevel >= 4) return "Maximum level reached";
    
    final nextThreshold = _getRelationshipThreshold(currentLevel + 1);
    final user = _ref.read(currentUserProvider);
    final currentPoints = user != null ? _getUserRelationshipPoints(user) : 0;
    final pointsNeeded = nextThreshold - currentPoints;
    
    if (pointsNeeded <= 0) return "Ready to advance!";
    
    return "Need $pointsNeeded more relationship points";
  }
}

/// Relationship Level Information
class RelationshipLevelInfo {
  final int level;
  final String name;
  final String description;
  final List<String> unlockedFeatures;
  final String nextLevelRequirement;

  const RelationshipLevelInfo({
    required this.level,
    required this.name,
    required this.description,
    required this.unlockedFeatures,
    required this.nextLevelRequirement,
  });
}

/// Relationship Gate State Notifier
class RelationshipGateNotifier extends StateNotifier<RelationshipGateState> {
  final RelationshipGateController _controller;

  RelationshipGateNotifier(this._controller) : super(const RelationshipGateState()) {
    _initializeState();
  }

  void _initializeState() {
    final currentLevel = _controller.getCurrentRelationshipLevel();
    final progress = _controller.getRelationshipProgress();
    final levelInfo = _controller.getRelationshipLevelInfo(currentLevel);
    
    state = state.copyWith(
      currentLevel: currentLevel,
      progress: progress,
      levelInfo: levelInfo,
    );
  }

  /// Filter items by relationship level
  ({List<ShopItemModel> available, List<ShopItemModel> locked}) filterItems(
    List<ShopItemModel> items
  ) {
    return _controller.filterItemsByRelationship(items, state.currentLevel);
  }

  /// Check if item is unlocked
  bool isItemUnlocked(ShopItemModel item) {
    return _controller.isItemUnlocked(item, state.currentLevel);
  }

  /// Generate locked item dialogue
  Future<String> generateLockedItemDialogue(ShopItemModel item) async {
    final dialogue = await _controller.generateLockedItemDialogue(item);
    
    state = state.copyWith(lastLockedDialogue: dialogue);
    
    return dialogue;
  }

  /// Refresh relationship state
  void refreshRelationshipState() {
    _initializeState();
  }

  /// Update relationship level (called when relationship progresses)
  void updateRelationshipLevel(int newLevel) {
    final progress = _controller.getRelationshipProgress();
    final levelInfo = _controller.getRelationshipLevelInfo(newLevel);
    
    state = state.copyWith(
      currentLevel: newLevel,
      progress: progress,
      levelInfo: levelInfo,
    );
  }
}

/// Relationship Gate State
class RelationshipGateState {
  final int currentLevel;
  final double progress;
  final RelationshipLevelInfo? levelInfo;
  final String? lastLockedDialogue;

  const RelationshipGateState({
    this.currentLevel = 1,
    this.progress = 0.0,
    this.levelInfo,
    this.lastLockedDialogue,
  });

  RelationshipGateState copyWith({
    int? currentLevel,
    double? progress,
    RelationshipLevelInfo? levelInfo,
    String? lastLockedDialogue,
  }) {
    return RelationshipGateState(
      currentLevel: currentLevel ?? this.currentLevel,
      progress: progress ?? this.progress,
      levelInfo: levelInfo ?? this.levelInfo,
      lastLockedDialogue: lastLockedDialogue ?? this.lastLockedDialogue,
    );
  }
}

// Providers
final relationshipGateControllerProvider = Provider<RelationshipGateController>((ref) {
  return RelationshipGateController(ref);
});

final relationshipGateProvider = StateNotifierProvider<RelationshipGateNotifier, RelationshipGateState>((ref) {
  final controller = ref.read(relationshipGateControllerProvider);
  return RelationshipGateNotifier(controller);
});

// Convenience providers
final currentRelationshipLevelProvider = Provider<int>((ref) {
  return ref.watch(relationshipGateProvider).currentLevel;
});

final relationshipProgressProvider = Provider<double>((ref) {
  return ref.watch(relationshipGateProvider).progress;
});

final relationshipLevelInfoProvider = Provider<RelationshipLevelInfo?>((ref) {
  return ref.watch(relationshipGateProvider).levelInfo;
});

final canAccessIntimateContentProvider = Provider<bool>((ref) {
  return ref.watch(currentRelationshipLevelProvider) >= 4;
});

final itemsUnlockingNextLevelProvider = Provider.family<List<ShopItemModel>, List<ShopItemModel>>((ref, allItems) {
  final controller = ref.read(relationshipGateControllerProvider);
  return controller.getItemsUnlockingAtNextLevel(allItems);
});