import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/shop/shop_item_model.dart';
import '../../models/shop/shop_preview_models.dart';
import '../../providers/unity_provider.dart';
import '../../providers/auth_provider.dart';

/// Cosmetic Preview Manager
/// Handles real-time cosmetic application and asset management for preview mode
class CosmeticPreviewManager {
  final Ref _ref;
  final Map<String, dynamic> _cosmeticCache = {};
  final Map<ShopItemType, String> _previewState = {};
  final Map<ShopItemType, String> _equippedState = {};

  CosmeticPreviewManager(this._ref) {
    _initializeEquippedState();
  }

  /// Initialize equipped state from user data
  void _initializeEquippedState() {
    final user = _ref.read(currentUserProvider);
    if (user != null) {
      _equippedState[ShopItemType.environment] = user.selectedEnvironment;
      // Add other equipped cosmetics based on user model
      // This would be expanded based on your user model structure
    }
  }

  /// Apply a cosmetic item in Unity for preview
  Future<void> applyCosmetic(ShopItemModel item) async {
    try {
      final unityController = _ref.read(unityControllerProvider.notifier);
      
      // Store previous state for this cosmetic type
      _previewState[item.type] = item.id;

      // Apply cosmetic based on type
      switch (item.type) {
        case ShopItemType.environment:
          await _applyEnvironment(item, unityController);
          break;
        case ShopItemType.outfit:
          await _applyOutfit(item, unityController);
          break;
        case ShopItemType.accessory:
          await _applyAccessory(item, unityController);
          break;
        default:
          debugPrint('Unsupported cosmetic type: ${item.type}');
      }

      // Cache the applied cosmetic
      _cosmeticCache[item.id] = {
        'item': item,
        'appliedAt': DateTime.now(),
        'type': item.type.name,
      };

      debugPrint('Applied cosmetic: ${item.name} (${item.id})');
    } catch (e) {
      debugPrint('Error applying cosmetic ${item.id}: $e');
      rethrow;
    }
  }

  /// Revert to equipped cosmetics
  Future<void> revertToEquipped() async {
    try {
      final unityController = _ref.read(unityControllerProvider.notifier);

      // Revert each cosmetic type to equipped state
      for (final entry in _equippedState.entries) {
        final type = entry.key;
        final equippedId = entry.value;

        switch (type) {
          case ShopItemType.environment:
            unityController.changeEnvironment(equippedId);
            break;
          case ShopItemType.outfit:
            unityController.changeOutfit(equippedId);
            break;
          case ShopItemType.accessory:
            unityController.changeAccessory(equippedId);
            break;
          default:
            break;
        }
      }

      // Clear preview state
      _previewState.clear();
      
      debugPrint('Reverted to equipped cosmetics');
    } catch (e) {
      debugPrint('Error reverting cosmetics: $e');
      rethrow;
    }
  }

  /// Preload cosmetic assets for smooth switching
  Future<void> preloadCosmeticAssets(List<ShopItemModel> items) async {
    try {
      final unityController = _ref.read(unityControllerProvider.notifier);
      
      // Group items by type for efficient preloading
      final itemsByType = <ShopItemType, List<ShopItemModel>>{};
      for (final item in items) {
        itemsByType.putIfAbsent(item.type, () => []).add(item);
      }

      // Preload assets for each type
      for (final entry in itemsByType.entries) {
        final type = entry.key;
        final typeItems = entry.value;
        
        await _preloadAssetsByType(type, typeItems, unityController);
      }

      debugPrint('Preloaded ${items.length} cosmetic assets');
    } catch (e) {
      debugPrint('Error preloading cosmetic assets: $e');
    }
  }

  /// Get current preview state
  Map<ShopItemType, String> get currentPreviewState => Map.from(_previewState);

  /// Get equipped state
  Map<ShopItemType, String> get equippedState => Map.from(_equippedState);

  /// Check if a cosmetic is currently applied
  bool isCosmeticApplied(String cosmeticId) {
    return _previewState.values.contains(cosmeticId);
  }

  /// Get applied cosmetic for a specific type
  String? getAppliedCosmetic(ShopItemType type) {
    return _previewState[type];
  }

  /// Update equipped state (called after successful purchase)
  void updateEquippedState(ShopItemModel item) {
    _equippedState[item.type] = item.id;
    debugPrint('Updated equipped state: ${item.type.name} -> ${item.id}');
  }

  /// Clear cosmetic cache
  void clearCache() {
    _cosmeticCache.clear();
    debugPrint('Cleared cosmetic cache');
  }

  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    return {
      'totalCached': _cosmeticCache.length,
      'cacheSize': _cosmeticCache.toString().length,
      'lastCleared': DateTime.now(),
    };
  }

  // Private helper methods

  Future<void> _applyEnvironment(ShopItemModel item, UnityNotifier unityController) async {
    unityController.changeEnvironment(item.id);
    
    // Apply any environment-specific settings
    if (item.metadata.containsKey('lighting')) {
      unityController.sendCommand('setLighting', {
        'preset': item.metadata['lighting'],
      });
    }
    
    if (item.metadata.containsKey('ambientSound')) {
      unityController.sendCommand('setAmbientSound', {
        'sound': item.metadata['ambientSound'],
      });
    }
  }

  Future<void> _applyOutfit(ShopItemModel item, UnityNotifier unityController) async {
    unityController.changeOutfit(item.id);
    
    // Apply outfit-specific properties
    if (item.metadata.containsKey('material')) {
      unityController.sendCommand('setOutfitMaterial', {
        'material': item.metadata['material'],
      });
    }
    
    if (item.metadata.containsKey('physics')) {
      unityController.sendCommand('setClothPhysics', {
        'enabled': item.metadata['physics'],
      });
    }
  }

  Future<void> _applyAccessory(ShopItemModel item, UnityNotifier unityController) async {
    final slot = item.metadata['slot'] as String? ?? 'default';
    unityController.changeAccessory(item.id, slot: slot);
    
    // Apply accessory-specific properties
    if (item.metadata.containsKey('attachment')) {
      unityController.sendCommand('setAccessoryAttachment', {
        'accessory': item.id,
        'attachment': item.metadata['attachment'],
      });
    }
  }

  Future<void> _preloadAssetsByType(
    ShopItemType type, 
    List<ShopItemModel> items, 
    UnityNotifier unityController
  ) async {
    final itemIds = items.map((item) => item.id).toList();
    
    switch (type) {
      case ShopItemType.environment:
        unityController.sendCommand('preloadEnvironments', {
          'environments': itemIds,
        });
        break;
      case ShopItemType.outfit:
        unityController.sendCommand('preloadOutfits', {
          'outfits': itemIds,
        });
        break;
      case ShopItemType.accessory:
        unityController.sendCommand('preloadAccessories', {
          'accessories': itemIds,
        });
        break;
      default:
        break;
    }
    
    // Wait for preloading to complete
    await Future.delayed(const Duration(milliseconds: 100));
  }
}

/// Cosmetic Application State Notifier
class CosmeticApplicationNotifier extends StateNotifier<CosmeticApplicationState> {
  final CosmeticPreviewManager _manager;

  CosmeticApplicationNotifier(this._manager) : super(const CosmeticApplicationState());

  /// Apply a cosmetic and update state
  Future<void> applyCosmetic(ShopItemModel item) async {
    state = state.copyWith(
      isApplying: true,
      currentlyApplying: item.id,
    );

    try {
      await _manager.applyCosmetic(item);
      
      final updatedApplied = Map<ShopItemType, String>.from(state.appliedCosmetics);
      updatedApplied[item.type] = item.id;
      
      state = state.copyWith(
        appliedCosmetics: updatedApplied,
        isApplying: false,
        currentlyApplying: null,
      );
    } catch (e) {
      state = state.copyWith(
        isApplying: false,
        currentlyApplying: null,
      );
      rethrow;
    }
  }

  /// Revert to equipped cosmetics
  Future<void> revertToEquipped() async {
    state = state.copyWith(isApplying: true);

    try {
      await _manager.revertToEquipped();
      
      state = state.copyWith(
        appliedCosmetics: Map.from(_manager.equippedState),
        isApplying: false,
      );
    } catch (e) {
      state = state.copyWith(isApplying: false);
      rethrow;
    }
  }

  /// Initialize with equipped cosmetics
  void initializeWithEquipped(Map<ShopItemType, String> equipped) {
    state = state.copyWith(
      equippedCosmetics: equipped,
      appliedCosmetics: Map.from(equipped),
    );
  }

  /// Update equipped state after purchase
  void updateEquippedCosmetic(ShopItemModel item) {
    _manager.updateEquippedState(item);
    
    final updatedEquipped = Map<ShopItemType, String>.from(state.equippedCosmetics);
    updatedEquipped[item.type] = item.id;
    
    state = state.copyWith(equippedCosmetics: updatedEquipped);
  }

  /// Check if cosmetic is currently applied
  bool isCosmeticApplied(String cosmeticId) {
    return state.appliedCosmetics.values.contains(cosmeticId);
  }

  /// Get applied cosmetic for type
  String? getAppliedCosmetic(ShopItemType type) {
    return state.appliedCosmetics[type];
  }
}

// Providers
final cosmeticPreviewManagerProvider = Provider<CosmeticPreviewManager>((ref) {
  return CosmeticPreviewManager(ref);
});

final cosmeticApplicationProvider = StateNotifierProvider<CosmeticApplicationNotifier, CosmeticApplicationState>((ref) {
  final manager = ref.read(cosmeticPreviewManagerProvider);
  return CosmeticApplicationNotifier(manager);
});

// Convenience providers
final isApplyingCosmeticProvider = Provider<bool>((ref) {
  return ref.watch(cosmeticApplicationProvider).isApplying;
});

final currentlyApplyingCosmeticProvider = Provider<String?>((ref) {
  return ref.watch(cosmeticApplicationProvider).currentlyApplying;
});

final appliedCosmeticsProvider = Provider<Map<ShopItemType, String>>((ref) {
  return ref.watch(cosmeticApplicationProvider).appliedCosmetics;
});

final equippedCosmeticsProvider = Provider<Map<ShopItemType, String>>((ref) {
  return ref.watch(cosmeticApplicationProvider).equippedCosmetics;
});