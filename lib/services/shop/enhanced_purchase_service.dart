import 'dart:async';
import 'package:ellahai/models/shop/shop_preview_models.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/shop/shop_item_model.dart';
import '../../models/user/user_model.dart';
import '../../providers/auth_provider.dart';
import '../../providers/shop_provider.dart';
import '../../services/relationship/relationship_gate_controller.dart';
import '../../services/companion/companion_reaction_service.dart';
import '../../services/cosmetic/cosmetic_preview_manager.dart';

/// Enhanced Purchase Service
/// Handles the complete purchase flow with validation, reactions, and state updates
class EnhancedPurchaseService {
  final Ref _ref;

  EnhancedPurchaseService(this._ref);

  /// Validate if a purchase can be made
  PurchaseValidationResult validatePurchase(ShopItemModel item) {
    final user = _ref.read(currentUserProvider);
    if (user == null) {
      return PurchaseValidationResult.failure('User not authenticated');
    }

    // Check if already owned
    if (_isItemOwned(item, user)) {
      return PurchaseValidationResult.failure('Item already owned');
    }

    // Check if user can afford it
    if (user.progress.hearts < item.price) {
      return PurchaseValidationResult.failure('Insufficient hearts');
    }

    // Check relationship requirements
    final relationshipGate = _ref.read(relationshipGateControllerProvider);
    final currentLevel = relationshipGate.getCurrentRelationshipLevel();
    if (!relationshipGate.isItemUnlocked(item, currentLevel)) {
      final requiredLevel = relationshipGate.getRequiredRelationshipLevel(item);
      final relationshipLevel = RelationshipLevel.fromLevel(requiredLevel);
      return PurchaseValidationResult.failure(
        'Requires ${relationshipLevel.displayName} relationship level'
      );
    }

    return PurchaseValidationResult.success();
  }

  /// Execute the complete purchase flow
  Future<PurchaseResult> executePurchase(ShopItemModel item) async {
    try {
      // Validate purchase
      final validation = validatePurchase(item);
      if (!validation.isValid) {
        return PurchaseResult.failure(validation.errorMessage!);
      }

      // Execute purchase through shop provider
      await _ref.read(shopProvider.notifier).purchaseItem(item.id);

      // Update cosmetic state if in preview mode
      final cosmeticManager = _ref.read(cosmeticPreviewManagerProvider);
      cosmeticManager.updateEquippedState(item);

      // Generate companion reaction
      final reactionService = _ref.read(companionReactionServiceProvider);
      final reaction = await reactionService.generatePurchaseSuccessReaction(item);

      // Log purchase analytics
      _logPurchaseAnalytics(item);

      return PurchaseResult.success(reaction);

    } catch (e) {
      debugPrint('Purchase failed: $e');
      
      // Generate failure reaction
      final reactionService = _ref.read(companionReactionServiceProvider);
      final reaction = await reactionService.generatePurchaseDeclineReaction(item);
      
      return PurchaseResult.failure(e.toString(), reaction);
    }
  }

  /// Get purchase summary for confirmation
  PurchaseSummary getPurchaseSummary(ShopItemModel item) {
    final user = _ref.read(currentUserProvider);
    final currentHearts = user?.progress.hearts ?? 0;
    final heartsAfterPurchase = currentHearts - item.price;
    
    return PurchaseSummary(
      item: item,
      currentHearts: currentHearts,
      heartsAfterPurchase: heartsAfterPurchase,
      canAfford: currentHearts >= item.price,
    );
  }

  /// Check if user owns an item
  bool _isItemOwned(ShopItemModel item, UserModel user) {
    switch (item.type) {
      case ShopItemType.environment:
        return user.ownedEnvironments.contains(item.id);
      case ShopItemType.outfit:
      case ShopItemType.accessory:
        return user.ownedOutfits.contains(item.id);
      default:
        return false;
    }
  }

  /// Log purchase analytics
  void _logPurchaseAnalytics(ShopItemModel item) {
    // This would integrate with your analytics service
    debugPrint('Purchase completed: ${item.name} (${item.id}) for ${item.price} hearts');
  }
}

/// Purchase validation result
class PurchaseValidationResult {
  final bool isValid;
  final String? errorMessage;

  const PurchaseValidationResult._(this.isValid, this.errorMessage);

  factory PurchaseValidationResult.success() {
    return const PurchaseValidationResult._(true, null);
  }

  factory PurchaseValidationResult.failure(String message) {
    return PurchaseValidationResult._(false, message);
  }
}

/// Purchase execution result
class PurchaseResult {
  final bool isSuccess;
  final String? errorMessage;
  final dynamic reaction;

  const PurchaseResult._(this.isSuccess, this.errorMessage, this.reaction);

  factory PurchaseResult.success([dynamic reaction]) {
    return PurchaseResult._(true, null, reaction);
  }

  factory PurchaseResult.failure(String message, [dynamic reaction]) {
    return PurchaseResult._(false, message, reaction);
  }
}

/// Purchase summary for confirmation dialogs
class PurchaseSummary {
  final ShopItemModel item;
  final int currentHearts;
  final int heartsAfterPurchase;
  final bool canAfford;

  const PurchaseSummary({
    required this.item,
    required this.currentHearts,
    required this.heartsAfterPurchase,
    required this.canAfford,
  });
}

/// Purchase state notifier for UI feedback
class PurchaseStateNotifier extends StateNotifier<PurchaseState> {
  final EnhancedPurchaseService _service;

  PurchaseStateNotifier(this._service) : super(const PurchaseState());

  /// Start purchase process
  Future<void> startPurchase(ShopItemModel item) async {
    state = state.copyWith(
      isProcessing: true,
      currentItem: item,
      error: null,
    );

    final result = await _service.executePurchase(item);

    if (result.isSuccess) {
      state = state.copyWith(
        isProcessing: false,
        isSuccess: true,
        reaction: result.reaction,
      );
      
      // Clear success state after delay
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          state = const PurchaseState();
        }
      });
    } else {
      state = state.copyWith(
        isProcessing: false,
        error: result.errorMessage,
        reaction: result.reaction,
      );
    }
  }

  /// Clear purchase state
  void clearState() {
    state = const PurchaseState();
  }
}

/// Purchase state for UI feedback
class PurchaseState {
  final bool isProcessing;
  final bool isSuccess;
  final ShopItemModel? currentItem;
  final String? error;
  final dynamic reaction;

  const PurchaseState({
    this.isProcessing = false,
    this.isSuccess = false,
    this.currentItem,
    this.error,
    this.reaction,
  });

  PurchaseState copyWith({
    bool? isProcessing,
    bool? isSuccess,
    ShopItemModel? currentItem,
    String? error,
    dynamic reaction,
  }) {
    return PurchaseState(
      isProcessing: isProcessing ?? this.isProcessing,
      isSuccess: isSuccess ?? this.isSuccess,
      currentItem: currentItem ?? this.currentItem,
      error: error ?? this.error,
      reaction: reaction ?? this.reaction,
    );
  }
}

// Providers
final enhancedPurchaseServiceProvider = Provider<EnhancedPurchaseService>((ref) {
  return EnhancedPurchaseService(ref);
});

final purchaseStateProvider = StateNotifierProvider<PurchaseStateNotifier, PurchaseState>((ref) {
  final service = ref.read(enhancedPurchaseServiceProvider);
  return PurchaseStateNotifier(service);
});

// Convenience providers
final isPurchasingProvider = Provider<bool>((ref) {
  return ref.watch(purchaseStateProvider).isProcessing;
});

final purchaseSuccessProvider = Provider<bool>((ref) {
  return ref.watch(purchaseStateProvider).isSuccess;
});

final purchaseErrorProvider = Provider<String?>((ref) {
  return ref.watch(purchaseStateProvider).error;
});