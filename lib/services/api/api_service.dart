import 'package:dio/dio.dart';
import '../../core/constants/app_constants.dart';

class ApiService {
  late final Dio _dio;

  ApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConstants.baseUrl,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
      sendTimeout: const Duration(seconds: 10),
      headers: {
        'Content-Type': 'application/json',
      },
    ));

    // Add interceptors for logging and error handling
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (obj) => print(obj),
    ));
    
    _dio.interceptors.add(InterceptorsWrapper(
      onError: (error, handler) {
        print('API Error: ${error.message}');
        handler.next(error);
      },
    ));
  }

  // Authentication endpoints
  Future<Map<String, dynamic>> getUser(String userId) async {
    try {
      final response = await _dio.get('/users/$userId');
      return response.data;
    } catch (e) {
      throw ApiException('Failed to get user: $e');
    }
  }

  Future<Map<String, dynamic>> createUser(Map<String, dynamic> userData) async {
    try {
      final response = await _dio.post('/users', data: userData);
      return response.data;
    } catch (e) {
      throw ApiException('Failed to create user: $e');
    }
  }

  // Progress endpoints
  Future<Map<String, dynamic>> getUserProgress(String userId) async {
    try {
      final response = await _dio.get('/users/$userId/progress');
      return response.data;
    } catch (e) {
      // Return default progress if not found or error
      return {
        'xp': 0,
        'level': 1,
        'hearts': 100,
        'messagesCount': 0,
        'voiceMessagesCount': 0,
        'achievements': [],
        'totalTimeSpent': 0,
      };
    }
  }

  Future<void> updateUserProgress(String userId, Map<String, dynamic> progressData) async {
    try {
      await _dio.put(
        '/users/$userId/progress',
        data: progressData,
      );
    } catch (e) {
      // Silently fail, progress will be synced next time
      print('Failed to update user progress: $e');
    }
  }

  Future<Map<String, dynamic>> updateUser(String userId, Map<String, dynamic> userData) async {
    try {
      final response = await _dio.put('/users/$userId', data: userData);
      return response.data;
    } catch (e) {
      throw ApiException('Failed to update user: $e');
    }
  }

  // Chat endpoints
  Future<Map<String, dynamic>> sendMessage(Map<String, dynamic> messageData) async {
    try {
      final response = await _dio.post('/chat/message', data: messageData);
      return response.data;
    } catch (e) {
      throw ApiException('Failed to send message: $e');
    }
  }

  Future<List<Map<String, dynamic>>> getChatHistory(String userId, {int limit = 50}) async {
    try {
      final response = await _dio.get('/chat/history/$userId', queryParameters: {
        'limit': limit,
      });
      return List<Map<String, dynamic>>.from(response.data);
    } catch (e) {
      throw ApiException('Failed to get chat history: $e');
    }
  }

  // Voice endpoints
  Future<String> convertSpeechToText(String audioPath) async {
    try {
      final formData = FormData.fromMap({
        'audio': await MultipartFile.fromFile(audioPath),
      });
      
      final response = await _dio.post('/voice/speech-to-text', data: formData);
      return response.data['text'];
    } catch (e) {
      throw ApiException('Failed to convert speech to text: $e');
    }
  }

  Future<String> convertTextToSpeech(String text, {String? voice}) async {
    try {
      final response = await _dio.post('/voice/text-to-speech', data: {
        'text': text,
        'voice': voice ?? AppConstants.defaultVoice,
      });
      return response.data['audioUrl'];
    } catch (e) {
      throw ApiException('Failed to convert text to speech: $e');
    }
  }

  // Shop endpoints
  Future<List<Map<String, dynamic>>> getShopItems() async {
    try {
      final response = await _dio.get('/shop/items');
      return List<Map<String, dynamic>>.from(response.data);
    } catch (e) {
      throw ApiException('Failed to get shop items: $e');
    }
  }

  Future<Map<String, dynamic>> purchaseItem(String userId, String itemId) async {
    try {
      final response = await _dio.post('/shop/purchase', data: {
        'userId': userId,
        'itemId': itemId,
      });
      return response.data;
    } catch (e) {
      throw ApiException('Failed to purchase item: $e');
    }
  }

  // Progress endpoints
  Future<Map<String, dynamic>> updateProgress(String userId, Map<String, dynamic> progressData) async {
    try {
      final response = await _dio.post('/progress/update/$userId', data: progressData);
      return response.data;
    } catch (e) {
      throw ApiException('Failed to update progress: $e');
    }
  }

  Future<Map<String, dynamic>> claimDailyBonus(String userId) async {
    try {
      final response = await _dio.post('/progress/daily-bonus/$userId');
      return response.data;
    } catch (e) {
      throw ApiException('Failed to claim daily bonus: $e');
    }
  }

  // Companion endpoints
  Future<List<Map<String, dynamic>>> getPersonalities() async {
    try {
      final response = await _dio.get('/companion/personalities');
      return List<Map<String, dynamic>>.from(response.data);
    } catch (e) {
      throw ApiException('Failed to get personalities: $e');
    }
  }

  Future<Map<String, dynamic>> updatePersonality(String userId, String personalityId) async {
    try {
      final response = await _dio.post('/companion/personality', data: {
        'userId': userId,
        'personalityId': personalityId,
      });
      return response.data;
    } catch (e) {
      throw ApiException('Failed to update personality: $e');
    }
  }

  // Environment endpoints
  Future<List<Map<String, dynamic>>> getEnvironments() async {
    try {
      final response = await _dio.get('/environments');
      return List<Map<String, dynamic>>.from(response.data);
    } catch (e) {
      throw ApiException('Failed to get environments: $e');
    }
  }

  Future<Map<String, dynamic>> updateEnvironment(String userId, String environmentId) async {
    try {
      final response = await _dio.post('/environments/select', data: {
        'userId': userId,
        'environmentId': environmentId,
      });
      return response.data;
    } catch (e) {
      throw ApiException('Failed to update environment: $e');
    }
  }

  // Health check
  Future<bool> checkHealth() async {
    try {
      final response = await _dio.get('/health');
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}

class ApiException implements Exception {
  final String message;
  ApiException(this.message);
  
  @override
  String toString() => 'ApiException: $message';
}
