import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/shop/shop_item_model.dart';
import '../../models/shop/shop_preview_models.dart';
import '../../providers/unity_provider.dart';
import '../../providers/shop_preview_provider.dart';

/// Preview Error Handler
/// Handles errors and provides fallback systems for the preview functionality
class PreviewErrorHandler {
  final Ref _ref;
  final List<PreviewError> _errorHistory = [];
  Timer? _retryTimer;

  PreviewErrorHandler(this._ref);

  /// Handle Unity connection errors
  Future<void> handleUnityConnectionError(String error) async {
    final previewError = PreviewError(
      type: PreviewErrorType.unityConnection,
      message: error,
      timestamp: DateTime.now(),
    );
    
    _logError(previewError);

    // Try to reconnect Unity
    await _attemptUnityReconnection();
  }

  /// Handle asset loading errors
  Future<void> handleAssetLoadingError(String assetId, String error) async {
    final previewError = PreviewError(
      type: PreviewErrorType.assetLoading,
      message: 'Failed to load asset $assetId: $error',
      timestamp: DateTime.now(),
      metadata: {'assetId': assetId},
    );
    
    _logError(previewError);

    // Fallback to placeholder asset
    await _loadPlaceholderAsset(assetId);
  }

  /// Handle camera transition errors
  Future<void> handleCameraTransitionError(String error) async {
    final previewError = PreviewError(
      type: PreviewErrorType.cameraTransition,
      message: error,
      timestamp: DateTime.now(),
    );
    
    _logError(previewError);

    // Reset camera to default position
    await _resetCameraToDefault();
  }

  /// Handle purchase errors
  Future<void> handlePurchaseError(ShopItemModel item, String error) async {
    final previewError = PreviewError(
      type: PreviewErrorType.purchase,
      message: 'Purchase failed for ${item.name}: $error',
      timestamp: DateTime.now(),
      metadata: {'itemId': item.id, 'itemName': item.name},
    );
    
    _logError(previewError);

    // Show user-friendly error message
    _showPurchaseErrorFeedback(item, error);
  }

  /// Handle relationship sync errors
  Future<void> handleRelationshipSyncError(String error) async {
    final previewError = PreviewError(
      type: PreviewErrorType.relationshipSync,
      message: error,
      timestamp: DateTime.now(),
    );
    
    _logError(previewError);

    // Use cached relationship data
    _useCachedRelationshipData();
  }

  /// Handle network errors
  Future<void> handleNetworkError(String operation, String error) async {
    final previewError = PreviewError(
      type: PreviewErrorType.network,
      message: 'Network error during $operation: $error',
      timestamp: DateTime.now(),
      metadata: {'operation': operation},
    );
    
    _logError(previewError);

    // Enable offline mode for preview
    _enableOfflinePreviewMode();
  }

  /// Get error recovery suggestions
  List<ErrorRecoverySuggestion> getRecoverySuggestions(PreviewErrorType errorType) {
    switch (errorType) {
      case PreviewErrorType.unityConnection:
        return [
          ErrorRecoverySuggestion(
            title: 'Restart Preview',
            description: 'Exit and re-enter preview mode',
            action: _restartPreviewMode,
          ),
          ErrorRecoverySuggestion(
            title: 'Use Static Preview',
            description: 'View items without 3D preview',
            action: _enableStaticPreviewMode,
          ),
        ];
      
      case PreviewErrorType.assetLoading:
        return [
          ErrorRecoverySuggestion(
            title: 'Retry Loading',
            description: 'Attempt to reload the asset',
            action: _retryAssetLoading,
          ),
          ErrorRecoverySuggestion(
            title: 'Use Placeholder',
            description: 'Show placeholder image instead',
            action: _useAssetPlaceholder,
          ),
        ];
      
      case PreviewErrorType.cameraTransition:
        return [
          ErrorRecoverySuggestion(
            title: 'Reset Camera',
            description: 'Return camera to default position',
            action: _resetCameraToDefault,
          ),
        ];
      
      case PreviewErrorType.purchase:
        return [
          ErrorRecoverySuggestion(
            title: 'Retry Purchase',
            description: 'Attempt the purchase again',
            action: _retryLastPurchase,
          ),
          ErrorRecoverySuggestion(
            title: 'Check Balance',
            description: 'Verify you have enough hearts',
            action: _showBalanceInfo,
          ),
        ];
      
      case PreviewErrorType.relationshipSync:
        return [
          ErrorRecoverySuggestion(
            title: 'Refresh Data',
            description: 'Reload relationship information',
            action: _refreshRelationshipData,
          ),
        ];
      
      case PreviewErrorType.network:
        return [
          ErrorRecoverySuggestion(
            title: 'Retry Connection',
            description: 'Attempt to reconnect',
            action: _retryNetworkConnection,
          ),
          ErrorRecoverySuggestion(
            title: 'Offline Mode',
            description: 'Continue with cached data',
            action: _enableOfflinePreviewMode,
          ),
        ];
    }
  }

  /// Check if system is in a recoverable state
  bool isRecoverable() {
    final recentErrors = _errorHistory
        .where((error) => DateTime.now().difference(error.timestamp).inMinutes < 5)
        .length;
    
    return recentErrors < 3; // Allow recovery if less than 3 errors in 5 minutes
  }

  /// Get system health status
  PreviewSystemHealth getSystemHealth() {
    final recentErrors = _errorHistory
        .where((error) => DateTime.now().difference(error.timestamp).inMinutes < 10)
        .toList();

    if (recentErrors.isEmpty) {
      return PreviewSystemHealth.healthy;
    } else if (recentErrors.length < 3) {
      return PreviewSystemHealth.degraded;
    } else {
      return PreviewSystemHealth.critical;
    }
  }

  // Private helper methods

  void _logError(PreviewError error) {
    _errorHistory.add(error);
    
    // Keep only last 50 errors
    if (_errorHistory.length > 50) {
      _errorHistory.removeAt(0);
    }

    debugPrint('Preview Error: ${error.type.name} - ${error.message}');
  }

  Future<void> _attemptUnityReconnection() async {
    try {
      final unityController = _ref.read(unityControllerProvider.notifier);
      unityController.resetAvatar();
      
      // Wait for reconnection
      await Future.delayed(const Duration(seconds: 2));
      
      // Verify connection
      final isInitialized = _ref.read(unityInitializedProvider);
      if (!isInitialized) {
        throw Exception('Unity reconnection failed');
      }
      
    } catch (e) {
      debugPrint('Unity reconnection failed: $e');
      _enableStaticPreviewMode();
    }
  }

  Future<void> _loadPlaceholderAsset(String assetId) async {
    // This would load a placeholder asset
    debugPrint('Loading placeholder for asset: $assetId');
  }

  Future<void> _resetCameraToDefault() async {
    try {
      final unityController = _ref.read(unityControllerProvider.notifier);
      unityController.resetCamera();
    } catch (e) {
      debugPrint('Failed to reset camera: $e');
    }
  }

  void _showPurchaseErrorFeedback(ShopItemModel item, String error) {
    // This would show user-friendly error feedback
    debugPrint('Purchase error feedback for ${item.name}: $error');
  }

  void _useCachedRelationshipData() {
    // Use locally cached relationship data
    debugPrint('Using cached relationship data');
  }

  void _enableOfflinePreviewMode() {
    // Enable offline mode with cached assets
    debugPrint('Enabling offline preview mode');
  }

  Future<void> _restartPreviewMode() async {
    final previewNotifier = _ref.read(shopPreviewProvider.notifier);
    await previewNotifier.exitPreviewMode();
    // The user would need to manually re-enter preview mode
  }

  void _enableStaticPreviewMode() {
    // Enable static image preview mode
    debugPrint('Enabling static preview mode');
  }

  Future<void> _retryAssetLoading() async {
    // Retry loading the last failed asset
    debugPrint('Retrying asset loading');
  }

  void _useAssetPlaceholder() {
    // Use placeholder for failed asset
    debugPrint('Using asset placeholder');
  }

  Future<void> _retryLastPurchase() async {
    // Retry the last failed purchase
    debugPrint('Retrying last purchase');
  }

  void _showBalanceInfo() {
    // Show user's current balance
    debugPrint('Showing balance information');
  }

  Future<void> _refreshRelationshipData() async {
    // Refresh relationship data from server
    debugPrint('Refreshing relationship data');
  }

  Future<void> _retryNetworkConnection() async {
    // Retry network connection
    debugPrint('Retrying network connection');
  }

  void dispose() {
    _retryTimer?.cancel();
  }
}

/// Preview error types
enum PreviewErrorType {
  unityConnection,
  assetLoading,
  cameraTransition,
  purchase,
  relationshipSync,
  network,
}

/// Preview error model
class PreviewError {
  final PreviewErrorType type;
  final String message;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  const PreviewError({
    required this.type,
    required this.message,
    required this.timestamp,
    this.metadata = const {},
  });
}

/// Error recovery suggestion
class ErrorRecoverySuggestion {
  final String title;
  final String description;
  final VoidCallback action;

  const ErrorRecoverySuggestion({
    required this.title,
    required this.description,
    required this.action,
  });
}

/// System health status
enum PreviewSystemHealth {
  healthy,
  degraded,
  critical,
}

/// Error state notifier
class PreviewErrorNotifier extends StateNotifier<PreviewErrorState> {
  final PreviewErrorHandler _handler;

  PreviewErrorNotifier(this._handler) : super(const PreviewErrorState());

  void reportError(PreviewErrorType type, String message, [Map<String, dynamic>? metadata]) {
    final error = PreviewError(
      type: type,
      message: message,
      timestamp: DateTime.now(),
      metadata: metadata ?? {},
    );

    state = state.copyWith(
      currentError: error,
      systemHealth: _handler.getSystemHealth(),
      recoverySuggestions: _handler.getRecoverySuggestions(type),
    );

    // Handle the error
    _handleError(error);
  }

  void clearError() {
    state = state.copyWith(currentError: null, recoverySuggestions: []);
  }

  void _handleError(PreviewError error) {
    switch (error.type) {
      case PreviewErrorType.unityConnection:
        _handler.handleUnityConnectionError(error.message);
        break;
      case PreviewErrorType.assetLoading:
        final assetId = error.metadata['assetId'] as String? ?? 'unknown';
        _handler.handleAssetLoadingError(assetId, error.message);
        break;
      case PreviewErrorType.cameraTransition:
        _handler.handleCameraTransitionError(error.message);
        break;
      case PreviewErrorType.purchase:
        // Would need item reference
        break;
      case PreviewErrorType.relationshipSync:
        _handler.handleRelationshipSyncError(error.message);
        break;
      case PreviewErrorType.network:
        final operation = error.metadata['operation'] as String? ?? 'unknown';
        _handler.handleNetworkError(operation, error.message);
        break;
    }
  }
}

/// Preview error state
class PreviewErrorState {
  final PreviewError? currentError;
  final PreviewSystemHealth systemHealth;
  final List<ErrorRecoverySuggestion> recoverySuggestions;

  const PreviewErrorState({
    this.currentError,
    this.systemHealth = PreviewSystemHealth.healthy,
    this.recoverySuggestions = const [],
  });

  PreviewErrorState copyWith({
    PreviewError? currentError,
    PreviewSystemHealth? systemHealth,
    List<ErrorRecoverySuggestion>? recoverySuggestions,
  }) {
    return PreviewErrorState(
      currentError: currentError ?? this.currentError,
      systemHealth: systemHealth ?? this.systemHealth,
      recoverySuggestions: recoverySuggestions ?? this.recoverySuggestions,
    );
  }
}

// Providers
final previewErrorHandlerProvider = Provider<PreviewErrorHandler>((ref) {
  return PreviewErrorHandler(ref);
});

final previewErrorProvider = StateNotifierProvider<PreviewErrorNotifier, PreviewErrorState>((ref) {
  final handler = ref.read(previewErrorHandlerProvider);
  return PreviewErrorNotifier(handler);
});

// Convenience providers
final hasPreviewErrorProvider = Provider<bool>((ref) {
  return ref.watch(previewErrorProvider).currentError != null;
});

final previewSystemHealthProvider = Provider<PreviewSystemHealth>((ref) {
  return ref.watch(previewErrorProvider).systemHealth;
});

final errorRecoverySuggestionsProvider = Provider<List<ErrorRecoverySuggestion>>((ref) {
  return ref.watch(previewErrorProvider).recoverySuggestions;
});