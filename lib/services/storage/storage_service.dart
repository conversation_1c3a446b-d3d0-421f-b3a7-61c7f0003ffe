import 'package:hive_flutter/hive_flutter.dart';
import '../../core/constants/app_constants.dart';
import '../../models/user/user_model.dart';
import '../../models/chat/message_model.dart';

class StorageService {
  static late Box<UserModel> _userBox;
  static late Box<MessageModel> _chatBox;
  static late Box<dynamic> _settingsBox;
  static late Box<dynamic> _progressBox;

  static Future<void> init() async {
    // Register adapters
    Hive.registerAdapter(UserModelAdapter());
    Hive.registerAdapter(MessageModelAdapter());
    Hive.registerAdapter(MessageTypeAdapter());
    Hive.registerAdapter(UserProgressAdapter());
    Hive.registerAdapter(CompanionPersonalityAdapter());

    // Open boxes
    _userBox = await Hive.openBox<UserModel>(AppConstants.userBox);
    _chatBox = await Hive.openBox<MessageModel>(AppConstants.chatBox);
    _settingsBox = await Hive.openBox<dynamic>(AppConstants.settingsBox);
    _progressBox = await Hive.openBox<dynamic>(AppConstants.progressBox);

    // Perform data migration if needed
    await _performDataMigration();
  }

  static Future<void> _performDataMigration() async {
    try {
      final user = _userBox.get('current_user');
      if (user != null) {
        // Check if the selectedPersonality field has the correct type
        if (user.selectedPersonality is! CompanionPersonality) {
          // Clear corrupted user data - user will need to re-authenticate
          await clearUser();
          print('Cleared corrupted user data due to type mismatch');
        }
      }
    } catch (e) {
      // If there's any error reading user data, clear it
      await clearUser();
      print('Cleared user data due to read error: $e');
    }
  }

  // User Storage
  static Future<void> saveUser(UserModel user) async {
    await _userBox.put('current_user', user);
  }

  static UserModel? getCurrentUser() {
    final user = _userBox.get('current_user');
    if (user != null) {
      // Ensure the user object has proper types by recreating it
      // This fixes any dynamic type issues from previous versions
      return _ensureProperTypes(user);
    }
    return null;
  }

  static UserModel _ensureProperTypes(UserModel user) {
    // Recreate the user object to ensure all fields have proper types
    return UserModel(
      id: user.id,
      name: user.name,
      email: user.email,
      photoURL: user.photoURL,
      createdAt: user.createdAt,
      progress: user.progress,
      selectedPersonality: user.selectedPersonality is CompanionPersonality 
          ? user.selectedPersonality 
          : CompanionPersonality.caringFriend,
      selectedEnvironment: user.selectedEnvironment,
      ownedEnvironments: user.ownedEnvironments,
      ownedOutfits: user.ownedOutfits,
      preferences: user.preferences,
      lastLogin: user.lastLogin,
    );
  }

  static Future<void> clearUser() async {
    await _userBox.delete('current_user');
  }

  // Chat Storage
  static Future<void> saveMessage(MessageModel message) async {
    await _chatBox.add(message);
    
    // Keep only recent messages for performance
    if (_chatBox.length > AppConstants.maxMessagesInChat) {
      final oldestKey = _chatBox.keys.first;
      await _chatBox.delete(oldestKey);
    }
  }

  // Progress Storage
  static Future<List<String>> getAchievements() async {
    final user = getCurrentUser();
    return user?.progress.achievements ?? [];
  }

  static Future<List<Map<String, dynamic>>> getAllConversations() async {
    final messages = _chatBox.values.toList();
    // Group messages by conversationId
    final conversations = <String, List<MessageModel>>{};
    
    for (final message in messages) {
      final convId = message.conversationId;
      if (!conversations.containsKey(convId)) {
        conversations[convId] = [];
      }
      conversations[convId]!.add(message);
    }
    
    // Convert to list of conversation maps
    return conversations.entries.map((entry) {
      return {
        'id': entry.key,
        'messages': entry.value,
        'lastMessage': entry.value.last,
        'unreadCount': entry.value.where((m) => !m.isRead).length,
      };
    }).toList();
  }

  static Future<void> saveAchievements(List<String> achievements) async {
    final user = getCurrentUser();
    if (user != null) {
      final updatedUser = user.copyWith(
        progress: user.progress.copyWith(achievements: achievements),
      );
      await saveUser(updatedUser);
    }
  }

  static Future<void> recordDailyLogin(DateTime date) async {
    final user = getCurrentUser();
    if (user != null) {
      final lastLogin = user.lastLogin ?? date.subtract(const Duration(days: 1));
      
      // Check if it's a new day
      if (date.day != lastLogin.day || 
          date.month != lastLogin.month || 
          date.year != lastLogin.year) {
        
        // Update last login
        final updatedUser = user.copyWith(
          lastLogin: date,
          // Add any daily rewards here if needed
        );
        await saveUser(updatedUser);
      }
    }
  }

  static List<MessageModel> getAllMessages() {
    return _chatBox.values.toList()
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
  }

  static Future<void> clearAllMessages() async {
    await _chatBox.clear();
  }

  // Settings Storage
  static Future<void> saveSetting(String key, dynamic value) async {
    await _settingsBox.put(key, value);
  }

  static T? getSetting<T>(String key, {T? defaultValue}) {
    return _settingsBox.get(key, defaultValue: defaultValue) as T?;
  }

  static bool isDarkMode() {
    return getSetting<bool>(AppConstants.isDarkModeKey, defaultValue: true) ?? true;
  }

  static Future<void> setDarkMode(bool value) async {
    await saveSetting(AppConstants.isDarkModeKey, value);
  }

  static String getSelectedEnvironment() {
    return getSetting<String>(
      AppConstants.selectedEnvironmentKey,
      defaultValue: AppConstants.defaultEnvironments.first,
    ) ?? AppConstants.defaultEnvironments.first;
  }

  static Future<void> setSelectedEnvironment(String environment) async {
    await saveSetting(AppConstants.selectedEnvironmentKey, environment);
  }

  static bool areNotificationsEnabled() {
    return getSetting<bool>(AppConstants.notificationsEnabledKey, defaultValue: true) ?? true;
  }

  static Future<void> setNotificationsEnabled(bool value) async {
    await saveSetting(AppConstants.notificationsEnabledKey, value);
  }

  static bool isVoiceEnabled() {
    return getSetting<bool>(AppConstants.voiceEnabledKey, defaultValue: true) ?? true;
  }

  static Future<void> setVoiceEnabled(bool value) async {
    await saveSetting(AppConstants.voiceEnabledKey, value);
  }

  static bool isAutoPlayVoiceEnabled() {
    return getSetting<bool>(AppConstants.autoPlayVoiceKey, defaultValue: true) ?? true;
  }

  static Future<void> setAutoPlayVoice(bool value) async {
    await saveSetting(AppConstants.autoPlayVoiceKey, value);
  }

  // Progress Storage
  static Future<void> saveProgress(String key, dynamic value) async {
    await _progressBox.put(key, value);
  }

  static T? getProgress<T>(String key, {T? defaultValue}) {
    return _progressBox.get(key, defaultValue: defaultValue) as T?;
  }

  static int getUserXP() {
    return getProgress<int>('user_xp', defaultValue: 0) ?? 0;
  }

  static Future<void> addUserXP(int xp) async {
    final currentXP = getUserXP();
    await saveProgress('user_xp', currentXP + xp);
  }

  static int getUserLevel() {
    final xp = getUserXP();
    for (int i = AppConstants.levelThresholds.length - 1; i >= 0; i--) {
      if (xp >= AppConstants.levelThresholds[i]) {
        return i;
      }
    }
    return 0;
  }

  static int getUserHearts() {
    return getProgress<int>('user_hearts', defaultValue: 100) ?? 100;
  }

  static Future<void> setUserHearts(int hearts) async {
    await saveProgress('user_hearts', hearts);
  }

  static Future<void> addUserHearts(int hearts) async {
    final currentHearts = getUserHearts();
    await setUserHearts(currentHearts + hearts);
  }

  static Future<bool> spendUserHearts(int hearts) async {
    final currentHearts = getUserHearts();
    if (currentHearts >= hearts) {
      await setUserHearts(currentHearts - hearts);
      return true;
    }
    return false;
  }

  static List<String> getOwnedEnvironments() {
    return getProgress<List<String>>(
      'owned_environments',
      defaultValue: [AppConstants.defaultEnvironments.first],
    )?.cast<String>() ?? [AppConstants.defaultEnvironments.first];
  }

  static Future<void> addOwnedEnvironment(String environment) async {
    final owned = getOwnedEnvironments();
    if (!owned.contains(environment)) {
      owned.add(environment);
      await saveProgress('owned_environments', owned);
    }
  }

  static DateTime? getLastDailyBonus() {
    final timestamp = getProgress<int>('last_daily_bonus');
    return timestamp != null ? DateTime.fromMillisecondsSinceEpoch(timestamp) : null;
  }

  static Future<void> setLastDailyBonus(DateTime date) async {
    await saveProgress('last_daily_bonus', date.millisecondsSinceEpoch);
  }

  static bool canClaimDailyBonus() {
    final lastBonus = getLastDailyBonus();
    if (lastBonus == null) return true;
    
    final now = DateTime.now();
    final daysSinceBonus = now.difference(lastBonus).inDays;
    return daysSinceBonus >= 1;
  }

  // Debug method to fix CompanionPersonality type issues
  static Future<void> fixPersonalityTypeIssue() async {
    try {
      final user = _userBox.get('current_user');
      if (user != null) {
        // Recreate user with proper types
        final fixedUser = UserModel(
          id: user.id,
          name: user.name,
          email: user.email,
          photoURL: user.photoURL,
          createdAt: user.createdAt,
          progress: user.progress,
          selectedPersonality: CompanionPersonality.caringFriend, // Reset to default
          selectedEnvironment: user.selectedEnvironment,
          ownedEnvironments: user.ownedEnvironments,
          ownedOutfits: user.ownedOutfits,
          preferences: user.preferences,
          lastLogin: user.lastLogin,
        );
        await saveUser(fixedUser);
        print('Fixed personality type issue');
      }
    } catch (e) {
      print('Error fixing personality type: $e');
      await clearUser();
    }
  }

  // Cleanup
  static Future<void> clearAllData() async {
    await _userBox.clear();
    await _chatBox.clear();
    await _settingsBox.clear();
    await _progressBox.clear();
  }

  static Future<void> close() async {
    await _userBox.close();
    await _chatBox.close();
    await _settingsBox.close();
    await _progressBox.close();
  }
}
