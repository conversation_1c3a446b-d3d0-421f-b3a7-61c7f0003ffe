import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;
import '../../core/constants/app_constants.dart';
import '../auth/auth_service.dart';

enum WebSocketState {
  disconnected,
  connecting,
  connected,
  reconnecting,
  error,
}

class WebSocketMessage {
  final String type;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  WebSocketMessage({
    required this.type,
    required this.data,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  factory WebSocketMessage.fromJson(Map<String, dynamic> json) {
    DateTime timestamp = DateTime.now();

    if (json['timestamp'] != null) {
      try {
        // Try parsing as string first
        if (json['timestamp'] is String) {
          timestamp = DateTime.parse(json['timestamp']);
        }
        // Try parsing as number (milliseconds since epoch)
        else if (json['timestamp'] is num) {
          timestamp = DateTime.fromMillisecondsSinceEpoch(json['timestamp'].toInt());
        }
      } catch (e) {
        print('WebSocket: Failed to parse timestamp: ${json['timestamp']}, using current time');
        // timestamp remains DateTime.now()
      }
    }

    return WebSocketMessage(
      type: json['type'] ?? '',
      data: json,
      timestamp: timestamp,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      ...data,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

class WebSocketService {
  final AuthService _authService;
  WebSocketChannel? _channel;
  WebSocketState _state = WebSocketState.disconnected;
  
  // Stream controllers
  final _messageController = StreamController<WebSocketMessage>.broadcast();
  final _stateController = StreamController<WebSocketState>.broadcast();
  final _errorController = StreamController<String>.broadcast();
  
  // Connection management
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 3; // Reduced to prevent rapid cycling
  static const Duration _heartbeatInterval = Duration(seconds: 25); // Slightly faster than backend timeout
  static const Duration _reconnectDelay = Duration(seconds: 5); // Longer delay between reconnects
  
  // Session management
  String? _sessionId;
  String? _connectionId;
  String? _conversationId;
  
  WebSocketService(this._authService);

  // Getters
  Stream<WebSocketMessage> get messageStream => _messageController.stream;
  Stream<WebSocketState> get stateStream => _stateController.stream;
  Stream<String> get errorStream => _errorController.stream;
  WebSocketState get state => _state;
  bool get isConnected => _state == WebSocketState.connected;
  String? get sessionId => _sessionId;
  String? get connectionId => _connectionId;

  // Connection Management
  Future<void> connect({String? conversationId}) async {
    if (_state == WebSocketState.connected || _state == WebSocketState.connecting) {
      print('WebSocket: Already connected or connecting, skipping...');
      return;
    }

    _conversationId = conversationId;
    _setState(WebSocketState.connecting);
    print('WebSocket: Starting connection...');

    try {
      final token = await _authService.getAccessToken();
      print('WebSocket: Got auth token: ${token != null ? 'YES' : 'NO'}');

      // For testing, create a dummy token if none exists
      final actualToken = token ?? 'dummy_token_for_testing';
      print('WebSocket: Using token: $actualToken');

      final wsUrl = '${AppConstants.websocketUrl}/ws/chat/?token=$actualToken';
      print('WebSocket: Connecting to: $wsUrl');

      // Parse URI and ensure it's a WebSocket URI
      final uri = Uri.parse(wsUrl);
      print('WebSocket: Parsed URI - scheme: ${uri.scheme}, host: ${uri.host}, port: ${uri.port}, path: ${uri.path}');

      // Ensure we're using WebSocket scheme
      final wsUri = uri.replace(scheme: uri.scheme == 'https' ? 'wss' : 'ws');
      print('WebSocket: Final WebSocket URI: $wsUri');

      _channel = WebSocketChannel.connect(wsUri);
      print('WebSocket: Channel created, setting up listeners...');

      // Listen to messages
      _channel!.stream.listen(
        _onMessage,
        onError: _onError,
        onDone: _onDisconnected,
      );

      print('WebSocket: Waiting for connection establishment...');
      // Wait for connection establishment
      await _waitForConnection();

    } catch (e) {
      print('WebSocket: Connection failed: $e');
      _setState(WebSocketState.error);
      _errorController.add('Connection failed: $e');
      _scheduleReconnect();
    }
  }

  Future<void> disconnect() async {
    _reconnectAttempts = 0;
    _heartbeatTimer?.cancel();
    _reconnectTimer?.cancel();
    
    if (_channel != null) {
      await _channel!.sink.close(status.goingAway);
      _channel = null;
    }
    
    _setState(WebSocketState.disconnected);
  }

  Future<void> _waitForConnection() async {
    final completer = Completer<void>();
    late StreamSubscription subscription;
    
    subscription = _messageController.stream.listen((message) {
      if (message.type == 'connection_established') {
        _sessionId = message.data['session_id'];
        _connectionId = message.data['connection_id'];
        _setState(WebSocketState.connected);
        _startHeartbeat();
        _reconnectAttempts = 0;
        subscription.cancel();
        completer.complete();
      }
    });

    // Timeout after 10 seconds
    Timer(const Duration(seconds: 10), () {
      if (!completer.isCompleted) {
        subscription.cancel();
        completer.completeError('Connection timeout');
      }
    });

    return completer.future;
  }

  void _onMessage(dynamic data) {
    try {
      final json = jsonDecode(data);
      final message = WebSocketMessage.fromJson(json);

      // Debug: Log all incoming messages to see what we're receiving
      print('WebSocket: Received message type: ${message.type}');

      _messageController.add(message);
      
      // Handle special message types
      switch (message.type) {
        case 'connection_heartbeat':
          // Legacy heartbeat handling
          _sendHeartbeatResponse();
          break;
        case 'heartbeat_request':
          // Backend is requesting a heartbeat response - this is the main one!
          print('WebSocket: Received heartbeat_request from backend, responding...');
          _sendHeartbeatResponse();
          break;
        case 'heartbeat_response':
          // Backend confirming our heartbeat
          print('WebSocket: Received heartbeat_response from backend');
          break;
        case 'error':
          _errorController.add(message.data['message'] ?? 'Unknown error');
          break;
      }
    } catch (e) {
      _errorController.add('Failed to parse message: $e');
    }
  }

  void _onError(error) {
    _setState(WebSocketState.error);
    _errorController.add('WebSocket error: $error');
    _scheduleReconnect();
  }

  void _onDisconnected() {
    _setState(WebSocketState.disconnected);
    _heartbeatTimer?.cancel();
    _scheduleReconnect();
  }

  void _setState(WebSocketState newState) {
    if (_state != newState) {
      _state = newState;
      _stateController.add(_state);
    }
  }

  void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    // Backend sends heartbeat_request every 30 seconds, we just respond
    // No need for client-initiated periodic heartbeats
    print('WebSocket: Heartbeat monitoring started (response-only mode)');
  }

  void _sendHeartbeatResponse() {
    // Send heartbeat that backend expects (connection_heartbeat)
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    print('WebSocket: Sending connection_heartbeat response (timestamp: $timestamp)');

    sendMessage('connection_heartbeat', {
      'timestamp': timestamp,
      'session_id': _sessionId,
      'connection_id': _connectionId,
      'response': true, // Indicate this is a response to heartbeat_request
    });
  }

  void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      print('WebSocket: Max reconnection attempts reached, stopping reconnection');
      _setState(WebSocketState.error);
      _errorController.add('Max reconnection attempts reached');
      return;
    }

    // Don't reconnect if already connecting or connected
    if (_state == WebSocketState.connecting || _state == WebSocketState.connected) {
      print('WebSocket: Skipping reconnect - already connecting/connected');
      return;
    }

    _reconnectTimer?.cancel();
    _reconnectTimer = Timer(_reconnectDelay, () {
      _reconnectAttempts++;
      print('WebSocket: Reconnection attempt $_reconnectAttempts/$_maxReconnectAttempts');
      _setState(WebSocketState.reconnecting);
      connect(conversationId: _conversationId);
    });
  }

  // Message Sending
  void sendMessage(String type, Map<String, dynamic> data) {
    if (!isConnected) {
      _errorController.add('Cannot send message: not connected');
      return;
    }

    final message = {
      'type': type,
      ...data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };

    if (_conversationId != null) {
      message['conversation_id'] = _conversationId;
    }

    try {
      _channel!.sink.add(jsonEncode(message));
    } catch (e) {
      _errorController.add('Failed to send message: $e');
    }
  }

  void sendTextMessage(String content, {String? conversationId}) {
    print('WebSocketService: Sending text message - Connected: $isConnected');
    print('WebSocketService: Message content: "$content"');
    print('WebSocketService: Conversation ID: $conversationId');

    sendMessage('text_message', {
      'content': content,
      if (conversationId != null) 'conversation_id': conversationId,
    });
  }

  void sendAudioChunk({
    required Uint8List audioData,
    required String chunkId,
    required bool isFinal,
    String? conversationId,
  }) {
    final base64Data = base64Encode(audioData);
    sendMessage('audio_chunk', {
      'data': base64Data,
      'chunk_id': chunkId,
      'is_final': isFinal,
      if (conversationId != null) 'conversation_id': conversationId,
    });
  }

  void sendTypingStart() {
    sendMessage('typing_start', {});
  }

  void sendTypingStop() {
    sendMessage('typing_stop', {});
  }

  void sendEmotionFeedback({
    required String emotion,
    required double intensity,
    String? context,
  }) {
    sendMessage('emotion_feedback', {
      'emotion': emotion,
      'intensity': intensity,
      if (context != null) 'context': context,
    });
  }

  void switchConversation(String conversationId) {
    _conversationId = conversationId;
    sendMessage('conversation_switch', {
      'conversation_id': conversationId,
    });
  }

  // Cleanup
  void dispose() {
    disconnect();
    _messageController.close();
    _stateController.close();
    _errorController.close();
  }
}
