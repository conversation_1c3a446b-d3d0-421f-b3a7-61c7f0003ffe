import 'package:dio/dio.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../../core/constants/app_constants.dart';

class AuthService {
  static const _storage = FlutterSecureStorage();
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userIdKey = 'user_id';

  late final Dio _dio;

  AuthService() {
    _dio = Dio(BaseOptions(
      baseUrl: AppConstants.baseUrl,
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
      sendTimeout: const Duration(seconds: 10),
      headers: {
        'Content-Type': 'application/json',
      },
    ));

    // Add interceptor for automatic token refresh
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        final token = await getAccessToken();
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        handler.next(options);
      },
      onError: (error, handler) async {
        if (error.response?.statusCode == 401) {
          // Try to refresh token
          final refreshed = await refreshToken();
          if (refreshed) {
            // Retry the original request
            final token = await getAccessToken();
            final opts = error.requestOptions;
            opts.headers['Authorization'] = 'Bearer $token';
            
            try {
              final response = await _dio.fetch(opts);
              handler.resolve(response);
              return;
            } catch (e) {
              // If retry fails, continue with original error
            }
          }
        }
        handler.next(error);
      },
    ));
  }

  // Token Management
  Future<String?> getAccessToken() async {
    final token = await _storage.read(key: _accessTokenKey);
    print('AuthService: Getting access token: ${token != null ? 'EXISTS' : 'NULL'}');
    return token;
  }

  Future<String?> getRefreshToken() async {
    return await _storage.read(key: _refreshTokenKey);
  }

  Future<String?> getUserId() async {
    return await _storage.read(key: _userIdKey);
  }

  Future<void> saveTokens({
    required String accessToken,
    required String refreshToken,
    required String userId,
  }) async {
    await Future.wait([
      _storage.write(key: _accessTokenKey, value: accessToken),
      _storage.write(key: _refreshTokenKey, value: refreshToken),
      _storage.write(key: _userIdKey, value: userId),
    ]);
  }

  Future<void> clearTokens() async {
    await Future.wait([
      _storage.delete(key: _accessTokenKey),
      _storage.delete(key: _refreshTokenKey),
      _storage.delete(key: _userIdKey),
    ]);
  }

  Future<bool> isAuthenticated() async {
    final token = await getAccessToken();
    return token != null && token.isNotEmpty;
  }

  // Authentication Endpoints
  Future<Map<String, dynamic>> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? username,
  }) async {
    try {
      final response = await _dio.post('/api/auth/register/', data: {
        'email': email,
        'password': password,
        'first_name': firstName,
        'last_name': lastName,
        'username': username ?? email.split('@')[0],
      });

      final data = response.data;
      await saveTokens(
        accessToken: data['tokens']['access'],
        refreshToken: data['tokens']['refresh'],
        userId: data['user']['id'],
      );

      return data;
    } on DioException catch (e) {
      if (e.response != null) {
        final statusCode = e.response!.statusCode;
        final responseData = e.response!.data;

        switch (statusCode) {
          case 400:
            if (responseData is Map && responseData.containsKey('error')) {
              throw AuthException(responseData['error']);
            } else if (responseData is Map && responseData.containsKey('email')) {
              throw AuthException('An account with this email already exists');
            } else {
              throw AuthException('Invalid registration data. Please check your information.');
            }
          case 409:
            throw AuthException('An account with this email already exists');
          case 422:
            throw AuthException('Invalid data provided. Please check all fields.');
          case 500:
            throw AuthException('Server error. Please try again later.');
          default:
            throw AuthException('Registration failed. Please try again.');
        }
      } else {
        throw AuthException('Network error. Please check your connection.');
      }
    } catch (e) {
      throw AuthException('Registration failed: $e');
    }
  }

  Future<Map<String, dynamic>> login({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _dio.post('/api/auth/login/', data: {
        'email': email,
        'password': password,
      });

      final data = response.data;
      await saveTokens(
        accessToken: data['tokens']['access'],
        refreshToken: data['tokens']['refresh'],
        userId: data['user']['id'],
      );

      return data;
    } on DioException catch (e) {
      if (e.response != null) {
        final statusCode = e.response!.statusCode;
        final responseData = e.response!.data;

        switch (statusCode) {
          case 400:
            if (responseData is Map && responseData.containsKey('error')) {
              throw AuthException(responseData['error']);
            } else {
              throw AuthException('Invalid email or password');
            }
          case 401:
            throw AuthException('Invalid email or password');
          case 404:
            throw AuthException('No account found with this email');
          case 500:
            throw AuthException('Server error. Please try again later.');
          default:
            throw AuthException('Login failed. Please try again.');
        }
      } else {
        throw AuthException('Network error. Please check your connection.');
      }
    } catch (e) {
      throw AuthException('Login failed: $e');
    }
  }

  Future<Map<String, dynamic>> loginWithGoogle(String idToken) async {
    try {
      final response = await _dio.post('/api/auth/google/', data: {
        'id_token': idToken,
      });

      final data = response.data;
      await saveTokens(
        accessToken: data['tokens']['access'],
        refreshToken: data['tokens']['refresh'],
        userId: data['user']['id'],
      );

      return data;
    } on DioException catch (e) {
      if (e.response != null) {
        final statusCode = e.response!.statusCode;
        final responseData = e.response!.data;

        switch (statusCode) {
          case 400:
            if (responseData is Map && responseData.containsKey('error')) {
              throw AuthException(responseData['error']);
            } else {
              throw AuthException('Invalid Google token');
            }
          case 401:
            throw AuthException('Google authentication failed');
          case 500:
            throw AuthException('Server error. Please try again later.');
          default:
            throw AuthException('Google login failed. Please try again.');
        }
      } else {
        throw AuthException('Network error. Please check your connection.');
      }
    } catch (e) {
      throw AuthException('Google login failed: $e');
    }
  }

  Future<Map<String, dynamic>> loginWithApple({
    required String identityToken,
    required String authorizationCode,
    String? email,
    String? firstName,
    String? lastName,
  }) async {
    try {
      final response = await _dio.post('/api/auth/apple/', data: {
        'identity_token': identityToken,
        'authorization_code': authorizationCode,
        'email': email,
        'first_name': firstName,
        'last_name': lastName,
      });

      final data = response.data;
      await saveTokens(
        accessToken: data['tokens']['access'],
        refreshToken: data['tokens']['refresh'],
        userId: data['user']['id'],
      );

      return data;
    } on DioException catch (e) {
      if (e.response != null) {
        final statusCode = e.response!.statusCode;
        final responseData = e.response!.data;

        switch (statusCode) {
          case 400:
            if (responseData is Map && responseData.containsKey('error')) {
              throw AuthException(responseData['error']);
            } else {
              throw AuthException('Invalid Apple credentials');
            }
          case 401:
            throw AuthException('Apple authentication failed');
          case 500:
            throw AuthException('Server error. Please try again later.');
          default:
            throw AuthException('Apple login failed. Please try again.');
        }
      } else {
        throw AuthException('Network error. Please check your connection.');
      }
    } catch (e) {
      throw AuthException('Apple login failed: $e');
    }
  }

  Future<bool> refreshToken() async {
    try {
      final refreshToken = await getRefreshToken();
      if (refreshToken == null) return false;

      final response = await _dio.post('/api/auth/refresh/', data: {
        'refresh': refreshToken,
      });

      final data = response.data;
      await _storage.write(key: _accessTokenKey, value: data['access']);

      return true;
    } catch (e) {
      // If refresh fails, clear all tokens
      await clearTokens();
      return false;
    }
  }

  Future<void> logout() async {
    try {
      final refreshToken = await getRefreshToken();
      if (refreshToken != null) {
        await _dio.post('/api/auth/logout/', data: {
          'refresh': refreshToken,
        });
      }
    } catch (e) {
      // Continue with logout even if API call fails
    } finally {
      await clearTokens();
    }
  }

  // User Profile
  Future<Map<String, dynamic>> getUserProfile() async {
    try {
      final response = await _dio.get('/api/user/profile/');
      return response.data;
    } catch (e) {
      throw AuthException('Failed to get user profile: $e');
    }
  }

  Future<Map<String, dynamic>> updateUserProfile(Map<String, dynamic> data) async {
    try {
      final response = await _dio.put('/api/user/profile/', data: data);
      return response.data;
    } catch (e) {
      throw AuthException('Failed to update user profile: $e');
    }
  }
}

class AuthException implements Exception {
  final String message;
  AuthException(this.message);
  
  @override
  String toString() => 'AuthException: $message';
}
