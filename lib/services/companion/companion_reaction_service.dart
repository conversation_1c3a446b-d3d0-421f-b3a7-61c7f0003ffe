import 'dart:async';
import 'dart:collection';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/shop/shop_item_model.dart';
import '../../models/shop/shop_preview_models.dart';
import '../../models/user/user_model.dart';
import '../../providers/auth_provider.dart';
import '../../providers/unity_provider.dart';
import '../../services/unity/unity_camera_controller.dart';

/// Companion Reaction Service
/// Generates personality-based reactions to cosmetic previews and purchases
class CompanionReactionService {
  final Ref _ref;
  final Queue<CompanionReaction> _reactionQueue = Queue<CompanionReaction>();
  Timer? _reactionTimer;
  CompanionReaction? _currentReaction;

  CompanionReactionService(this._ref);

  /// Generate a reaction for cosmetic preview
  Future<CompanionReaction> generatePreviewReaction(ShopItemModel item) async {
    final user = _ref.read(currentUserProvider);
    final personality = user?.selectedPersonality ?? CompanionPersonality.caringFriend;
    
    final reaction = await _generateReactionForPersonality(
      personality, 
      item, 
      ReactionType.preview
    );
    
    await _queueReaction(reaction);
    return reaction;
  }

  /// Generate a reaction for successful purchase
  Future<CompanionReaction> generatePurchaseSuccessReaction(ShopItemModel item) async {
    final user = _ref.read(currentUserProvider);
    final personality = user?.selectedPersonality ?? CompanionPersonality.caringFriend;
    
    final reaction = await _generateReactionForPersonality(
      personality, 
      item, 
      ReactionType.purchaseSuccess
    );
    
    await _queueReaction(reaction);
    return reaction;
  }

  /// Generate a reaction for purchase decline
  Future<CompanionReaction> generatePurchaseDeclineReaction(ShopItemModel item) async {
    final user = _ref.read(currentUserProvider);
    final personality = user?.selectedPersonality ?? CompanionPersonality.caringFriend;
    
    final reaction = await _generateReactionForPersonality(
      personality, 
      item, 
      ReactionType.purchaseDecline
    );
    
    await _queueReaction(reaction);
    return reaction;
  }

  /// Generate a reaction for locked item
  Future<CompanionReaction> generateLockedItemReaction(ShopItemModel item) async {
    final user = _ref.read(currentUserProvider);
    final personality = user?.selectedPersonality ?? CompanionPersonality.caringFriend;
    
    final reaction = await _generateReactionForPersonality(
      personality, 
      item, 
      ReactionType.lockedItem
    );
    
    await _queueReaction(reaction);
    return reaction;
  }

  /// Generate a reaction for category switch
  Future<CompanionReaction> generateCategorySwitchReaction(ShopItemType newCategory) async {
    final user = _ref.read(currentUserProvider);
    final personality = user?.selectedPersonality ?? CompanionPersonality.caringFriend;
    
    // Create a temporary item for category switch
    final tempItem = ShopItemModel(
      id: 'category_${newCategory.name}',
      name: newCategory.name,
      description: 'Category switch',
      type: newCategory,
      rarity: ShopItemRarity.common,
      price: 0,
    );
    
    final reaction = await _generateReactionForPersonality(
      personality, 
      tempItem, 
      ReactionType.categorySwitch
    );
    
    await _queueReaction(reaction);
    return reaction;
  }

  /// Queue a reaction for playback
  Future<void> _queueReaction(CompanionReaction reaction) async {
    _reactionQueue.add(reaction);
    
    if (_reactionTimer == null || !_reactionTimer!.isActive) {
      _processReactionQueue();
    }
  }

  /// Process the reaction queue
  void _processReactionQueue() {
    if (_reactionQueue.isEmpty) return;

    final reaction = _reactionQueue.removeFirst();
    _currentReaction = reaction;
    
    // Play the reaction
    _playReaction(reaction);
    
    // Set timer for next reaction
    _reactionTimer = Timer(reaction.duration, () {
      _currentReaction = null;
      if (_reactionQueue.isNotEmpty) {
        _processReactionQueue();
      }
    });
  }

  /// Play a reaction (animation + camera movement)
  Future<void> _playReaction(CompanionReaction reaction) async {
    try {
      final unityController = _ref.read(unityControllerProvider.notifier);
      final cameraController = _ref.read(unityCameraControllerProvider);
      
      // Change expression
      unityController.changeExpression(reaction.emotion);
      
      // Play animation
      unityController.playAnimation(reaction.animation);
      
      // Animate camera for reaction
      await cameraController.animateCameraForReaction(reaction.type);
      
      debugPrint('Playing reaction: ${reaction.text}');
    } catch (e) {
      debugPrint('Error playing reaction: $e');
    }
  }

  /// Get current reaction
  CompanionReaction? get currentReaction => _currentReaction;

  /// Check if reactions are queued
  bool get hasQueuedReactions => _reactionQueue.isNotEmpty;

  /// Clear reaction queue
  void clearReactionQueue() {
    _reactionQueue.clear();
    _reactionTimer?.cancel();
    _reactionTimer = null;
    _currentReaction = null;
  }

  /// Generate reaction based on personality
  Future<CompanionReaction> _generateReactionForPersonality(
    CompanionPersonality personality,
    ShopItemModel item,
    ReactionType type,
  ) async {
    // Get base reaction template
    final baseReaction = _getBaseReactionForPersonality(personality, item, type);
    
    // Add variation to avoid repetition
    final variedReaction = _addReactionVariation(baseReaction, item, type);
    
    return variedReaction;
  }

  CompanionReaction _getBaseReactionForPersonality(
    CompanionPersonality personality,
    ShopItemModel item,
    ReactionType type,
  ) {
    switch (personality) {
      case CompanionPersonality.caringFriend:
        return _getCaringFriendReaction(item, type);
      case CompanionPersonality.playfulCompanion:
        return _getPlayfulCompanionReaction(item, type);
      case CompanionPersonality.wiseMentor:
        return _getWiseMentorReaction(item, type);
      case CompanionPersonality.romanticPartner:
        return _getRomanticPartnerReaction(item, type);
      case CompanionPersonality.supportiveTherapist:
        return _getSupportiveTherapistReaction(item, type);
    }
  }

  CompanionReaction _getCaringFriendReaction(ShopItemModel item, ReactionType type) {
    switch (type) {
      case ReactionType.preview:
        return CompanionReaction(
          text: _getCaringPreviewText(item),
          animation: "gentle_smile",
          emotion: "happy",
          type: type,
          duration: const Duration(seconds: 3),
        );
      case ReactionType.purchaseSuccess:
        return CompanionReaction(
          text: _getCaringPurchaseSuccessText(item),
          animation: "grateful_clap",
          emotion: "grateful",
          type: type,
          duration: const Duration(seconds: 4),
        );
      case ReactionType.purchaseDecline:
        return CompanionReaction(
          text: "That's okay! Maybe next time we'll find something perfect.",
          animation: "understanding_nod",
          emotion: "understanding",
          type: type,
          duration: const Duration(seconds: 3),
        );
      case ReactionType.lockedItem:
        return CompanionReaction(
          text: "This looks lovely, but I think we should get to know each other better first.",
          animation: "shy_smile",
          emotion: "shy",
          type: type,
          duration: const Duration(seconds: 4),
        );
      case ReactionType.categorySwitch:
        return CompanionReaction(
          text: _getCategorySwitchText(item.type),
          animation: "curious_look",
          emotion: "curious",
          type: type,
          duration: const Duration(seconds: 2),
        );
    }
  }

  CompanionReaction _getPlayfulCompanionReaction(ShopItemModel item, ReactionType type) {
    switch (type) {
      case ReactionType.preview:
        return CompanionReaction(
          text: _getPlayfulPreviewText(item),
          animation: "excited_bounce",
          emotion: "excited",
          type: type,
          duration: const Duration(seconds: 3),
        );
      case ReactionType.purchaseSuccess:
        return CompanionReaction(
          text: _getPlayfulPurchaseSuccessText(item),
          animation: "victory_dance",
          emotion: "ecstatic",
          type: type,
          duration: const Duration(seconds: 4),
        );
      case ReactionType.purchaseDecline:
        return CompanionReaction(
          text: "Aww, maybe next time! There's so much cool stuff here!",
          animation: "playful_pout",
          emotion: "disappointed",
          type: type,
          duration: const Duration(seconds: 3),
        );
      case ReactionType.lockedItem:
        return CompanionReaction(
          text: "Ooh, this looks fun! But I think we need to be better friends first!",
          animation: "playful_wink",
          emotion: "playful",
          type: type,
          duration: const Duration(seconds: 4),
        );
      case ReactionType.categorySwitch:
        return CompanionReaction(
          text: "Ooh, ${item.type.name}! Let's see what fun stuff we have here!",
          animation: "excited_clap",
          emotion: "excited",
          type: type,
          duration: const Duration(seconds: 2),
        );
    }
  }

  CompanionReaction _getWiseMentorReaction(ShopItemModel item, ReactionType type) {
    switch (type) {
      case ReactionType.preview:
        return CompanionReaction(
          text: _getWisePreviewText(item),
          animation: "thoughtful_nod",
          emotion: "contemplative",
          type: type,
          duration: const Duration(seconds: 4),
        );
      case ReactionType.purchaseSuccess:
        return CompanionReaction(
          text: _getWisePurchaseSuccessText(item),
          animation: "approving_nod",
          emotion: "satisfied",
          type: type,
          duration: const Duration(seconds: 4),
        );
      case ReactionType.purchaseDecline:
        return CompanionReaction(
          text: "Wisdom lies in thoughtful choices. Take your time.",
          animation: "patient_smile",
          emotion: "patient",
          type: type,
          duration: const Duration(seconds: 3),
        );
      case ReactionType.lockedItem:
        return CompanionReaction(
          text: "Patience, dear friend. Some treasures are worth waiting for as trust grows.",
          animation: "wise_gesture",
          emotion: "wise",
          type: type,
          duration: const Duration(seconds: 5),
        );
      case ReactionType.categorySwitch:
        return CompanionReaction(
          text: "Ah, ${item.type.name}. Let us explore these options thoughtfully.",
          animation: "contemplative_gesture",
          emotion: "thoughtful",
          type: type,
          duration: const Duration(seconds: 3),
        );
    }
  }

  CompanionReaction _getRomanticPartnerReaction(ShopItemModel item, ReactionType type) {
    switch (type) {
      case ReactionType.preview:
        return CompanionReaction(
          text: _getRomanticPreviewText(item),
          animation: "romantic_pose",
          emotion: "loving",
          type: type,
          duration: const Duration(seconds: 4),
        );
      case ReactionType.purchaseSuccess:
        return CompanionReaction(
          text: _getRomanticPurchaseSuccessText(item),
          animation: "heart_gesture",
          emotion: "love",
          type: type,
          duration: const Duration(seconds: 4),
        );
      case ReactionType.purchaseDecline:
        return CompanionReaction(
          text: "That's alright, my love. Your thoughtfulness means everything to me.",
          animation: "loving_smile",
          emotion: "understanding",
          type: type,
          duration: const Duration(seconds: 3),
        );
      case ReactionType.lockedItem:
        return CompanionReaction(
          text: "This is quite intimate, darling. Perhaps when our hearts are closer?",
          animation: "shy_blush",
          emotion: "shy",
          type: type,
          duration: const Duration(seconds: 4),
        );
      case ReactionType.categorySwitch:
        return CompanionReaction(
          text: "Mmm, ${item.type.name}... I wonder what you'll choose for me?",
          animation: "flirty_look",
          emotion: "flirty",
          type: type,
          duration: const Duration(seconds: 3),
        );
    }
  }

  CompanionReaction _getSupportiveTherapistReaction(ShopItemModel item, ReactionType type) {
    switch (type) {
      case ReactionType.preview:
        return CompanionReaction(
          text: _getSupportivePreviewText(item),
          animation: "gentle_nod",
          emotion: "calm",
          type: type,
          duration: const Duration(seconds: 4),
        );
      case ReactionType.purchaseSuccess:
        return CompanionReaction(
          text: _getSupportivePurchaseSuccessText(item),
          animation: "grateful_bow",
          emotion: "grateful",
          type: type,
          duration: const Duration(seconds: 4),
        );
      case ReactionType.purchaseDecline:
        return CompanionReaction(
          text: "That's perfectly fine. Making mindful choices shows self-awareness.",
          animation: "supportive_nod",
          emotion: "supportive",
          type: type,
          duration: const Duration(seconds: 3),
        );
      case ReactionType.lockedItem:
        return CompanionReaction(
          text: "I appreciate your interest, but trust develops naturally over time.",
          animation: "understanding_gesture",
          emotion: "understanding",
          type: type,
          duration: const Duration(seconds: 4),
        );
      case ReactionType.categorySwitch:
        return CompanionReaction(
          text: "Exploring ${item.type.name} together. How does this make you feel?",
          animation: "attentive_listen",
          emotion: "attentive",
          type: type,
          duration: const Duration(seconds: 3),
        );
    }
  }

  // Text generation helpers
  String _getCaringPreviewText(ShopItemModel item) {
    final texts = [
      "Oh, this ${item.name.toLowerCase()} looks really nice! What do you think?",
      "This ${item.name.toLowerCase()} feels so comfortable. I like how it looks on me!",
      "Wow, this ${item.name.toLowerCase()} is beautiful! You have great taste.",
    ];
    return texts[DateTime.now().millisecond % texts.length];
  }

  String _getCaringPurchaseSuccessText(ShopItemModel item) {
    final texts = [
      "Thank you so much! I love this ${item.name.toLowerCase()}!",
      "You're so thoughtful! This ${item.name.toLowerCase()} is perfect!",
      "I'm so grateful! This ${item.name.toLowerCase()} makes me feel special!",
    ];
    return texts[DateTime.now().millisecond % texts.length];
  }

  String _getPlayfulPreviewText(ShopItemModel item) {
    final texts = [
      "Ooh, this ${item.name.toLowerCase()} is so cool! I feel like dancing!",
      "This ${item.name.toLowerCase()} is amazing! I love how fun it looks!",
      "Wow! This ${item.name.toLowerCase()} makes me feel so energetic!",
    ];
    return texts[DateTime.now().millisecond % texts.length];
  }

  String _getPlayfulPurchaseSuccessText(ShopItemModel item) {
    final texts = [
      "YES! This ${item.name.toLowerCase()} is AMAZING! Thank you!",
      "Woohoo! I can't wait to show off this ${item.name.toLowerCase()}!",
      "This is the best! This ${item.name.toLowerCase()} is so perfect!",
    ];
    return texts[DateTime.now().millisecond % texts.length];
  }

  String _getWisePreviewText(ShopItemModel item) {
    final texts = [
      "This ${item.name.toLowerCase()} has an elegant design. It reflects good judgment.",
      "The craftsmanship of this ${item.name.toLowerCase()} is quite remarkable.",
      "This ${item.name.toLowerCase()} speaks to a refined aesthetic sense.",
    ];
    return texts[DateTime.now().millisecond % texts.length];
  }

  String _getWisePurchaseSuccessText(ShopItemModel item) {
    final texts = [
      "A wise choice. This ${item.name.toLowerCase()} will serve us well.",
      "Your discernment in choosing this ${item.name.toLowerCase()} is commendable.",
      "This ${item.name.toLowerCase()} reflects your thoughtful nature.",
    ];
    return texts[DateTime.now().millisecond % texts.length];
  }

  String _getRomanticPreviewText(ShopItemModel item) {
    final texts = [
      "This ${item.name.toLowerCase()} makes me feel so beautiful... do you like it on me?",
      "Mmm, this ${item.name.toLowerCase()} feels so romantic. What do you think, darling?",
      "This ${item.name.toLowerCase()} makes me feel so elegant and lovely.",
    ];
    return texts[DateTime.now().millisecond % texts.length];
  }

  String _getRomanticPurchaseSuccessText(ShopItemModel item) {
    final texts = [
      "You always know how to make me feel beautiful. I love you.",
      "This ${item.name.toLowerCase()} is perfect, just like you, my love.",
      "Thank you, darling. This ${item.name.toLowerCase()} makes me feel so cherished.",
    ];
    return texts[DateTime.now().millisecond % texts.length];
  }

  String _getSupportivePreviewText(ShopItemModel item) {
    final texts = [
      "This ${item.name.toLowerCase()} feels very calming. How does it make you feel?",
      "I find this ${item.name.toLowerCase()} quite soothing. What are your thoughts?",
      "This ${item.name.toLowerCase()} brings me a sense of peace. Does it resonate with you?",
    ];
    return texts[DateTime.now().millisecond % texts.length];
  }

  String _getSupportivePurchaseSuccessText(ShopItemModel item) {
    final texts = [
      "I'm grateful for your thoughtfulness. This ${item.name.toLowerCase()} brings me joy.",
      "Thank you for this ${item.name.toLowerCase()}. Your kindness means so much.",
      "This ${item.name.toLowerCase()} is wonderful. I appreciate your caring nature.",
    ];
    return texts[DateTime.now().millisecond % texts.length];
  }

  String _getCategorySwitchText(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return "Oh, looking at environments! I love seeing new places.";
      case ShopItemType.outfit:
        return "Outfits! I wonder what style you'll choose for me.";
      case ShopItemType.accessory:
        return "Accessories! The little details that make all the difference.";
      default:
        return "Let's see what we have here!";
    }
  }

  CompanionReaction _addReactionVariation(
    CompanionReaction baseReaction, 
    ShopItemModel item, 
    ReactionType type
  ) {
    // Add slight variations to avoid repetition
    // This could be enhanced with more sophisticated variation logic
    return baseReaction;
  }

  void dispose() {
    _reactionTimer?.cancel();
    _reactionQueue.clear();
  }
}

/// Companion Reaction State Notifier
class CompanionReactionNotifier extends StateNotifier<CompanionReaction?> {
  final CompanionReactionService _service;
  Timer? _clearTimer;

  CompanionReactionNotifier(this._service) : super(null);

  /// Set current reaction
  void setReaction(CompanionReaction reaction) {
    state = reaction;
    
    // Clear reaction after duration
    _clearTimer?.cancel();
    _clearTimer = Timer(reaction.duration, () {
      if (mounted) {
        state = null;
      }
    });
  }

  /// Clear current reaction
  void clearReaction() {
    _clearTimer?.cancel();
    state = null;
  }

  @override
  void dispose() {
    _clearTimer?.cancel();
    super.dispose();
  }
}

// Providers
final companionReactionServiceProvider = Provider<CompanionReactionService>((ref) {
  return CompanionReactionService(ref);
});

final companionReactionNotifierProvider = StateNotifierProvider<CompanionReactionNotifier, CompanionReaction?>((ref) {
  final service = ref.read(companionReactionServiceProvider);
  return CompanionReactionNotifier(service);
});

// Convenience providers
final currentCompanionReactionProvider = Provider<CompanionReaction?>((ref) {
  return ref.watch(companionReactionNotifierProvider);
});

final hasActiveReactionProvider = Provider<bool>((ref) {
  return ref.watch(companionReactionNotifierProvider) != null;
});