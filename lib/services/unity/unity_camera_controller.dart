import 'package:ellahai/models/shop/shop_item_model.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/shop/shop_preview_models.dart';
import '../../providers/unity_provider.dart';

/// Unity Camera Controller Service
/// Provides high-level camera control methods for the preview system
class UnityCameraController {
  final Ref _ref;

  UnityCameraController(this._ref);

  /// Transition camera to preview mode with smooth animation
  Future<void> enterPreviewMode({
    Duration? duration,
    ShopItemType? itemType,
  }) async {
    final unityController = _ref.read(unityControllerProvider.notifier);
    final transitionDuration = duration ?? const Duration(milliseconds: 800);

    try {
      // Choose camera preset based on item type
      final preset = _getCameraPresetForItemType(itemType);
      
      unityController.smoothCameraTransition(
        targetPosition: preset['position'],
        targetRotation: preset['rotation'],
        targetFov: preset['fov'],
        duration: transitionDuration,
      );

      // Set camera mode to preview
      await unityController.setCameraMode(CameraMode.preview, duration: transitionDuration);
      
    } catch (e) {
      debugPrint('Error entering preview mode: $e');
      rethrow;
    }
  }

  /// Transition camera back to default chat mode
  Future<void> exitPreviewMode({Duration? duration}) async {
    final unityController = _ref.read(unityControllerProvider.notifier);
    final transitionDuration = duration ?? const Duration(milliseconds: 800);

    try {
      await unityController.transitionToDefaultMode(duration: transitionDuration);
    } catch (e) {
      debugPrint('Error exiting preview mode: $e');
      rethrow;
    }
  }

  /// Switch camera angle for different item types while in preview mode
  Future<void> switchToItemTypeView(ShopItemType itemType, {Duration? duration}) async {
    final unityController = _ref.read(unityControllerProvider.notifier);
    final transitionDuration = duration ?? const Duration(milliseconds: 500);

    try {
      final preset = _getCameraPresetForItemType(itemType);
      
      unityController.smoothCameraTransition(
        targetPosition: preset['position'],
        targetRotation: preset['rotation'],
        targetFov: preset['fov'],
        duration: transitionDuration,
      );
    } catch (e) {
      debugPrint('Error switching camera view: $e');
      rethrow;
    }
  }

  /// Animate camera for companion reactions
  Future<void> animateCameraForReaction(ReactionType reactionType, {Duration? duration}) async {
    final unityController = _ref.read(unityControllerProvider.notifier);
    final transitionDuration = duration ?? const Duration(milliseconds: 300);

    try {
      switch (reactionType) {
        case ReactionType.preview:
          // Slight zoom in for preview reactions
          await _animateSlightZoom(1.1, transitionDuration);
          break;
        case ReactionType.purchaseSuccess:
          // Celebratory camera movement
          await _animateCelebratoryMovement(transitionDuration);
          break;
        case ReactionType.lockedItem:
          // Subtle shake for locked items
          await _animateSubtleShake(transitionDuration);
          break;
        default:
          break;
      }
    } catch (e) {
      debugPrint('Error animating camera for reaction: $e');
    }
  }

  /// Check if camera is currently transitioning
  bool get isTransitioning {
    return _ref.read(isCameraTransitioningProvider);
  }

  /// Get current camera mode
  CameraMode get currentMode {
    return _ref.read(unityCameraModeProvider);
  }

  /// Get current camera settings
  Map<String, dynamic> get currentSettings {
    return _ref.read(cameraSettingsProvider);
  }

  // Private helper methods

  Map<String, dynamic> _getCameraPresetForItemType(ShopItemType? itemType) {
    switch (itemType) {
      case ShopItemType.outfit:
        return CameraPresets.outfitPreview;
      case ShopItemType.accessory:
        return CameraPresets.accessoryPreview;
      case ShopItemType.environment:
        return CameraPresets.fullBodyPreview;
      default:
        return CameraPresets.fullBodyPreview;
    }
  }

  Future<void> _animateSlightZoom(double zoomFactor, Duration duration) async {
    final unityController = _ref.read(unityControllerProvider.notifier);
    final currentSettings = _ref.read(cameraSettingsProvider);
    final currentFov = currentSettings['fov'] as double? ?? 60.0;
    
    // Zoom in
    unityController.setCameraZoom(currentFov / zoomFactor, duration: Duration(milliseconds: duration.inMilliseconds ~/ 2));
    
    // Wait and zoom back out
    await Future.delayed(Duration(milliseconds: duration.inMilliseconds ~/ 2));
    unityController.setCameraZoom(currentFov, duration: Duration(milliseconds: duration.inMilliseconds ~/ 2));
  }

  Future<void> _animateCelebratoryMovement(Duration duration) async {
    final unityController = _ref.read(unityControllerProvider.notifier);
    final currentSettings = _ref.read(cameraSettingsProvider);
    final currentPos = currentSettings['position'] as Map<String, dynamic>? ?? {'x': 0.0, 'y': 1.0, 'z': 4.0};
    
    // Slight upward movement
    unityController.setCameraPosition(
      x: currentPos['x'],
      y: currentPos['y'] + 0.1,
      z: currentPos['z'],
      duration: Duration(milliseconds: duration.inMilliseconds ~/ 2),
    );
    
    // Return to original position
    await Future.delayed(Duration(milliseconds: duration.inMilliseconds ~/ 2));
    unityController.setCameraPosition(
      x: currentPos['x'],
      y: currentPos['y'],
      z: currentPos['z'],
      duration: Duration(milliseconds: duration.inMilliseconds ~/ 2),
    );
  }

  Future<void> _animateSubtleShake(Duration duration) async {
    final unityController = _ref.read(unityControllerProvider.notifier);
    final currentSettings = _ref.read(cameraSettingsProvider);
    final currentPos = currentSettings['position'] as Map<String, dynamic>? ?? {'x': 0.0, 'y': 1.0, 'z': 4.0};
    
    const shakeIntensity = 0.02;
    final shakeDuration = Duration(milliseconds: duration.inMilliseconds ~/ 4);
    
    // Shake sequence
    for (int i = 0; i < 4; i++) {
      final offsetX = (i % 2 == 0) ? shakeIntensity : -shakeIntensity;
      
      unityController.setCameraPosition(
        x: currentPos['x'] + offsetX,
        y: currentPos['y'],
        z: currentPos['z'],
        duration: shakeDuration,
      );
      
      await Future.delayed(shakeDuration);
    }
    
    // Return to center
    unityController.setCameraPosition(
      x: currentPos['x'],
      y: currentPos['y'],
      z: currentPos['z'],
      duration: shakeDuration,
    );
  }
}

// Provider for Unity Camera Controller
final unityCameraControllerProvider = Provider<UnityCameraController>((ref) {
  return UnityCameraController(ref);
});

// Convenience providers for camera state
final canEnterPreviewModeProvider = Provider<bool>((ref) {
  final isInitialized = ref.watch(unityInitializedProvider);
  final isTransitioning = ref.watch(isCameraTransitioningProvider);
  final currentMode = ref.watch(unityCameraModeProvider);
  
  return isInitialized && !isTransitioning && currentMode == CameraMode.defaultMode;
});

final canExitPreviewModeProvider = Provider<bool>((ref) {
  final isInitialized = ref.watch(unityInitializedProvider);
  final isTransitioning = ref.watch(isCameraTransitioningProvider);
  final currentMode = ref.watch(unityCameraModeProvider);
  
  return isInitialized && !isTransitioning && currentMode == CameraMode.preview;
});

final isInPreviewCameraModeProvider = Provider<bool>((ref) {
  return ref.watch(unityCameraModeProvider) == CameraMode.preview;
});