import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:just_audio/just_audio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Audio playback state
enum AudioPlaybackState {
  idle,
  loading,
  playing,
  paused,
  stopped,
  error,
}

/// Service for playing TTS audio chunks using just_audio
class AudioPlaybackService {
  final AudioPlayer _audioPlayer = AudioPlayer();
  
  // State management
  AudioPlaybackState _state = AudioPlaybackState.idle;
  final _stateController = StreamController<AudioPlaybackState>.broadcast();
  final _errorController = StreamController<String>.broadcast();
  
  // Audio chunk management
  final List<Uint8List> _audioChunks = [];
  bool _isStreamingComplete = false;
  String? _currentRequestId;
  
  // Getters
  Stream<AudioPlaybackState> get stateStream => _stateController.stream;
  Stream<String> get errorStream => _errorController.stream;
  AudioPlaybackState get currentState => _state;
  bool get isPlaying => _state == AudioPlaybackState.playing;
  bool get isLoading => _state == AudioPlaybackState.loading;

  /// Initialize the audio service
  Future<void> initialize() async {
    try {
      debugPrint('AudioPlaybackService: Initializing...');
      
      // Listen to player state changes
      _audioPlayer.playerStateStream.listen((playerState) {
        switch (playerState.processingState) {
          case ProcessingState.idle:
            _setState(AudioPlaybackState.idle);
            break;
          case ProcessingState.loading:
            _setState(AudioPlaybackState.loading);
            break;
          case ProcessingState.buffering:
            _setState(AudioPlaybackState.loading);
            break;
          case ProcessingState.ready:
            if (playerState.playing) {
              _setState(AudioPlaybackState.playing);
            } else {
              _setState(AudioPlaybackState.paused);
            }
            break;
          case ProcessingState.completed:
            _setState(AudioPlaybackState.stopped);
            _onPlaybackCompleted();
            break;
        }
      });

      // Listen to errors
      _audioPlayer.playbackEventStream.listen(
        null,
        onError: (error) {
          _handleError('Audio playback error: $error');
        },
      );

      debugPrint('AudioPlaybackService: Initialization complete');
    } catch (e) {
      _handleError('Failed to initialize audio service: $e');
    }
  }

  /// Start playing TTS audio chunks
  Future<void> startTTSPlayback(String requestId) async {
    try {
      debugPrint('AudioPlaybackService: Starting TTS playback for request: $requestId');
      
      // Stop any current playback
      await stopPlayback();
      
      // Reset state for new playback
      _currentRequestId = requestId;
      _audioChunks.clear();
      _isStreamingComplete = false;
      
      _setState(AudioPlaybackState.loading);
      
    } catch (e) {
      _handleError('Failed to start TTS playback: $e');
    }
  }

  /// Add audio chunk to the playback queue
  Future<void> addAudioChunk({
    required Uint8List audioData,
    required String chunkId,
    required bool isFinal,
    String? requestId,
  }) async {
    try {
      // Verify this chunk belongs to the current request
      if (requestId != null && requestId != _currentRequestId) {
        debugPrint('AudioPlaybackService: Ignoring chunk for different request: $requestId');
        return;
      }

      debugPrint('AudioPlaybackService: Adding audio chunk ${chunkId} (${audioData.length} bytes, final: $isFinal)');
      
      _audioChunks.add(audioData);
      
      if (isFinal) {
        _isStreamingComplete = true;
        await _playAudioChunks();
      } else if (_audioChunks.length == 1) {
        // Start playing immediately when we get the first chunk
        await _playAudioChunks();
      }
      
    } catch (e) {
      _handleError('Failed to add audio chunk: $e');
    }
  }

  /// Play the accumulated audio chunks
  Future<void> _playAudioChunks() async {
    if (_audioChunks.isEmpty) {
      debugPrint('AudioPlaybackService: No audio chunks to play');
      return;
    }

    try {
      debugPrint('AudioPlaybackService: Playing ${_audioChunks.length} audio chunks');
      
      // Concatenate all audio chunks
      final totalLength = _audioChunks.fold<int>(0, (sum, chunk) => sum + chunk.length);
      final concatenatedAudio = Uint8List(totalLength);
      
      int offset = 0;
      for (final chunk in _audioChunks) {
        concatenatedAudio.setRange(offset, offset + chunk.length, chunk);
        offset += chunk.length;
      }
      
      debugPrint('AudioPlaybackService: Total audio size: ${concatenatedAudio.length} bytes');
      
      // Create audio source from bytes
      final audioSource = AudioSource.uri(
        Uri.dataFromBytes(
          concatenatedAudio,
          mimeType: 'audio/wav', // Assuming WAV format from backend
        ),
      );
      
      // Set the audio source and play
      await _audioPlayer.setAudioSource(audioSource);
      await _audioPlayer.play();
      
      debugPrint('AudioPlaybackService: Audio playback started');
      
    } catch (e) {
      _handleError('Failed to play audio chunks: $e');
    }
  }

  /// Stop current playback
  Future<void> stopPlayback() async {
    try {
      if (_audioPlayer.playing) {
        await _audioPlayer.stop();
      }
      
      _audioChunks.clear();
      _isStreamingComplete = false;
      _currentRequestId = null;
      
      _setState(AudioPlaybackState.stopped);
      debugPrint('AudioPlaybackService: Playback stopped');
      
    } catch (e) {
      _handleError('Failed to stop playback: $e');
    }
  }

  /// Pause current playback
  Future<void> pausePlayback() async {
    try {
      if (_audioPlayer.playing) {
        await _audioPlayer.pause();
        debugPrint('AudioPlaybackService: Playback paused');
      }
    } catch (e) {
      _handleError('Failed to pause playback: $e');
    }
  }

  /// Resume paused playback
  Future<void> resumePlayback() async {
    try {
      if (!_audioPlayer.playing && _audioPlayer.processingState == ProcessingState.ready) {
        await _audioPlayer.play();
        debugPrint('AudioPlaybackService: Playback resumed');
      }
    } catch (e) {
      _handleError('Failed to resume playback: $e');
    }
  }

  /// Handle playback completion
  void _onPlaybackCompleted() {
    debugPrint('AudioPlaybackService: Playback completed');
    _audioChunks.clear();
    _isStreamingComplete = false;
    _currentRequestId = null;
  }

  /// Set state and notify listeners
  void _setState(AudioPlaybackState newState) {
    if (_state != newState) {
      _state = newState;
      _stateController.add(_state);
      debugPrint('AudioPlaybackService: State changed to $_state');
    }
  }

  /// Handle errors
  void _handleError(String error) {
    debugPrint('AudioPlaybackService Error: $error');
    _setState(AudioPlaybackState.error);
    _errorController.add(error);
  }

  /// Dispose resources
  void dispose() {
    debugPrint('AudioPlaybackService: Disposing...');
    _audioPlayer.dispose();
    _stateController.close();
    _errorController.close();
  }
}

/// Provider for AudioPlaybackService
final audioPlaybackServiceProvider = Provider<AudioPlaybackService>((ref) {
  final service = AudioPlaybackService();
  
  // Initialize the service
  service.initialize();
  
  // Dispose when provider is disposed
  ref.onDispose(() {
    service.dispose();
  });
  
  return service;
});

/// Provider for audio playback state
final audioPlaybackStateProvider = StreamProvider<AudioPlaybackState>((ref) {
  final service = ref.watch(audioPlaybackServiceProvider);
  return service.stateStream;
});
