import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/shop/shop_item_model.dart';
import '../models/shop/shop_preview_models.dart';
import '../services/storage/storage_service.dart';
import '../services/api/api_service.dart';
import 'auth_provider.dart';
import 'unity_provider.dart';

// Shop State
class ShopState {
  final List<ShopItemModel> environments;
  final List<ShopItemModel> outfits;
  final List<ShopItemModel> accessories;
  final List<ShopItemModel> companions;
  final List<ShopItemModel> pets;
  final bool isLoading;
  final String? error;

  const ShopState({
    this.environments = const [],
    this.outfits = const [],
    this.accessories = const [],
    this.companions = const [],
    this.pets = const [],
    this.isLoading = false,
    this.error,
  });

  ShopState copyWith({
    List<ShopItemModel>? environments,
    List<ShopItemModel>? outfits,
    List<ShopItemModel>? accessories,
    List<ShopItemModel>? companions,
    List<ShopItemModel>? pets,
    bool? isLoading,
    String? error,
  }) {
    return ShopState(
      environments: environments ?? this.environments,
      outfits: outfits ?? this.outfits,
      accessories: accessories ?? this.accessories,
      companions: companions ?? this.companions,
      pets: pets ?? this.pets,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }

  List<ShopItemModel> get allItems => [
    ...environments,
    ...outfits,
    ...accessories,
    ...companions,
    ...pets,
  ];
}

// Shop Notifier
class ShopNotifier extends StateNotifier<ShopState> {
  final ApiService _apiService;
  final Ref _ref;

  ShopNotifier(this._apiService, this._ref) : super(const ShopState()) {
    _loadShopItems();
  }

  Future<void> _loadShopItems() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Use mock data for now - easy to replace with API later
      print('Loading mock shop data...');
      
      // Simulate API delay
      await Future.delayed(const Duration(milliseconds: 500));
      
      final mockEnvironments = [
        ShopItemModel.environment(
          id: 'cozy_room',
          name: 'Cozy Room',
          description: 'A warm and comfortable living space',
          price: 0,
          rarity: ShopItemRarity.common,
          metadata: {'relationshipRequired': 1},
        ),
        ShopItemModel.environment(
          id: 'modern_apartment',
          name: 'Modern Apartment',
          description: 'Sleek and contemporary urban living',
          price: 100,
          rarity: ShopItemRarity.rare,
          metadata: {'relationshipRequired': 2},
        ),
        ShopItemModel.environment(
          id: 'garden_terrace',
          name: 'Garden Terrace',
          description: 'Beautiful outdoor space with plants',
          price: 150,
          rarity: ShopItemRarity.rare,
          metadata: {'relationshipRequired': 2},
        ),
        ShopItemModel.environment(
          id: 'starry_night',
          name: 'Starry Night',
          description: 'Magical nighttime sky view',
          price: 200,
          rarity: ShopItemRarity.epic,
          levelRequired: 5,
          metadata: {'relationshipRequired': 3},
        ),
      ];

      final mockOutfits = [
        ShopItemModel.outfit(
          id: 'casual_wear',
          name: 'Casual Wear',
          description: 'Comfortable everyday clothing',
          price: 0,
          rarity: ShopItemRarity.common,
          metadata: {'relationshipRequired': 1},
        ),
        ShopItemModel.outfit(
          id: 'elegant_dress',
          name: 'Elegant Dress',
          description: 'Beautiful formal attire',
          price: 80,
          rarity: ShopItemRarity.rare,
          metadata: {'relationshipRequired': 2},
        ),
        ShopItemModel.outfit(
          id: 'sporty_outfit',
          name: 'Sporty Outfit',
          description: 'Athletic and active wear',
          price: 60,
          rarity: ShopItemRarity.common,
          metadata: {'relationshipRequired': 1},
        ),
        ShopItemModel.outfit(
          id: 'party_dress',
          name: 'Party Dress',
          description: 'Glamorous outfit for special occasions',
          price: 200,
          rarity: ShopItemRarity.epic,
          levelRequired: 10,
          metadata: {'relationshipRequired': 3},
        ),
        ShopItemModel.outfit(
          id: 'intimate_lingerie',
          name: 'Intimate Lingerie',
          description: 'Special intimate wear',
          price: 300,
          rarity: ShopItemRarity.legendary,
          levelRequired: 15,
          metadata: {'relationshipRequired': 4},
        ),
      ];

      final mockAccessories = [
        ShopItemModel.accessory(
          id: 'simple_earrings',
          name: 'Simple Earrings',
          description: 'Classic and elegant earrings',
          price: 30,
          rarity: ShopItemRarity.common,
          metadata: {'relationshipRequired': 1},
        ),
        ShopItemModel.accessory(
          id: 'stylish_glasses',
          name: 'Stylish Glasses',
          description: 'Trendy eyewear accessory',
          price: 50,
          rarity: ShopItemRarity.common,
          metadata: {'relationshipRequired': 1},
        ),
        ShopItemModel.accessory(
          id: 'flower_crown',
          name: 'Flower Crown',
          description: 'Beautiful floral headpiece',
          price: 75,
          rarity: ShopItemRarity.rare,
          metadata: {'relationshipRequired': 2},
        ),
        ShopItemModel.accessory(
          id: 'diamond_necklace',
          name: 'Diamond Necklace',
          description: 'Luxurious sparkling jewelry',
          price: 300,
          rarity: ShopItemRarity.legendary,
          levelRequired: 20,
          metadata: {'relationshipRequired': 4},
        ),
      ];

      final mockCompanions = [
        ShopItemModel(
          id: 'alex_playful',
          name: 'Alex',
          description: 'A fun-loving companion who brings energy and excitement to every conversation.',
          type: ShopItemType.companion,
          rarity: ShopItemRarity.rare,
          price: 200,
          levelRequired: 5,
          metadata: {
            'personality': 'playfulCompanion',
            'relationshipRequired': 2,
          },
        ),
        ShopItemModel(
          id: 'sage_mentor',
          name: 'Sage',
          description: 'A wise mentor who offers thoughtful guidance and deep insights.',
          type: ShopItemType.companion,
          rarity: ShopItemRarity.epic,
          price: 400,
          levelRequired: 10,
          metadata: {
            'personality': 'wiseMentor',
            'relationshipRequired': 3,
          },
        ),
        ShopItemModel(
          id: 'valentine_romantic',
          name: 'Valentine',
          description: 'A romantic companion for intimate conversations and emotional connection.',
          type: ShopItemType.companion,
          rarity: ShopItemRarity.legendary,
          price: 600,
          levelRequired: 15,
          metadata: {
            'personality': 'romanticPartner',
            'relationshipRequired': 4,
          },
        ),
        ShopItemModel(
          id: 'dr_hope_therapist',
          name: 'Dr. Hope',
          description: 'A supportive therapist focused on your mental health and wellbeing.',
          type: ShopItemType.companion,
          rarity: ShopItemRarity.legendary,
          price: 800,
          levelRequired: 20,
          metadata: {
            'personality': 'supportiveTherapist',
            'relationshipRequired': 4,
            'premiumRequired': true,
          },
        ),
      ];

      // Mock pet data
      final mockPets = [
        ShopItemModel.pet(
          id: 'fluffy_cat',
          name: 'Fluffy Cat',
          description: 'A cute and playful feline companion that purrs when you talk to it.',
          price: 150,
          rarity: ShopItemRarity.common,
          metadata: {'petType': 'cat', 'behavior': 'playful', 'sound': 'purr'},
        ),
        ShopItemModel.pet(
          id: 'loyal_dog',
          name: 'Loyal Dog',
          description: 'A friendly and loyal canine that follows you everywhere.',
          price: 200,
          rarity: ShopItemRarity.rare,
          metadata: {'petType': 'dog', 'behavior': 'loyal', 'sound': 'bark'},
        ),
        ShopItemModel.pet(
          id: 'colorful_parrot',
          name: 'Colorful Parrot',
          description: 'A vibrant parrot that can learn to repeat your words.',
          price: 250,
          rarity: ShopItemRarity.rare,
          metadata: {'petType': 'bird', 'behavior': 'talkative', 'sound': 'squawk'},
        ),
        ShopItemModel.pet(
          id: 'mini_dragon',
          name: 'Mini Dragon',
          description: 'A magical miniature dragon that breathes tiny flames.',
          price: 500,
          rarity: ShopItemRarity.epic,
          levelRequired: 10,
          metadata: {'petType': 'fantasy', 'behavior': 'protective', 'sound': 'roar'},
        ),
        ShopItemModel.pet(
          id: 'spirit_fox',
          name: 'Spirit Fox',
          description: 'A mystical nine-tailed fox with magical abilities.',
          price: 800,
          rarity: ShopItemRarity.legendary,
          levelRequired: 20,
          metadata: {'petType': 'fantasy', 'behavior': 'wise', 'sound': 'ethereal'},
        ),
      ];

      print('Mock data loaded: ${mockEnvironments.length} environments, ${mockOutfits.length} outfits, ${mockAccessories.length} accessories, ${mockCompanions.length} companions, ${mockPets.length} pets');

      state = state.copyWith(
        environments: mockEnvironments,
        outfits: mockOutfits,
        accessories: mockAccessories,
        companions: mockCompanions,
        pets: mockPets,
        isLoading: false,
      );
    } catch (e) {
      print('Error loading shop items: $e');
      // Fallback to default items if mock data fails
      state = state.copyWith(
        environments: DefaultShopItems.environments,
        outfits: DefaultShopItems.outfits,
        accessories: DefaultShopItems.accessories,
        companions: DefaultShopItems.companions,
        pets: DefaultShopItems.pets,
        isLoading: false,
        error: null,
      );
    }
  }

  Future<void> purchaseItem(String itemId) async {
    final user = _ref.read(currentUserProvider);
    if (user == null) return;

    final item = state.allItems.firstWhere(
      (item) => item.id == itemId,
      orElse: () => throw Exception('Item not found'),
    );

    // Check if user can afford the item
    if (user.progress.hearts < item.price) {
      state = state.copyWith(error: 'Not enough ${item.price} hearts');
      return;
    }

    // Check level requirement
    if (user.progress.level < item.levelRequired) {
      state = state.copyWith(error: 'Level ${item.levelRequired} required');
      return;
    }

    // Check if already owned
    final isOwned = _isItemOwned(user, item);
    if (isOwned) {
      state = state.copyWith(error: 'Item already owned');
      return;
    }

    try {
      state = state.copyWith(isLoading: true, error: null);

      // Deduct hearts
      final success = await StorageService.spendUserHearts(item.price);
      if (!success) {
        state = state.copyWith(
          isLoading: false,
          error: 'Not enough hearts',
        );
        return;
      }

      // Add item to user's inventory
      await _addItemToInventory(user, item);

      // Apply item if it's an environment
      if (item.type == ShopItemType.environment) {
        await StorageService.setSelectedEnvironment(item.id);
        _ref.read(unityControllerProvider.notifier).changeEnvironment(item.id);
      }

      // Try to sync with backend
      try {
        await _apiService.purchaseItem(user.id, item.id);
      } catch (e) {
        // Continue even if backend sync fails
        print('Failed to sync purchase with backend: $e');
      }

      // Update auth state to reflect changes
      _ref.read(authProvider.notifier).updateUserProfile();

      state = state.copyWith(isLoading: false);
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Purchase failed: ${e.toString()}',
      );
    }
  }

  bool _isItemOwned(user, ShopItemModel item) {
    switch (item.type) {
      case ShopItemType.environment:
        return user.ownedEnvironments.contains(item.id);
      case ShopItemType.outfit:
        return user.ownedOutfits.contains(item.id);
      case ShopItemType.accessory:
        // For accessories, check in metadata or separate list
        return user.ownedOutfits.contains(item.id); // Simplified for now
      case ShopItemType.companion:
        // For now, store companions in ownedOutfits (simplified)
        // In a real implementation, this would be a separate companions list
        return user.ownedOutfits.contains(item.id);
      default:
        return false;
    }
  }

  Future<void> _addItemToInventory(user, ShopItemModel item) async {
    switch (item.type) {
      case ShopItemType.environment:
        await StorageService.addOwnedEnvironment(item.id);
        break;
      case ShopItemType.outfit:
      case ShopItemType.accessory:
      case ShopItemType.companion:
        // Add to owned outfits list (simplified)
        final updatedUser = user.copyWith(
          ownedOutfits: [...user.ownedOutfits, item.id],
        );
        await StorageService.saveUser(updatedUser);
        break;
      default:
        break;
    }
  }

  Future<void> refreshShop() async {
    await _loadShopItems();
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  // Get items by category
  List<ShopItemModel> getItemsByType(ShopItemType type) {
    return state.allItems.where((item) => item.type == type).toList();
  }

  // Get items by rarity
  List<ShopItemModel> getItemsByRarity(ShopItemRarity rarity) {
    return state.allItems.where((item) => item.rarity == rarity).toList();
  }

  // Get affordable items for user
  List<ShopItemModel> getAffordableItems(int userHearts) {
    return state.allItems.where((item) => item.price <= userHearts).toList();
  }

  // Get items available for user level
  List<ShopItemModel> getAvailableItems(int userLevel) {
    return state.allItems.where((item) => item.levelRequired <= userLevel).toList();
  }

  // Get items filtered by relationship level
  List<ShopItemModel> getItemsByRelationshipLevel(int relationshipLevel) {
    return state.allItems.where((item) {
      final requiredLevel = item.metadata['relationshipRequired'] as int? ?? 1;
      return relationshipLevel >= requiredLevel;
    }).toList();
  }

  // Check if item is locked based on relationship
  bool isItemLockedByRelationship(ShopItemModel item, int relationshipLevel) {
    final requiredLevel = item.metadata['relationshipRequired'] as int? ?? 1;
    return relationshipLevel < requiredLevel;
  }
}

// Providers
final shopProvider = StateNotifierProvider<ShopNotifier, ShopState>((ref) {
  return ShopNotifier(ref.read(apiServiceProvider), ref);
});

final shopEnvironmentsProvider = Provider<List<ShopItemModel>>((ref) {
  return ref.watch(shopProvider).environments;
});

final shopOutfitsProvider = Provider<List<ShopItemModel>>((ref) {
  return ref.watch(shopProvider).outfits;
});

final shopAccessoriesProvider = Provider<List<ShopItemModel>>((ref) {
  return ref.watch(shopProvider).accessories;
});

final shopCompanionsProvider = Provider<List<ShopItemModel>>((ref) {
  return ref.watch(shopProvider).companions;
});

final shopPetsProvider = Provider<List<ShopItemModel>>((ref) {
  return ref.watch(shopProvider).pets;
});

final isShopLoadingProvider = Provider<bool>((ref) {
  return ref.watch(shopProvider).isLoading;
});

final shopErrorProvider = Provider<String?>((ref) {
  return ref.watch(shopProvider).error;
});

// Utility providers
final affordableItemsProvider = Provider<List<ShopItemModel>>((ref) {
  final user = ref.watch(currentUserProvider);
  final shopState = ref.watch(shopProvider);
  
  if (user == null) return [];
  
  return shopState.allItems
      .where((item) => item.price <= user.progress.hearts)
      .toList();
});

final availableItemsProvider = Provider<List<ShopItemModel>>((ref) {
  final user = ref.watch(currentUserProvider);
  final shopState = ref.watch(shopProvider);
  
  if (user == null) return [];
  
  return shopState.allItems
      .where((item) => item.levelRequired <= user.progress.level)
      .toList();
});

final ownedItemsProvider = Provider<List<ShopItemModel>>((ref) {
  final user = ref.watch(currentUserProvider);
  final shopState = ref.watch(shopProvider);
  
  if (user == null) return [];
  
  return shopState.allItems.where((item) {
    switch (item.type) {
      case ShopItemType.environment:
        return user.ownedEnvironments.contains(item.id);
      case ShopItemType.outfit:
      case ShopItemType.accessory:
      case ShopItemType.companion:
        return user.ownedOutfits.contains(item.id);
      default:
        return false;
    }
  }).toList();
});
