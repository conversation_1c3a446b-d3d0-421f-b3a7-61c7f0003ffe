import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../core/constants/app_constants.dart';
import 'service_providers.dart';
import '../services/storage/storage_service.dart';
import '../services/api/api_service.dart';
import '../models/chat/message_model.dart';
import '../models/user/user_model.dart';

class ProgressState {
  final List<String> achievements;
  final int daysActive;
  final int totalHeartsEarned;
  final int conversationCount;
  final String favoriteTimeOfDay;
  final Map<String, int> dailyStats;
  final bool isLoading;
  final String? error;

  const ProgressState({
    this.achievements = const [],
    this.daysActive = 0,
    this.totalHeartsEarned = 0,
    this.conversationCount = 0,
    this.favoriteTimeOfDay = 'Evening',
    this.dailyStats = const {},
    this.isLoading = false,
    this.error,
  });

  ProgressState copyWith({
    List<String>? achievements,
    int? daysActive,
    int? totalHeartsEarned,
    int? conversationCount,
    String? favoriteTimeOfDay,
    Map<String, int>? dailyStats,
    bool? isLoading,
    String? error,
  }) {
    return ProgressState(
      achievements: achievements ?? this.achievements,
      daysActive: daysActive ?? this.daysActive,
      totalHeartsEarned: totalHeartsEarned ?? this.totalHeartsEarned,
      conversationCount: conversationCount ?? this.conversationCount,
      favoriteTimeOfDay: favoriteTimeOfDay ?? this.favoriteTimeOfDay,
      dailyStats: dailyStats ?? this.dailyStats,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

class ProgressNotifier extends StateNotifier<ProgressState> {
  final ApiService _apiService;

  ProgressNotifier(this._apiService) : super(const ProgressState()) {
    _loadProgress();
  }

  Future<void> _loadProgress() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // Load from local storage first
      final achievements = await StorageService.getAchievements();
      final stats = await _loadLocalStats();

      state = state.copyWith(
        achievements: achievements,
        daysActive: stats['daysActive'] ?? 0,
        totalHeartsEarned: stats['totalHeartsEarned'] ?? 0,
        conversationCount: stats['conversationCount'] ?? 0,
        favoriteTimeOfDay: stats['favoriteTimeOfDay'] ?? 'Evening',
        dailyStats: stats['dailyStats'] ?? {},
        isLoading: false,
      );

      // Sync with backend
      await _syncWithBackend();
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load progress: $e',
      );
    }
  }

  Future<Map<String, dynamic>> _loadLocalStats() async {
    try {
      final user = StorageService.getCurrentUser();
      if (user == null) return {};

      final now = DateTime.now();
      final firstLoginDate = user.createdAt;
      final daysActive = now.difference(firstLoginDate).inDays + 1;

      // Calculate total hearts earned (from progress history)
      final totalHeartsEarned = user.progress.hearts + (user.progress.level * 50);

      // Get conversation count from messages
      final conversations = await StorageService.getAllConversations();
      final conversationCount = conversations.length;

      // Analyze favorite time of day from message timestamps
      final favoriteTimeOfDay = await _analyzeFavoriteTimeOfDay();

      // Get daily stats
      final dailyStats = await _getDailyStats();

      return {
        'daysActive': daysActive,
        'totalHeartsEarned': totalHeartsEarned,
        'conversationCount': conversationCount,
        'favoriteTimeOfDay': favoriteTimeOfDay,
        'dailyStats': dailyStats,
      };
    } catch (e) {
      return {};
    }
  }

  Future<String> _analyzeFavoriteTimeOfDay() async {
    try {
      final conversations = await StorageService.getAllConversations();
      final hourCounts = <int, int>{};

      for (final conversation in conversations) {
        final messages = conversation['messages'] as List<MessageModel>;
        for (final message in messages) {
          final hour = message.timestamp.hour;
          hourCounts[hour] = (hourCounts[hour] ?? 0) + 1;
        }
      }

      if (hourCounts.isEmpty) return 'Evening';

      final mostActiveHour = hourCounts.entries
          .reduce((a, b) => a.value > b.value ? a : b)
          .key;

      if (mostActiveHour >= 6 && mostActiveHour < 12) {
        return 'Morning';
      } else if (mostActiveHour >= 12 && mostActiveHour < 18) {
        return 'Afternoon';
      } else {
        return 'Evening';
      }
    } catch (e) {
      return 'Evening';
    }
  }

  Future<Map<String, int>> _getDailyStats() async {
    try {
      final conversations = await StorageService.getAllConversations();
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      
      int todayMessages = 0;
      int todayVoiceMessages = 0;
      int todayTimeSpent = 0;

      for (final conversation in conversations) {
        final messages = conversation['messages'] as List<MessageModel>;
        for (final message in messages) {
          final messageDate = DateTime(
            message.timestamp.year,
            message.timestamp.month,
            message.timestamp.day,
          );

          if (messageDate == today) {
            todayMessages++;
            if (message.type == MessageType.voice) {
              todayVoiceMessages++;
            }
            // Estimate time spent (rough calculation)
            todayTimeSpent += 30; // 30 seconds per message
          }
        }
      }

      return {
        'todayMessages': todayMessages,
        'todayVoiceMessages': todayVoiceMessages,
        'todayTimeSpent': todayTimeSpent ~/ 60, // Convert to minutes
      };
    } catch (e) {
      return {};
    }
  }

  Future<void> _syncWithBackend() async {
    try {
      final user = StorageService.getCurrentUser();
      if (user == null) return;
      
      // Sync progress with backend
      final response = await _apiService.getUserProgress(user.id);
      if (response['success'] == true) {
        final backendData = response['data'];
        
        state = state.copyWith(
          achievements: List<String>.from(backendData['achievements'] ?? []),
          daysActive: backendData['daysActive'] ?? state.daysActive,
          totalHeartsEarned: backendData['totalHeartsEarned'] ?? state.totalHeartsEarned,
        );
      }
    } catch (e) {
      // Backend sync failed, continue with local data
      print('Backend sync failed: $e');
    }
  }

  Future<void> updateProgress(UserProgress progress) async {
    try {
      // Update local state
      final user = StorageService.getCurrentUser();
      if (user == null) return;

      // Create a new progress object instead of modifying the final field directly
      final updatedProgress = UserProgress(
        xp: progress.xp,
        level: progress.level,
        hearts: progress.hearts,
        messagesCount: progress.messagesCount,
        voiceMessagesCount: progress.voiceMessagesCount,
        lastDailyBonus: progress.lastDailyBonus,
        achievements: progress.achievements,
        totalTimeSpent: progress.totalTimeSpent,
      );

      final updatedUser = user.copyWith(progress: updatedProgress);
      await StorageService.saveUser(updatedUser);

      // Sync with backend
      await _apiService.updateUserProgress(user.id, updatedProgress.toJson());

      // Check for new achievements
      await _checkForNewAchievements(updatedProgress);
    } catch (e) {
      state = state.copyWith(error: 'Failed to update progress: $e');
    }
  }

  Future<void> _checkForNewAchievements(UserProgress progress) async {
    final newAchievements = <String>[];

    // Check various achievement conditions
    if (progress.messagesCount >= 1 && !state.achievements.contains('first_chat')) {
      newAchievements.add('first_chat');
    }

    if (progress.voiceMessagesCount >= 1 && !state.achievements.contains('voice_pioneer')) {
      newAchievements.add('voice_pioneer');
    }

    if (progress.level >= 5 && !state.achievements.contains('level_5')) {
      newAchievements.add('level_5');
    }

    if (progress.level >= 10 && !state.achievements.contains('level_10')) {
      newAchievements.add('level_10');
    }

    if (progress.messagesCount >= 100 && !state.achievements.contains('chatterbox')) {
      newAchievements.add('chatterbox');
    }

    // Check shop-related achievements
    final user = StorageService.getCurrentUser();
    if (user != null) {
      final totalPurchases = user.ownedEnvironments.length + user.ownedOutfits.length;
      if (totalPurchases >= 5 && !state.achievements.contains('shopaholic')) {
        newAchievements.add('shopaholic');
      }
    }

    if (newAchievements.isNotEmpty) {
      final updatedAchievements = [...state.achievements, ...newAchievements];
      state = state.copyWith(achievements: updatedAchievements);

      // Save to storage
      await StorageService.saveAchievements(updatedAchievements);

      // Notify about new achievements
      for (final achievement in newAchievements) {
        await _showAchievementNotification(achievement);
      }
    }
  }

  Future<void> _showAchievementNotification(String achievementId) async {
    // TODO: Show achievement notification/popup
    print('New achievement unlocked: $achievementId');
  }

  int _calculateLevel(int xp) {
    // Level calculation: Level = sqrt(XP / 100)
    return (xp / AppConstants.xpPerLevel).floor() + 1;
  }

  Future<void> addXP(int xp, String reason) async {
    try {
      final user = StorageService.getCurrentUser();
      if (user == null) return;

      final updatedProgress = user.progress.copyWith(
        xp: user.progress.xp + xp,
      );

      // Check for level up
      final newLevel = _calculateLevel(updatedProgress.xp);
      if (newLevel > user.progress.level) {
        final heartsReward = (newLevel - user.progress.level) * AppConstants.heartsPerLevel;
        final finalProgress = updatedProgress.copyWith(
          hearts: updatedProgress.hearts + heartsReward,
          level: newLevel,
        );

        // Show level up notification
        await _showLevelUpNotification(newLevel, heartsReward);
        
        // Update user
        final updatedUser = user.copyWith(progress: finalProgress);
        await StorageService.saveUser(updatedUser);

        // Update progress tracking
        await updateProgress(finalProgress);
      } else {
        // Update user
        final updatedUser = user.copyWith(progress: updatedProgress);
        await StorageService.saveUser(updatedUser);

        // Update progress tracking
        await updateProgress(updatedProgress);
      }
    } catch (e) {
      print('Failed to add XP: $e');
    }
  }

  Future<void> _showLevelUpNotification(int newLevel, int heartsReward) async {
    // TODO: Show level up notification/popup
    print('Level up! New level: $newLevel, Hearts reward: $heartsReward');
  }

  Future<void> recordDailyActivity() async {
    try {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      
      // Record daily login
      await StorageService.recordDailyLogin(today);

      // Update daily stats
      final dailyStats = await _getDailyStats();
      state = state.copyWith(dailyStats: dailyStats);
    } catch (e) {
      print('Failed to record daily activity: $e');
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  Future<void> refresh() async {
    await _loadProgress();
  }
}

final progressProvider = StateNotifierProvider<ProgressNotifier, ProgressState>((ref) {
  final apiService = ref.watch(apiServiceProvider);
  return ProgressNotifier(apiService);
});

// Providers for specific progress data
final achievementsProvider = Provider<List<String>>((ref) {
  return ref.watch(progressProvider).achievements;
});

final dailyStatsProvider = Provider<Map<String, int>>((ref) {
  return ref.watch(progressProvider).dailyStats;
});

final progressLoadingProvider = Provider<bool>((ref) {
  return ref.watch(progressProvider).isLoading;
});

final progressErrorProvider = Provider<String?>((ref) {
  return ref.watch(progressProvider).error;
});
