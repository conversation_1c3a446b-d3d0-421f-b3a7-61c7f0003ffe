import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:async';

import '../core/constants/app_constants.dart';
import '../services/storage/storage_service.dart';
import '../services/api/api_service.dart';
import 'auth_provider.dart';

// Voice State
class VoiceState {
  final bool isListening;
  final bool isSpeaking;
  final bool isInitialized;
  final String recognizedText;
  final double soundLevel;
  final bool hasPermission;
  final String? error;
  final List<String> availableVoices;
  final String selectedVoice;

  const VoiceState({
    this.isListening = false,
    this.isSpeaking = false,
    this.isInitialized = false,
    this.recognizedText = '',
    this.soundLevel = 0.0,
    this.hasPermission = false,
    this.error,
    this.availableVoices = const [],
    this.selectedVoice = AppConstants.defaultVoice,
  });

  VoiceState copyWith({
    bool? isListening,
    bool? isSpeaking,
    bool? isInitialized,
    String? recognizedText,
    double? soundLevel,
    bool? hasPermission,
    String? error,
    List<String>? availableVoices,
    String? selectedVoice,
  }) {
    return VoiceState(
      isListening: isListening ?? this.isListening,
      isSpeaking: isSpeaking ?? this.isSpeaking,
      isInitialized: isInitialized ?? this.isInitialized,
      recognizedText: recognizedText ?? this.recognizedText,
      soundLevel: soundLevel ?? this.soundLevel,
      hasPermission: hasPermission ?? this.hasPermission,
      error: error ?? this.error,
      availableVoices: availableVoices ?? this.availableVoices,
      selectedVoice: selectedVoice ?? this.selectedVoice,
    );
  }
}

// Voice Notifier
class VoiceNotifier extends StateNotifier<VoiceState> {
  final SpeechToText _speechToText = SpeechToText();
  final FlutterTts _flutterTts = FlutterTts();
  final ApiService _apiService;
  
  Timer? _silenceTimer;
  StreamSubscription? _soundLevelSubscription;

  VoiceNotifier(this._apiService) : super(const VoiceState()) {
    _initialize();
  }

  Future<void> _initialize() async {
    try {
      // Request microphone permission
      final permission = await Permission.microphone.request();
      final hasPermission = permission == PermissionStatus.granted;

      if (!hasPermission) {
        state = state.copyWith(
          error: AppConstants.voicePermissionError,
          hasPermission: false,
        );
        return;
      }

      // Initialize Speech-to-Text
      final sttAvailable = await _speechToText.initialize(
        onStatus: _onSpeechStatus,
        onError: _onSpeechError,
      );

      // Initialize Text-to-Speech
      await _initializeTts();

      // Get available voices
      final voices = await _flutterTts.getVoices;
      final voiceList = voices?.map((voice) => voice['name'] as String).toList() ?? [];

      state = state.copyWith(
        isInitialized: sttAvailable,
        hasPermission: hasPermission,
        availableVoices: voiceList,
      );

    } catch (e) {
      state = state.copyWith(
        error: 'Failed to initialize voice services: $e',
        isInitialized: false,
      );
    }
  }

  Future<void> _initializeTts() async {
    await _flutterTts.setLanguage('en-US');
    await _flutterTts.setSpeechRate(AppConstants.defaultSpeechRate);
    await _flutterTts.setPitch(AppConstants.defaultPitch);
    
    // Set voice if available
    final voices = await _flutterTts.getVoices;
    if (voices != null && voices.isNotEmpty) {
      // Try to find the default voice or use the first available
      final defaultVoice = voices.firstWhere(
        (voice) => voice['name'] == AppConstants.defaultVoice,
        orElse: () => voices.first,
      );
      await _flutterTts.setVoice(defaultVoice);
    }

    // Set up TTS callbacks
    _flutterTts.setStartHandler(() {
      state = state.copyWith(isSpeaking: true);
    });

    _flutterTts.setCompletionHandler(() {
      state = state.copyWith(isSpeaking: false);
    });

    _flutterTts.setErrorHandler((message) {
      state = state.copyWith(
        isSpeaking: false,
        error: 'TTS Error: $message',
      );
    });
  }

  // Speech-to-Text Methods
  Future<void> startListening() async {
    if (!state.isInitialized || !state.hasPermission) {
      await _initialize();
      if (!state.isInitialized) return;
    }

    if (state.isListening) return;

    try {
      state = state.copyWith(
        isListening: true,
        recognizedText: '',
        error: null,
      );

      await _speechToText.listen(
        onResult: _onSpeechResult,
        listenFor: const Duration(seconds: 30),
        pauseFor: const Duration(seconds: 3),
        partialResults: true,
        localeId: 'en_US',
        onSoundLevelChange: _onSoundLevelChange,
      );

      // Set up silence detection
      _startSilenceTimer();

    } catch (e) {
      state = state.copyWith(
        isListening: false,
        error: 'Failed to start listening: $e',
      );
    }
  }

  Future<void> stopListening() async {
    if (!state.isListening) return;

    try {
      await _speechToText.stop();
      _cancelSilenceTimer();
      
      state = state.copyWith(isListening: false);
    } catch (e) {
      state = state.copyWith(
        isListening: false,
        error: 'Failed to stop listening: $e',
      );
    }
  }

  void _onSpeechResult(result) {
    final recognizedText = result.recognizedWords as String;
    state = state.copyWith(recognizedText: recognizedText);

    // Reset silence timer on new speech
    if (recognizedText.isNotEmpty) {
      _startSilenceTimer();
    }
  }

  void _onSpeechStatus(String status) {
    print('Speech status: $status');
    
    if (status == 'done' || status == 'notListening') {
      state = state.copyWith(isListening: false);
      _cancelSilenceTimer();
    }
  }

  void _onSpeechError(error) {
    print('Speech error: $error');
    state = state.copyWith(
      isListening: false,
      error: 'Speech recognition error: ${error.errorMsg}',
    );
    _cancelSilenceTimer();
  }

  void _onSoundLevelChange(double level) {
    state = state.copyWith(soundLevel: level);
  }

  void _startSilenceTimer() {
    _cancelSilenceTimer();
    _silenceTimer = Timer(const Duration(seconds: 2), () {
      if (state.isListening && state.recognizedText.isNotEmpty) {
        stopListening();
      }
    });
  }

  void _cancelSilenceTimer() {
    _silenceTimer?.cancel();
    _silenceTimer = null;
  }

  // Text-to-Speech Methods
  Future<void> speakText(String text) async {
    if (text.isEmpty) return;

    try {
      // Stop any current speech
      await _flutterTts.stop();

      state = state.copyWith(isSpeaking: true, error: null);

      // Use streaming TTS if available, otherwise use local TTS
      if (StorageService.isVoiceEnabled()) {
        try {
          // This will be handled by the TTS streaming service in the chat provider
          // For now, fallback to local TTS
          await _flutterTts.speak(text);
        } catch (e) {
          // Fallback to local TTS
          await _flutterTts.speak(text);
        }
      } else {
        await _flutterTts.speak(text);
      }

    } catch (e) {
      state = state.copyWith(
        isSpeaking: false,
        error: 'Failed to speak text: $e',
      );
    }
  }

  Future<void> stopSpeaking() async {
    try {
      await _flutterTts.stop();
      state = state.copyWith(isSpeaking: false);
    } catch (e) {
      state = state.copyWith(
        isSpeaking: false,
        error: 'Failed to stop speaking: $e',
      );
    }
  }

  Future<void> playAudio(String audioPath) async {
    try {
      state = state.copyWith(isSpeaking: true);
      
      // TODO: Implement audio playback using audio_players package
      // For now, we'll use TTS as fallback
      
      state = state.copyWith(isSpeaking: false);
    } catch (e) {
      state = state.copyWith(
        isSpeaking: false,
        error: 'Failed to play audio: $e',
      );
    }
  }

  // Voice Settings
  Future<void> setVoice(String voiceName) async {
    try {
      final voices = await _flutterTts.getVoices;
      final voice = voices?.firstWhere(
        (v) => v['name'] == voiceName,
        orElse: () => null,
      );

      if (voice != null) {
        await _flutterTts.setVoice(voice);
        state = state.copyWith(selectedVoice: voiceName);
      }
    } catch (e) {
      state = state.copyWith(error: 'Failed to set voice: $e');
    }
  }

  Future<void> setSpeechRate(double rate) async {
    try {
      await _flutterTts.setSpeechRate(rate);
    } catch (e) {
      state = state.copyWith(error: 'Failed to set speech rate: $e');
    }
  }

  Future<void> setPitch(double pitch) async {
    try {
      await _flutterTts.setPitch(pitch);
    } catch (e) {
      state = state.copyWith(error: 'Failed to set pitch: $e');
    }
  }

  // Utility Methods
  String get finalRecognizedText => state.recognizedText;
  
  bool get canListen => state.isInitialized && state.hasPermission && !state.isListening;
  
  bool get canSpeak => state.isInitialized && !state.isSpeaking;

  void clearError() {
    state = state.copyWith(error: null);
  }

  void clearRecognizedText() {
    state = state.copyWith(recognizedText: '');
  }

  @override
  void dispose() {
    _cancelSilenceTimer();
    _soundLevelSubscription?.cancel();
    _speechToText.cancel();
    _flutterTts.stop();
    super.dispose();
  }
}

// Providers
final voiceProvider = StateNotifierProvider<VoiceNotifier, VoiceState>((ref) {
  return VoiceNotifier(ref.read(apiServiceProvider));
});

final isListeningProvider = Provider<bool>((ref) {
  return ref.watch(voiceProvider).isListening;
});

final isSpeakingProvider = Provider<bool>((ref) {
  return ref.watch(voiceProvider).isSpeaking;
});

final recognizedTextProvider = Provider<String>((ref) {
  return ref.watch(voiceProvider).recognizedText;
});

final soundLevelProvider = Provider<double>((ref) {
  return ref.watch(voiceProvider).soundLevel;
});

final voicePermissionProvider = Provider<bool>((ref) {
  return ref.watch(voiceProvider).hasPermission;
});

final voiceErrorProvider = Provider<String?>((ref) {
  return ref.watch(voiceProvider).error;
});

final availableVoicesProvider = Provider<List<String>>((ref) {
  return ref.watch(voiceProvider).availableVoices;
});

final selectedVoiceProvider = Provider<String>((ref) {
  return ref.watch(voiceProvider).selectedVoice;
});
