import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:equatable/equatable.dart';
import '../models/shop/shop_item_model.dart';
import '../models/user/user_model.dart';
import '../services/storage/storage_service.dart';
import 'auth_provider.dart';
import 'unity_provider.dart';
import 'shop_provider.dart';

// Collection Detail State
class CollectionDetailState extends Equatable {
  final bool isInDetailMode;
  final ShopItemModel? selectedItem;
  final ShopItemType? currentCategory;
  final List<ShopItemModel> ownedItems;
  final List<ShopItemModel> categoryItems;
  final bool isTransitioning;
  final String? error;
  final bool isEquipping;

  const CollectionDetailState({
    this.isInDetailMode = false,
    this.selectedItem,
    this.currentCategory,
    this.ownedItems = const [],
    this.categoryItems = const [],
    this.isTransitioning = false,
    this.error,
    this.isEquipping = false,
  });

  CollectionDetailState copyWith({
    bool? isInDetailMode,
    ShopItemModel? selectedItem,
    ShopItemType? currentCategory,
    List<ShopItemModel>? ownedItems,
    List<ShopItemModel>? categoryItems,
    bool? isTransitioning,
    String? error,
    bool? isEquipping,
  }) {
    return CollectionDetailState(
      isInDetailMode: isInDetailMode ?? this.isInDetailMode,
      selectedItem: selectedItem ?? this.selectedItem,
      currentCategory: currentCategory ?? this.currentCategory,
      ownedItems: ownedItems ?? this.ownedItems,
      categoryItems: categoryItems ?? this.categoryItems,
      isTransitioning: isTransitioning ?? this.isTransitioning,
      error: error ?? this.error,
      isEquipping: isEquipping ?? this.isEquipping,
    );
  }

  @override
  List<Object?> get props => [
        isInDetailMode,
        selectedItem,
        currentCategory,
        ownedItems,
        categoryItems,
        isTransitioning,
        error,
        isEquipping,
      ];
}

// Collection Detail Notifier
class CollectionDetailNotifier extends StateNotifier<CollectionDetailState> {
  final Ref _ref;

  CollectionDetailNotifier(this._ref) : super(const CollectionDetailState());

  // Enter detail mode for a specific item
  Future<void> enterDetailMode(ShopItemModel item) async {
    if (state.isInDetailMode) return;

    state = state.copyWith(isTransitioning: true);

    try {
      // Get all owned items for the same category
      final user = _ref.read(currentUserProvider);
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final shopState = _ref.read(shopProvider);
      final categoryItems = _getCategoryItems(shopState, item.type);
      final ownedItems = _getOwnedItemsForCategory(user, categoryItems, item.type);

      // Update state
      state = state.copyWith(
        isInDetailMode: true,
        selectedItem: item,
        currentCategory: item.type,
        categoryItems: categoryItems,
        ownedItems: ownedItems,
        isTransitioning: false,
        error: null,
      );
    } catch (e) {
      state = state.copyWith(
        isTransitioning: false,
        error: 'Failed to enter detail mode: ${e.toString()}',
      );
    }
  }

  // Exit detail mode
  Future<void> exitDetailMode() async {
    if (!state.isInDetailMode) return;

    state = state.copyWith(isTransitioning: true);

    try {
      // Clear detail state
      state = const CollectionDetailState();
    } catch (e) {
      state = state.copyWith(
        isTransitioning: false,
        error: 'Failed to exit detail mode: ${e.toString()}',
      );
    }
  }

  // Equip an item
  Future<void> equipItem(ShopItemModel item) async {
    if (!state.isInDetailMode || state.isEquipping) return;

    state = state.copyWith(isEquipping: true);

    try {
      final user = _ref.read(currentUserProvider);
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Check if user owns the item
      if (!_isItemOwned(item, user)) {
        throw Exception('Item not owned');
      }

      // Update user's equipped item based on type
      await _equipItemInUserProfile(item, user);

      // Apply the item in Unity
      await _applyItemInUnity(item);

      state = state.copyWith(
        isEquipping: false,
        error: null,
      );
    } catch (e) {
      state = state.copyWith(
        isEquipping: false,
        error: 'Failed to equip item: ${e.toString()}',
      );
    }
  }

  // Helper methods
  List<ShopItemModel> _getCategoryItems(ShopState shopState, ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return shopState.environments;
      case ShopItemType.outfit:
        return shopState.outfits;
      case ShopItemType.accessory:
        return shopState.accessories;
      case ShopItemType.companion:
        return shopState.companions;
      case ShopItemType.pet:
        return shopState.pets;
      default:
        return [];
    }
  }

  List<ShopItemModel> _getOwnedItemsForCategory(
    UserModel user,
    List<ShopItemModel> categoryItems,
    ShopItemType type,
  ) {
    return categoryItems.where((item) => _isItemOwned(item, user)).toList();
  }

  bool _isItemOwned(ShopItemModel item, UserModel user) {
    switch (item.type) {
      case ShopItemType.environment:
        return user.ownedEnvironments.contains(item.id);
      case ShopItemType.outfit:
      case ShopItemType.accessory:
      case ShopItemType.companion:
      case ShopItemType.pet:
        return user.ownedOutfits.contains(item.id);
      default:
        return false;
    }
  }

  Future<void> _equipItemInUserProfile(ShopItemModel item, UserModel user) async {
    // For now, use mock equipment state - save to storage
    switch (item.type) {
      case ShopItemType.environment:
        await StorageService.saveProgress('selected_environment', item.id);
        break;
      case ShopItemType.outfit:
        await StorageService.saveProgress('selected_outfit', item.id);
        break;
      case ShopItemType.companion:
        await StorageService.saveProgress('selected_companion', item.id);
        break;
      case ShopItemType.accessory:
        await StorageService.saveProgress('selected_accessory', item.id);
        break;
      case ShopItemType.pet:
        await StorageService.saveProgress('selected_pet', item.id);
        break;
      default:
        break;
    }
  }

  Future<void> _applyItemInUnity(ShopItemModel item) async {
    final unityController = _ref.read(unityControllerProvider.notifier);

    switch (item.type) {
      case ShopItemType.environment:
        unityController.changeEnvironment(item.id);
        break;
      case ShopItemType.outfit:
        unityController.changeOutfit(item.id);
        break;
      case ShopItemType.accessory:
        unityController.sendCommand('changeAccessory', {'accessory': item.id});
        break;
      case ShopItemType.companion:
        unityController.sendCommand('changeCompanion', {
          'companionId': item.id,
          'personality': item.metadata['personality'] ?? 'caringFriend',
        });
        break;
      case ShopItemType.pet:
        unityController.sendCommand('changePet', {'petId': item.id});
        break;
      default:
        break;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Providers
final collectionDetailProvider = StateNotifierProvider<CollectionDetailNotifier, CollectionDetailState>((ref) {
  return CollectionDetailNotifier(ref);
});

// Utility providers
final selectedCollectionItemProvider = Provider<ShopItemModel?>((ref) {
  return ref.watch(collectionDetailProvider).selectedItem;
});

final collectionOwnedItemsProvider = Provider<List<ShopItemModel>>((ref) {
  return ref.watch(collectionDetailProvider).ownedItems;
});

final collectionCategoryItemsProvider = Provider<List<ShopItemModel>>((ref) {
  return ref.watch(collectionDetailProvider).categoryItems;
});

final isInCollectionDetailModeProvider = Provider<bool>((ref) {
  return ref.watch(collectionDetailProvider).isInDetailMode;
});
