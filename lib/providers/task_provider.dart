import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/task/task_model.dart';

class TaskNotifier extends StateNotifier<List<TaskModel>> {
  TaskNotifier() : super([]);

  void addTask(TaskModel task) {
    state = [...state, task];
  }

  void updateTask(String taskId, TaskModel updatedTask) {
    state = state.map((task) {
      return task.id == taskId ? updatedTask : task;
    }).toList();
  }

  void removeTask(String taskId) {
    state = state.where((task) => task.id != taskId).toList();
  }

  void cancelTask(String taskId) {
    state = state.map((task) {
      if (task.id == taskId) {
        return task.copyWith(
          status: TaskStatus.cancelled,
          updatedAt: DateTime.now(),
        );
      }
      return task;
    }).toList();
  }

  void requestProgressUpdate(String taskId) {
    // TODO: Implement actual progress update request to backend
    // For now, simulate a progress update
    final task = state.firstWhere((t) => t.id == taskId);
    final updatedTask = task.copyWith(
      progress: (task.progress + 0.1).clamp(0.0, 1.0),
      updatedAt: DateTime.now(),
    );
    updateTask(taskId, updatedTask);
  }

  List<TaskModel> get activeTasks {
    return state.where((task) => 
      task.status == TaskStatus.running || 
      task.status == TaskStatus.pending ||
      task.status == TaskStatus.needsIntervention
    ).toList();
  }

  List<TaskModel> get completedTasks {
    return state.where((task) => 
      task.status == TaskStatus.completed ||
      task.status == TaskStatus.failed ||
      task.status == TaskStatus.cancelled
    ).toList();
  }

  bool get hasActiveTasks => activeTasks.isNotEmpty;
}

final taskProvider = StateNotifierProvider<TaskNotifier, List<TaskModel>>((ref) {
  return TaskNotifier();
});

final activeTasksProvider = Provider<List<TaskModel>>((ref) {
  final tasks = ref.watch(taskProvider);
  return tasks.where((task) => 
    task.status == TaskStatus.running || 
    task.status == TaskStatus.pending ||
    task.status == TaskStatus.needsIntervention
  ).toList();
});

final hasActiveTasksProvider = Provider<bool>((ref) {
  final activeTasks = ref.watch(activeTasksProvider);
  return activeTasks.isNotEmpty;
});