import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:logger/logger.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'dart:math';
import '../models/user/user_model.dart';
import '../services/storage/storage_service.dart';
import '../services/api/api_service.dart';
import '../services/auth/auth_service.dart';

final _logger = Logger();

// Auth State
sealed class AuthState {
  const AuthState();

  T when<T>({
    required T Function() loading,
    required T Function() unauthenticated,
    required T Function(UserModel user) authenticated,
    required T Function(String email, bool isSignup) emailVerificationRequired,
    required T Function(String email) passwordResetVerificationRequired,
    required T Function(String email) changePasswordRequired,
  }) {
    return switch (this) {
      AuthStateLoading() => loading(),
      AuthStateUnauthenticated() => unauthenticated(),
      AuthStateAuthenticated(user: final user) => authenticated(user),
      AuthStateEmailVerificationRequired(email: final email, isSignup: final isSignup) =>
        emailVerificationRequired(email, isSignup),
      AuthStatePasswordResetVerificationRequired(email: final email) =>
        passwordResetVerificationRequired(email),
      AuthStateChangePasswordRequired(email: final email) =>
        changePasswordRequired(email),
    };
  }
}

class AuthStateLoading extends AuthState {
  const AuthStateLoading();
}

class AuthStateUnauthenticated extends AuthState {
  const AuthStateUnauthenticated();
}

class AuthStateAuthenticated extends AuthState {
  final UserModel user;
  const AuthStateAuthenticated({required this.user});
}

class AuthStateEmailVerificationRequired extends AuthState {
  final String email;
  final bool isSignup; // true for signup flow, false for login flow
  const AuthStateEmailVerificationRequired({
    required this.email,
    required this.isSignup,
  });
}

class AuthStatePasswordResetVerificationRequired extends AuthState {
  final String email;
  const AuthStatePasswordResetVerificationRequired({required this.email});
}

class AuthStateChangePasswordRequired extends AuthState {
  final String email;
  const AuthStateChangePasswordRequired({required this.email});
}

// Auth Provider
class AuthNotifier extends StateNotifier<AuthState> {
  final GoogleSignIn _googleSignIn;
  final ApiService _apiService;
  GoogleSignInAccount? _currentGoogleUser;

  // Email/Password authentication storage
  final Map<String, String> _pendingUsers = {}; // email -> hashed password
  final Map<String, String> _verificationCodes = {}; // email -> verification code
  final Map<String, DateTime> _codeExpirations = {}; // email -> expiration time

  AuthNotifier({
    required GoogleSignIn googleSignIn,
    required ApiService apiService,
  }) : _googleSignIn = googleSignIn,
       _apiService = apiService,
       super(const AuthStateLoading()) {
    _init();
  }

  void _init() async {
    // Check if user is already signed in locally
    final localUser = StorageService.getCurrentUser();
    if (localUser != null) {
      // Try to restore Google Sign In session
      try {
        await _googleSignIn.signInSilently();
        _currentGoogleUser = _googleSignIn.currentUser;
        if (_currentGoogleUser != null) {
          state = AuthStateAuthenticated(user: localUser);
          return;
        }
      } catch (e) {
        _logger.w('Failed to restore Google Sign In session: $e');
      }
    }

    state = const AuthStateUnauthenticated();
  }

  // Helper methods for email/password authentication
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  String _generateVerificationCode() {
    final random = Random();
    return (100000 + random.nextInt(900000)).toString(); // 6-digit code
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  bool _isValidPassword(String password) {
    // At least 8 characters, contains uppercase, lowercase, number
    return password.length >= 8 &&
           RegExp(r'[A-Z]').hasMatch(password) &&
           RegExp(r'[a-z]').hasMatch(password) &&
           RegExp(r'[0-9]').hasMatch(password);
  }

  bool _isVerificationCodeValid(String email) {
    final expiration = _codeExpirations[email];
    return expiration != null && DateTime.now().isBefore(expiration);
  }

  UserModel _createUserFromBackendResponse(Map<String, dynamic> userData) {
    return UserModel.initial(
      id: userData['id']?.toString() ?? '',
      name: userData['first_name'] != null && userData['last_name'] != null
          ? '${userData['first_name']} ${userData['last_name']}'.trim()
          : userData['username'] ?? 'User',
      email: userData['email'] ?? '',
      photoURL: userData['profile_picture'],
    );
  }

  Future<UserModel> _createOrUpdateUser(GoogleSignInAccount googleUser) async {
    try {
      // Try to get user from backend using Google ID
      final userData = await _apiService.getUser(googleUser.id);
      return UserModel.fromJson(userData);
    } catch (e) {
      // If user doesn't exist in backend, create new user
      final newUser = UserModel.initial(
        id: googleUser.id,
        name: googleUser.displayName ?? 'User',
        email: googleUser.email,
        photoURL: googleUser.photoUrl,
      );

      try {
        await _apiService.createUser(newUser.toJson());
      } catch (e) {
        // Continue even if backend fails
        _logger.w('Failed to create user in backend: $e');
      }

      return newUser;
    }
  }

  // Google Sign In
  Future<void> signInWithGoogle() async {
    try {
      state = const AuthStateLoading();

      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        state = const AuthStateUnauthenticated();
        return;
      }

      _currentGoogleUser = googleUser;

      // Get Google ID token for backend authentication
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final String? idToken = googleAuth.idToken;

      if (idToken == null) {
        throw Exception('Failed to get Google ID token');
      }

      // Authenticate with backend using Google ID token
      try {
        final authService = AuthService();
        final response = await authService.loginWithGoogle(idToken);

        // Create user model from backend response
        final user = _createUserFromBackendResponse(response['user']);
        await StorageService.saveUser(user);

        state = AuthStateAuthenticated(user: user);
      } catch (e) {
        // Fallback to local authentication if backend fails
        _logger.w('Backend Google auth failed, using local auth: $e');
        final user = await _createOrUpdateUser(googleUser);
        await StorageService.saveUser(user);
        state = AuthStateAuthenticated(user: user);
      }
    } catch (e) {
      state = const AuthStateUnauthenticated();
      rethrow;
    }
  }

  // Apple Sign In
  Future<void> signInWithApple() async {
    try {
      state = const AuthStateLoading();

      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      // Extract Apple credentials
      final identityToken = credential.identityToken;
      final authorizationCode = credential.authorizationCode;
      final email = credential.email ?? '';
      final firstName = credential.givenName ?? '';
      final lastName = credential.familyName ?? '';

      if (identityToken == null || authorizationCode == null ||
          identityToken.isEmpty || authorizationCode.isEmpty) {
        throw Exception('Failed to get Apple credentials');
      }

      // Authenticate with backend using Apple credentials
      try {
        final authService = AuthService();
        final response = await authService.loginWithApple(
          identityToken: identityToken,
          authorizationCode: authorizationCode,
          email: email.isNotEmpty ? email : null,
          firstName: firstName.isNotEmpty ? firstName : null,
          lastName: lastName.isNotEmpty ? lastName : null,
        );

        // Create user model from backend response
        final user = _createUserFromBackendResponse(response['user']);
        await StorageService.saveUser(user);

        state = AuthStateAuthenticated(user: user);
      } catch (e) {
        // Fallback to local authentication if backend fails
        _logger.w('Backend Apple auth failed, using local auth: $e');
        final appleUserId = credential.userIdentifier ?? '';
        final name = firstName.isNotEmpty && lastName.isNotEmpty
            ? '$firstName $lastName'
            : 'User';

        final user = UserModel.initial(
          id: appleUserId,
          name: name,
          email: email,
          photoURL: null,
        );

        await StorageService.saveUser(user);
        state = AuthStateAuthenticated(user: user);
      }
    } catch (e) {
      state = const AuthStateUnauthenticated();
      rethrow;
    }
  }

  // Anonymous Sign In (for testing/demo purposes)
  Future<void> signInAnonymously() async {
    try {
      _logger.i('Starting anonymous sign in...');
      state = const AuthStateLoading();

      final anonymousId = 'anonymous_${DateTime.now().millisecondsSinceEpoch}';
      final user = UserModel.initial(
        id: anonymousId,
        name: 'Guest User',
        email: '',
        photoURL: null,
      );

      _logger.i('Created anonymous user: ${user.id}');
      await StorageService.saveUser(user);

      _logger.i('Anonymous sign in complete');
      state = AuthStateAuthenticated(user: user);
    } catch (e) {
      _logger.e('Anonymous sign in failed: $e');
      state = const AuthStateUnauthenticated();
      rethrow;
    }
  }

  // Email/Password Sign Up
  Future<void> signUpWithEmailPassword({
    required String email,
    required String password,
    required String name,
  }) async {
    try {
      state = const AuthStateLoading();

      // Validate input
      if (!_isValidEmail(email)) {
        throw Exception('Please enter a valid email address');
      }
      if (!_isValidPassword(password)) {
        throw Exception('Password must be at least 8 characters with uppercase, lowercase, and number');
      }
      if (name.trim().isEmpty) {
        throw Exception('Please enter your name');
      }

      // Try to register with backend first
      try {
        final authService = AuthService();
        final nameParts = name.trim().split(' ');
        final firstName = nameParts.first;
        final lastName = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

        final response = await authService.register(
          email: email,
          password: password,
          firstName: firstName,
          lastName: lastName,
        );

        // Create user model from backend response
        final user = _createUserFromBackendResponse(response['user']);
        await StorageService.saveUser(user);

        state = AuthStateAuthenticated(user: user);
        return;
      } catch (e) {
        _logger.e('Backend registration failed: $e');

        // Check if it's a specific backend error that should not fallback
        final errorMessage = e.toString().toLowerCase();
        if (errorMessage.contains('email') && errorMessage.contains('already')) {
          throw Exception('An account with this email already exists');
        }
        if (errorMessage.contains('validation') || errorMessage.contains('invalid')) {
          throw Exception('Registration failed: Invalid data provided');
        }
        if (errorMessage.contains('400')) {
          throw Exception('Registration failed: Please check your information and try again');
        }

        // For network errors or server issues, we can fallback to local flow
        if (!errorMessage.contains('network') && !errorMessage.contains('connection') &&
            !errorMessage.contains('timeout') && !errorMessage.contains('500')) {
          // This is likely a validation error from backend, don't fallback
          throw Exception('Registration failed: ${e.toString()}');
        }

        _logger.w('Network/server error, falling back to local flow: $e');
      }

      // Fallback to local registration flow
      // Check if user already exists locally
      final existingUser = StorageService.getCurrentUser();
      if (existingUser != null && existingUser.email == email) {
        throw Exception('An account with this email already exists');
      }

      // Store pending user data
      _pendingUsers[email] = _hashPassword(password);

      // Generate and store verification code
      final code = _generateVerificationCode();
      _verificationCodes[email] = code;
      _codeExpirations[email] = DateTime.now().add(const Duration(minutes: 10));

      // In a real app, send email here
      _logger.i('Verification code for $email: $code');

      // Transition to email verification state
      state = AuthStateEmailVerificationRequired(email: email, isSignup: true);
    } catch (e) {
      state = const AuthStateUnauthenticated();
      rethrow;
    }
  }

  // Email/Password Sign In
  Future<void> signInWithEmailPassword({
    required String email,
    required String password,
  }) async {
    try {
      state = const AuthStateLoading();

      // Validate input
      if (!_isValidEmail(email)) {
        throw Exception('Please enter a valid email address');
      }
      if (password.isEmpty) {
        throw Exception('Please enter your password');
      }

      // Try to authenticate with backend first
      try {
        final authService = AuthService();
        final response = await authService.login(
          email: email,
          password: password,
        );

        // Create user model from backend response
        final user = _createUserFromBackendResponse(response['user']);
        await StorageService.saveUser(user);

        state = AuthStateAuthenticated(user: user);
        return;
      } catch (e) {
        _logger.e('Backend login failed: $e');

        // Check if it's a specific backend error that should not fallback
        final errorMessage = e.toString().toLowerCase();
        if (errorMessage.contains('invalid') && (errorMessage.contains('email') || errorMessage.contains('password'))) {
          throw Exception('Invalid email or password');
        }
        if (errorMessage.contains('user') && errorMessage.contains('not found')) {
          throw Exception('No account found with this email');
        }
        if (errorMessage.contains('401') || errorMessage.contains('unauthorized')) {
          throw Exception('Invalid email or password');
        }
        if (errorMessage.contains('400')) {
          throw Exception('Login failed: Please check your credentials');
        }

        // For network errors or server issues, we can fallback to local flow
        if (!errorMessage.contains('network') && !errorMessage.contains('connection') &&
            !errorMessage.contains('timeout') && !errorMessage.contains('500')) {
          // This is likely a validation error from backend, don't fallback
          throw Exception('Login failed: ${e.toString()}');
        }

        _logger.w('Network/server error, falling back to local auth: $e');
      }

      // Fallback to local authentication
      final localUser = StorageService.getCurrentUser();
      if (localUser != null && localUser.email == email) {
        // For demo purposes, we'll check against stored password hash
        final storedHash = _pendingUsers[email];
        if (storedHash != null && storedHash == _hashPassword(password)) {
          state = AuthStateAuthenticated(user: localUser);
          return;
        }
      }

      // If no local user or password doesn't match, require email verification
      // Generate and store verification code
      final code = _generateVerificationCode();
      _verificationCodes[email] = code;
      _codeExpirations[email] = DateTime.now().add(const Duration(minutes: 10));

      // In a real app, send email here
      _logger.i('Verification code for $email: $code');

      // Transition to email verification state
      state = AuthStateEmailVerificationRequired(email: email, isSignup: false);
    } catch (e) {
      state = const AuthStateUnauthenticated();
      rethrow;
    }
  }

  // Verify Email with Code
  Future<void> verifyEmailWithCode({
    required String email,
    required String code,
    String? name, // Required for signup flow
  }) async {
    try {
      state = const AuthStateLoading();

      // Check if code is valid and not expired
      if (!_isVerificationCodeValid(email)) {
        throw Exception('Verification code has expired. Please request a new one.');
      }

      final storedCode = _verificationCodes[email];
      if (storedCode == null || storedCode != code) {
        throw Exception('Invalid verification code. Please try again.');
      }

      // Check if this is a signup or login flow
      final isSignup = _pendingUsers.containsKey(email);

      UserModel user;
      if (isSignup) {
        // Create new user for signup flow
        if (name == null || name.trim().isEmpty) {
          throw Exception('Name is required for new accounts');
        }

        final userId = 'email_${DateTime.now().millisecondsSinceEpoch}';
        user = UserModel.initial(
          id: userId,
          name: name.trim(),
          email: email,
          photoURL: null,
        );

        // Try to create user in backend
        try {
          await _apiService.createUser(user.toJson());
        } catch (e) {
          _logger.w('Failed to create user in backend: $e');
        }
      } else {
        // For login flow, try to get existing user
        final localUser = StorageService.getCurrentUser();
        if (localUser != null && localUser.email == email) {
          user = localUser;
        } else {
          // Create user if not found locally (shouldn't happen in normal flow)
          final userId = 'email_${DateTime.now().millisecondsSinceEpoch}';
          user = UserModel.initial(
            id: userId,
            name: email.split('@')[0], // Use email prefix as name
            email: email,
            photoURL: null,
          );
        }
      }

      // Save user and authenticate
      await StorageService.saveUser(user);

      // Clean up verification data
      _verificationCodes.remove(email);
      _codeExpirations.remove(email);
      if (isSignup) {
        _pendingUsers.remove(email);
      }

      state = AuthStateAuthenticated(user: user);
    } catch (e) {
      // Return to email verification state on error
      final isSignup = _pendingUsers.containsKey(email);
      state = AuthStateEmailVerificationRequired(email: email, isSignup: isSignup);
      rethrow;
    }
  }

  // Resend Verification Code
  Future<void> resendVerificationCode(String email) async {
    try {
      // Generate new verification code
      final code = _generateVerificationCode();
      _verificationCodes[email] = code;
      _codeExpirations[email] = DateTime.now().add(const Duration(minutes: 10));

      // In a real app, send email here
      _logger.i('New verification code for $email: $code');
    } catch (e) {
      _logger.e('Failed to resend verification code: $e');
      rethrow;
    }
  }

  // Use Password Instead (for login flow only)
  Future<void> usePasswordInstead({
    required String email,
    required String password,
  }) async {
    try {
      state = const AuthStateLoading();

      // Validate input
      if (password.isEmpty) {
        throw Exception('Please enter your password');
      }

      // Check if user exists locally with matching password
      final localUser = StorageService.getCurrentUser();
      if (localUser != null && localUser.email == email) {
        final storedHash = _pendingUsers[email];
        if (storedHash != null && storedHash == _hashPassword(password)) {
          // Clean up verification data
          _verificationCodes.remove(email);
          _codeExpirations.remove(email);

          state = AuthStateAuthenticated(user: localUser);
          return;
        }
      }

      throw Exception('Invalid password. Please try again or use email verification.');
    } catch (e) {
      // Return to email verification state on error
      state = AuthStateEmailVerificationRequired(email: email, isSignup: false);
      rethrow;
    }
  }

  // Forgot Password - Request Reset
  Future<void> requestPasswordReset(String email) async {
    try {
      state = const AuthStateLoading();

      // Validate email
      if (!_isValidEmail(email)) {
        throw Exception('Please enter a valid email address');
      }

      // Generate and store verification code for password reset
      final code = _generateVerificationCode();
      _verificationCodes[email] = code;
      _codeExpirations[email] = DateTime.now().add(const Duration(minutes: 10));

      // In a real app, send password reset email here
      _logger.i('Password reset code for $email: $code');

      // Transition to password reset verification state
      state = AuthStatePasswordResetVerificationRequired(email: email);
    } catch (e) {
      state = const AuthStateUnauthenticated();
      rethrow;
    }
  }

  // Verify Password Reset Code
  Future<void> verifyPasswordResetCode({
    required String email,
    required String code,
  }) async {
    try {
      state = const AuthStateLoading();

      // Check if code is valid and not expired
      if (!_isVerificationCodeValid(email)) {
        throw Exception('Verification code has expired. Please request a new one.');
      }

      final storedCode = _verificationCodes[email];
      if (storedCode == null || storedCode != code) {
        throw Exception('Invalid verification code. Please try again.');
      }

      // Clean up verification code (but keep email for password change)
      _verificationCodes.remove(email);
      _codeExpirations.remove(email);

      // Transition to change password state
      state = AuthStateChangePasswordRequired(email: email);
    } catch (e) {
      // Return to password reset verification state on error
      state = AuthStatePasswordResetVerificationRequired(email: email);
      rethrow;
    }
  }

  // Change Password (after verification)
  Future<void> changePassword({
    required String email,
    required String newPassword,
  }) async {
    try {
      state = const AuthStateLoading();

      // Validate new password
      if (!_isValidPassword(newPassword)) {
        throw Exception('Password must be at least 8 characters with uppercase, lowercase, and number');
      }

      // Update password hash
      _pendingUsers[email] = _hashPassword(newPassword);

      // Check if user exists locally and update
      final localUser = StorageService.getCurrentUser();
      if (localUser != null && localUser.email == email) {
        // User exists locally, keep them signed in
        state = AuthStateAuthenticated(user: localUser);
      } else {
        // User doesn't exist locally, redirect to sign in
        state = const AuthStateUnauthenticated();
      }

      // In a real app, update password in backend here
      _logger.i('Password updated for $email');
    } catch (e) {
      // Return to change password state on error
      state = AuthStateChangePasswordRequired(email: email);
      rethrow;
    }
  }

  // Sign Out
  Future<void> signOut() async {
    try {
      await _googleSignIn.signOut();
      _currentGoogleUser = null;
      await StorageService.clearUser();

      // Clean up email/password data
      _pendingUsers.clear();
      _verificationCodes.clear();
      _codeExpirations.clear();

      state = const AuthStateUnauthenticated();
    } catch (e) {
      _logger.e('Error signing out: $e');
    }
  }

  // Update User Profile
  Future<void> updateUserProfile({
    String? name,
    String? photoURL,
    Map<String, dynamic>? preferences,
  }) async {
    final currentState = state;
    if (currentState is! AuthStateAuthenticated) return;

    try {
      final updatedUser = currentState.user.copyWith(
        name: name,
        photoURL: photoURL,
        preferences: preferences,
      );

      await StorageService.saveUser(updatedUser);
      state = AuthStateAuthenticated(user: updatedUser);

      // Try to update backend
      try {
        await _apiService.updateUser(updatedUser.id, updatedUser.toJson());
      } catch (e) {
        _logger.w('Failed to update user in backend: $e');
      }
    } catch (e) {
      _logger.e('Error updating user profile: $e');
    }
  }

  // Update User (convenience method for settings pages)
  Future<void> updateUser(UserModel updatedUser) async {
    final currentState = state;
    if (currentState is! AuthStateAuthenticated) return;

    try {
      await StorageService.saveUser(updatedUser);
      state = AuthStateAuthenticated(user: updatedUser);

      // Try to update backend
      try {
        await _apiService.updateUser(updatedUser.id, updatedUser.toJson());
      } catch (e) {
        _logger.w('Failed to update user in backend: $e');
      }
    } catch (e) {
      _logger.e('Error updating user: $e');
    }
  }

  // Get current user
  UserModel? get currentUser {
    final currentState = state;
    return currentState is AuthStateAuthenticated ? currentState.user : null;
  }

  // Check if user is authenticated
  bool get isAuthenticated => state is AuthStateAuthenticated;

  // Get Google Sign In account for additional operations
  GoogleSignInAccount? get currentGoogleUser => _currentGoogleUser;
}

// Providers
final googleSignInProvider = Provider<GoogleSignIn>((ref) {
  return GoogleSignIn(
    scopes: [
      'email',
      'profile',
    ],
  );
});

final apiServiceProvider = Provider<ApiService>((ref) {
  return ApiService();
});

final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier(
    googleSignIn: ref.read(googleSignInProvider),
    apiService: ref.read(apiServiceProvider),
  );
});

// Utility providers
final currentUserProvider = Provider<UserModel?>((ref) {
  final authState = ref.watch(authProvider);
  return authState is AuthStateAuthenticated ? authState.user : null;
});

final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authProvider);
  return authState is AuthStateAuthenticated;
});

// Extensions for UserModel serialization
extension UserModelExtension on UserModel {
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'photoURL': photoURL,
      'createdAt': createdAt.toIso8601String(),
      'progress': {
        'xp': progress.xp,
        'level': progress.level,
        'hearts': progress.hearts,
        'messagesCount': progress.messagesCount,
        'voiceMessagesCount': progress.voiceMessagesCount,
        'lastDailyBonus': progress.lastDailyBonus?.toIso8601String(),
        'achievements': progress.achievements,
        'totalTimeSpent': progress.totalTimeSpent,
      },
      'selectedPersonality': selectedPersonality.name,
      'selectedEnvironment': selectedEnvironment,
      'ownedEnvironments': ownedEnvironments,
      'ownedOutfits': ownedOutfits,
      'preferences': preferences,
    };
  }

  static UserModel fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      photoURL: json['photoURL'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      progress: UserProgress(
        xp: json['progress']['xp'] as int,
        level: json['progress']['level'] as int,
        hearts: json['progress']['hearts'] as int,
        messagesCount: json['progress']['messagesCount'] as int,
        voiceMessagesCount: json['progress']['voiceMessagesCount'] as int,
        lastDailyBonus:
            json['progress']['lastDailyBonus'] != null
                ? DateTime.parse(json['progress']['lastDailyBonus'] as String)
                : null,
        achievements: (json['progress']['achievements'] as List).cast<String>(),
        totalTimeSpent: json['progress']['totalTimeSpent'] as int,
      ),
      selectedPersonality: CompanionPersonality.values.firstWhere(
        (e) => e.name == json['selectedPersonality'],
        orElse: () => CompanionPersonality.caringFriend,
      ),
      selectedEnvironment: json['selectedEnvironment'] as String,
      ownedEnvironments: (json['ownedEnvironments'] as List).cast<String>(),
      ownedOutfits: (json['ownedOutfits'] as List).cast<String>(),
      preferences: Map<String, dynamic>.from(json['preferences'] as Map),
    );
  }
}