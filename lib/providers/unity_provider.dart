import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_unity_widget/flutter_unity_widget.dart';
import '../core/constants/app_constants.dart';
import '../services/storage/storage_service.dart';
import '../models/shop/shop_preview_models.dart';

// Unity State
class UnityState {
  final UnityWidgetController? controller;
  final bool isInitialized;
  final String? currentEnvironment;
  final String? currentExpression;
  final bool isTalking;
  final Map<String, dynamic> avatarSettings;
  final CameraMode cameraMode;
  final bool isCameraTransitioning;
  final Map<String, dynamic> cameraSettings;

  const UnityState({
    this.controller,
    this.isInitialized = false,
    this.currentEnvironment,
    this.currentExpression,
    this.isTalking = false,
    this.avatarSettings = const {},
    this.cameraMode = CameraMode.defaultMode,
    this.isCameraTransitioning = false,
    this.cameraSettings = const {},
  });

  UnityState copyWith({
    UnityWidgetController? controller,
    bool? isInitialized,
    String? currentEnvironment,
    String? currentExpression,
    bool? isTalking,
    Map<String, dynamic>? avatarSettings,
    CameraMode? cameraMode,
    bool? isCameraTransitioning,
    Map<String, dynamic>? cameraSettings,
  }) {
    return UnityState(
      controller: controller ?? this.controller,
      isInitialized: isInitialized ?? this.isInitialized,
      currentEnvironment: currentEnvironment ?? this.currentEnvironment,
      currentExpression: currentExpression ?? this.currentExpression,
      isTalking: isTalking ?? this.isTalking,
      avatarSettings: avatarSettings ?? this.avatarSettings,
      cameraMode: cameraMode ?? this.cameraMode,
      isCameraTransitioning: isCameraTransitioning ?? this.isCameraTransitioning,
      cameraSettings: cameraSettings ?? this.cameraSettings,
    );
  }
}

// Unity Notifier
class UnityNotifier extends StateNotifier<UnityState> {
  UnityNotifier() : super(const UnityState()) {
    _loadSettings();
  }

  void _loadSettings() {
    final environment = StorageService.getSelectedEnvironment();
    state = state.copyWith(
      currentEnvironment: environment,
      avatarSettings: {
        'environment': environment,
        'expression': 'neutral',
        'outfit': 'default',
      },
      cameraSettings: {
        'mode': 'default',
        'position': {'x': 0.0, 'y': 1.6, 'z': 2.0},
        'rotation': {'x': 0.0, 'y': 0.0, 'z': 0.0},
        'fov': 60.0,
      },
    );
  }

  void setController(UnityWidgetController controller) {
    state = state.copyWith(
      controller: controller,
      isInitialized: true,
    );
    
    // Initialize Unity with current settings
    _initializeUnity();
  }

  void _initializeUnity() {
    if (state.controller == null) return;

    // Set initial environment
    changeEnvironment(state.currentEnvironment ?? AppConstants.defaultEnvironments.first);
    
    // Set initial expression
    changeExpression('neutral');
    
    // Send initial avatar settings
    sendCommand('initializeAvatar', state.avatarSettings);
  }

  void sendCommand(String command, Map<String, dynamic> parameters) {
    if (state.controller == null || !state.isInitialized) {
      print('Unity controller not initialized, queuing command: $command');
      return;
    }

    try {
      final message = {
        'command': command,
        'parameters': parameters,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
      
      state.controller!.postMessage(
        'GameManager', // Unity GameObject name
        'ReceiveCommand', // Unity method name
        message.toString(),
      );
      
      print('Sent Unity command: $command with parameters: $parameters');
    } catch (e) {
      print('Error sending Unity command: $e');
    }
  }

  void handleMessage(dynamic message) {
    print('Received Unity message: $message');
    
    try {
      // Parse Unity message and update state accordingly
      if (message is String) {
        // Handle string messages from Unity
        _handleStringMessage(message);
      } else if (message is Map) {
        // Handle structured messages from Unity
        _handleStructuredMessage(message);
      }
    } catch (e) {
      print('Error handling Unity message: $e');
    }
  }

  void _handleStringMessage(String message) {
    // Handle simple string messages from Unity
    switch (message.toLowerCase()) {
      case 'avatar_ready':
        print('Unity avatar is ready');
        break;
      case 'animation_complete':
        if (state.isTalking) {
          state = state.copyWith(isTalking: false);
        }
        break;
      case 'environment_changed':
        print('Unity environment changed successfully');
        break;
      case 'camera_transition_complete':
        state = state.copyWith(isCameraTransitioning: false);
        print('Unity camera transition completed');
        break;
      case 'camera_mode_changed':
        print('Unity camera mode changed successfully');
        break;
      default:
        print('Unknown Unity message: $message');
    }
  }

  void _handleStructuredMessage(Map message) {
    final type = message['type'] as String?;
    final data = message['data'] as Map<String, dynamic>?;

    switch (type) {
      case 'expression_changed':
        if (data?['expression'] != null) {
          state = state.copyWith(currentExpression: data!['expression']);
        }
        break;
      case 'environment_changed':
        if (data?['environment'] != null) {
          state = state.copyWith(currentEnvironment: data!['environment']);
        }
        break;
      case 'talking_state':
        if (data?['isTalking'] != null) {
          state = state.copyWith(isTalking: data!['isTalking']);
        }
        break;
      case 'camera_state_changed':
        if (data?['mode'] != null) {
          final mode = data!['mode'] == 'preview' ? CameraMode.preview : CameraMode.defaultMode;
          state = state.copyWith(cameraMode: mode);
        }
        if (data?['isTransitioning'] != null) {
          state = state.copyWith(isCameraTransitioning: data!['isTransitioning']);
        }
        if (data?['settings'] != null) {
          state = state.copyWith(cameraSettings: Map<String, dynamic>.from(data!['settings']));
        }
        break;
      case 'camera_transition_complete':
        state = state.copyWith(isCameraTransitioning: false);
        break;
      case 'error':
        print('Unity error: ${data?['message'] ?? 'Unknown error'}');
        break;
    }
  }

  // Avatar Control Methods
  void startTalking({bool isUser = false, String emotion = 'neutral'}) {
    state = state.copyWith(isTalking: true);
    
    sendCommand(AppConstants.unityStartTalkingCommand, {
      'isUser': isUser,
      'emotion': emotion,
    });
  }

  void stopTalking() {
    state = state.copyWith(isTalking: false);
    
    sendCommand(AppConstants.unityStopTalkingCommand, {});
  }

  void changeExpression(String expression) {
    state = state.copyWith(currentExpression: expression);
    
    sendCommand(AppConstants.unityChangeExpressionCommand, {
      'expression': expression,
    });
  }

  void changeEnvironment(String environment) {
    state = state.copyWith(currentEnvironment: environment);
    
    sendCommand(AppConstants.unityChangeEnvironmentCommand, {
      'environment': environment,
    });
    
    // Save to storage
    StorageService.setSelectedEnvironment(environment);
  }

  void changeOutfit(String outfit) {
    final updatedSettings = Map<String, dynamic>.from(state.avatarSettings);
    updatedSettings['outfit'] = outfit;
    
    state = state.copyWith(avatarSettings: updatedSettings);
    
    sendCommand(AppConstants.unityChangeOutfitCommand, {
      'outfit': outfit,
    });
  }

  // Predefined Expressions
  void expressHappiness() => changeExpression('happy');
  void expressSadness() => changeExpression('sad');
  void expressExcitement() => changeExpression('excited');
  void expressThinking() => changeExpression('thinking');
  void expressLove() => changeExpression('love');
  void expressNeutral() => changeExpression('neutral');

  // Animation Triggers
  void playGesture(String gesture) {
    sendCommand('playGesture', {'gesture': gesture});
  }

  void playAnimation(String animation) {
    sendCommand('playAnimation', {'animation': animation});
  }

  // Environment Presets
  void setCozyRoom() => changeEnvironment('cozy_room');
  void setModernApartment() => changeEnvironment('modern_apartment');
  void setGardenTerrace() => changeEnvironment('garden_terrace');
  void setStarryNight() => changeEnvironment('starry_night');
  void setCherryBlossom() => changeEnvironment('cherry_blossom');
  void setCyberpunkCity() => changeEnvironment('cyberpunk_city');

  // Camera Control Methods
  Future<void> setCameraMode(CameraMode mode, {Duration? duration}) async {
    if (state.cameraMode == mode) return;

    state = state.copyWith(
      isCameraTransitioning: true,
      cameraMode: mode,
    );

    final transitionDuration = duration ?? const Duration(milliseconds: 800);
    
    sendCommand('setCameraMode', {
      'mode': mode == CameraMode.preview ? 'preview' : 'default',
      'duration': transitionDuration.inMilliseconds,
      'settings': _getCameraSettingsForMode(mode),
    });

    // Wait for transition to complete
    await Future.delayed(transitionDuration);
    
    if (mounted) {
      state = state.copyWith(isCameraTransitioning: false);
    }
  }

  Future<void> transitionToPreviewMode({Duration? duration}) async {
    await setCameraMode(CameraMode.preview, duration: duration);
  }

  Future<void> transitionToDefaultMode({Duration? duration}) async {
    await setCameraMode(CameraMode.defaultMode, duration: duration);
  }

  void setCameraPosition({
    required double x,
    required double y,
    required double z,
    double? rotX,
    double? rotY,
    double? rotZ,
    Duration? duration,
  }) {
    final transitionDuration = duration ?? const Duration(milliseconds: 500);
    
    state = state.copyWith(isCameraTransitioning: true);

    sendCommand('setCameraPosition', {
      'position': {'x': x, 'y': y, 'z': z},
      'rotation': {
        'x': rotX ?? 0.0,
        'y': rotY ?? 0.0,
        'z': rotZ ?? 0.0,
      },
      'duration': transitionDuration.inMilliseconds,
    });

    // Update local camera settings
    final updatedSettings = Map<String, dynamic>.from(state.cameraSettings);
    updatedSettings['position'] = {'x': x, 'y': y, 'z': z};
    updatedSettings['rotation'] = {
      'x': rotX ?? 0.0,
      'y': rotY ?? 0.0,
      'z': rotZ ?? 0.0,
    };
    
    state = state.copyWith(cameraSettings: updatedSettings);

    // Clear transition state after duration
    Future.delayed(transitionDuration, () {
      if (mounted) {
        state = state.copyWith(isCameraTransitioning: false);
      }
    });
  }

  void setCameraZoom(double fov, {Duration? duration}) {
    final transitionDuration = duration ?? const Duration(milliseconds: 300);
    
    sendCommand('setCameraZoom', {
      'fov': fov,
      'duration': transitionDuration.inMilliseconds,
    });

    // Update local camera settings
    final updatedSettings = Map<String, dynamic>.from(state.cameraSettings);
    updatedSettings['fov'] = fov;
    state = state.copyWith(cameraSettings: updatedSettings);
  }

  void smoothCameraTransition({
    required Map<String, double> targetPosition,
    required Map<String, double> targetRotation,
    double? targetFov,
    Duration? duration,
  }) {
    final transitionDuration = duration ?? const Duration(milliseconds: 800);
    
    state = state.copyWith(isCameraTransitioning: true);

    sendCommand('smoothCameraTransition', {
      'targetPosition': targetPosition,
      'targetRotation': targetRotation,
      'targetFov': targetFov ?? 60.0,
      'duration': transitionDuration.inMilliseconds,
      'easing': 'easeInOutCubic',
    });

    // Update local settings
    final updatedSettings = Map<String, dynamic>.from(state.cameraSettings);
    updatedSettings['position'] = targetPosition;
    updatedSettings['rotation'] = targetRotation;
    if (targetFov != null) {
      updatedSettings['fov'] = targetFov;
    }
    
    state = state.copyWith(cameraSettings: updatedSettings);

    Future.delayed(transitionDuration, () {
      if (mounted) {
        state = state.copyWith(isCameraTransitioning: false);
      }
    });
  }

  // Camera preset methods
  void setCameraForFullBodyView({Duration? duration}) {
    setCameraPosition(
      x: 0.0,
      y: 1.0,
      z: 4.0,
      rotX: 0.0,
      rotY: 0.0,
      rotZ: 0.0,
      duration: duration,
    );
    setCameraZoom(45.0, duration: duration);
  }

  void setCameraForCloseUpView({Duration? duration}) {
    setCameraPosition(
      x: 0.0,
      y: 1.6,
      z: 2.0,
      rotX: 0.0,
      rotY: 0.0,
      rotZ: 0.0,
      duration: duration,
    );
    setCameraZoom(60.0, duration: duration);
  }

  void setCameraForOutfitPreview({Duration? duration}) {
    setCameraPosition(
      x: 0.5,
      y: 1.2,
      z: 3.5,
      rotX: 5.0,
      rotY: -10.0,
      rotZ: 0.0,
      duration: duration,
    );
    setCameraZoom(50.0, duration: duration);
  }

  Map<String, dynamic> _getCameraSettingsForMode(CameraMode mode) {
    switch (mode) {
      case CameraMode.defaultMode:
        return {
          'position': {'x': 0.0, 'y': 1.6, 'z': 2.0},
          'rotation': {'x': 0.0, 'y': 0.0, 'z': 0.0},
          'fov': 60.0,
          'target': 'head',
        };
      case CameraMode.preview:
        return {
          'position': {'x': 0.0, 'y': 1.0, 'z': 4.0},
          'rotation': {'x': 0.0, 'y': 0.0, 'z': 0.0},
          'fov': 45.0,
          'target': 'fullBody',
        };
    }
  }

  // Accessory and cosmetic application methods
  void changeAccessory(String accessoryId, {String? slot}) {
    final updatedSettings = Map<String, dynamic>.from(state.avatarSettings);
    final accessoryKey = slot != null ? 'accessory_$slot' : 'accessory';
    updatedSettings[accessoryKey] = accessoryId;
    
    state = state.copyWith(avatarSettings: updatedSettings);
    
    sendCommand('changeAccessory', {
      'accessory': accessoryId,
      'slot': slot ?? 'default',
    });
  }

  void removeAccessory({String? slot}) {
    final updatedSettings = Map<String, dynamic>.from(state.avatarSettings);
    final accessoryKey = slot != null ? 'accessory_$slot' : 'accessory';
    updatedSettings.remove(accessoryKey);
    
    state = state.copyWith(avatarSettings: updatedSettings);
    
    sendCommand('removeAccessory', {
      'slot': slot ?? 'default',
    });
  }

  void previewCosmetic(String cosmeticId, String type, {bool temporary = true}) {
    sendCommand('previewCosmetic', {
      'cosmetic': cosmeticId,
      'type': type,
      'temporary': temporary,
    });
  }

  void applyCosmetic(String cosmeticId, String type) {
    final updatedSettings = Map<String, dynamic>.from(state.avatarSettings);
    updatedSettings[type] = cosmeticId;
    
    state = state.copyWith(avatarSettings: updatedSettings);
    
    sendCommand('applyCosmetic', {
      'cosmetic': cosmeticId,
      'type': type,
      'permanent': true,
    });
  }

  void revertCosmetics() {
    sendCommand('revertCosmetics', {});
  }

  // Utility Methods
  void resetAvatar() {
    changeExpression('neutral');
    stopTalking();
    changeEnvironment(AppConstants.defaultEnvironments.first);
    setCameraMode(CameraMode.defaultMode);
  }

  void resetCamera() {
    setCameraMode(CameraMode.defaultMode);
  }

  @override
  void dispose() {
    if (state.controller != null) {
      // Clean up Unity controller if needed
      sendCommand('cleanup', {});
    }
    super.dispose();
  }
}

// Providers
final unityControllerProvider = StateNotifierProvider<UnityNotifier, UnityState>((ref) {
  return UnityNotifier();
});

final unityInitializedProvider = Provider<bool>((ref) {
  return ref.watch(unityControllerProvider).isInitialized;
});

final currentEnvironmentProvider = Provider<String?>((ref) {
  return ref.watch(unityControllerProvider).currentEnvironment;
});

final currentExpressionProvider = Provider<String?>((ref) {
  return ref.watch(unityControllerProvider).currentExpression;
});

final isTalkingProvider = Provider<bool>((ref) {
  return ref.watch(unityControllerProvider).isTalking;
});

final avatarSettingsProvider = Provider<Map<String, dynamic>>((ref) {
  return ref.watch(unityControllerProvider).avatarSettings;
});

final unityCameraModeProvider = Provider<CameraMode>((ref) {
  return ref.watch(unityControllerProvider).cameraMode;
});

final isCameraTransitioningProvider = Provider<bool>((ref) {
  return ref.watch(unityControllerProvider).isCameraTransitioning;
});

final cameraSettingsProvider = Provider<Map<String, dynamic>>((ref) {
  return ref.watch(unityControllerProvider).cameraSettings;
});

// Unity Command Helper
class UnityCommands {
  static const String startTalking = AppConstants.unityStartTalkingCommand;
  static const String stopTalking = AppConstants.unityStopTalkingCommand;
  static const String changeExpression = AppConstants.unityChangeExpressionCommand;
  static const String changeEnvironment = AppConstants.unityChangeEnvironmentCommand;
  static const String changeOutfit = AppConstants.unityChangeOutfitCommand;
  
  // Camera commands
  static const String setCameraMode = 'setCameraMode';
  static const String setCameraPosition = 'setCameraPosition';
  static const String setCameraZoom = 'setCameraZoom';
  static const String smoothCameraTransition = 'smoothCameraTransition';
  
  // Cosmetic commands
  static const String changeAccessory = 'changeAccessory';
  static const String removeAccessory = 'removeAccessory';
  static const String previewCosmetic = 'previewCosmetic';
  static const String applyCosmetic = 'applyCosmetic';
  static const String revertCosmetics = 'revertCosmetics';
  
  // Additional commands
  static const String playGesture = 'playGesture';
  static const String playAnimation = 'playAnimation';
  static const String setLighting = 'setLighting';
  static const String setCameraAngle = 'setCameraAngle';
  static const String initializeAvatar = 'initializeAvatar';
  static const String cleanup = 'cleanup';
}

// Unity Message Types
class UnityMessageTypes {
  static const String avatarReady = 'avatar_ready';
  static const String animationComplete = 'animation_complete';
  static const String environmentChanged = 'environment_changed';
  static const String expressionChanged = 'expression_changed';
  static const String talkingState = 'talking_state';
  static const String cameraTransitionComplete = 'camera_transition_complete';
  static const String cameraModeChanged = 'camera_mode_changed';
  static const String cameraStateChanged = 'camera_state_changed';
  static const String cosmeticApplied = 'cosmetic_applied';
  static const String cosmeticReverted = 'cosmetic_reverted';
  static const String error = 'error';
}

// Camera preset configurations
class CameraPresets {
  static const Map<String, dynamic> defaultChat = {
    'position': {'x': 0.0, 'y': 1.6, 'z': 2.0},
    'rotation': {'x': 0.0, 'y': 0.0, 'z': 0.0},
    'fov': 60.0,
    'target': 'head',
  };

  static const Map<String, dynamic> fullBodyPreview = {
    'position': {'x': 0.0, 'y': 1.0, 'z': 4.0},
    'rotation': {'x': 0.0, 'y': 0.0, 'z': 0.0},
    'fov': 45.0,
    'target': 'fullBody',
  };

  static const Map<String, dynamic> outfitPreview = {
    'position': {'x': 0.5, 'y': 1.2, 'z': 3.5},
    'rotation': {'x': 5.0, 'y': -10.0, 'z': 0.0},
    'fov': 50.0,
    'target': 'outfit',
  };

  static const Map<String, dynamic> accessoryPreview = {
    'position': {'x': 0.0, 'y': 1.8, 'z': 2.5},
    'rotation': {'x': -5.0, 'y': 0.0, 'z': 0.0},
    'fov': 55.0,
    'target': 'accessories',
  };
}
