import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/chat/message_model.dart';
import '../services/storage/storage_service.dart';
import '../services/auth/auth_service.dart';
import '../services/audio/audio_playback_service.dart';
import '../services/chat/websocket_service.dart';
import '../services/chat/chat_service.dart' as chat_service;
import '../services/voice/tts_streaming_service.dart';
import '../core/constants/app_constants.dart';
import 'auth_provider.dart';
import 'unity_provider.dart';

// Chat State
class ChatState {
  final List<MessageModel> messages;
  final List<chat_service.ConversationModel> conversations;
  final String? currentConversationId;
  final bool isLoading;
  final bool isTyping;
  final bool isConnected;
  final String? error;
  final String? currentTranscription;
  final Map<String, dynamic>? currentEmotion;

  const ChatState({
    required this.messages,
    this.conversations = const [],
    this.currentConversationId,
    this.isLoading = false,
    this.isTyping = false,
    this.isConnected = false,
    this.error,
    this.currentTranscription,
    this.currentEmotion,
  });

  ChatState copyWith({
    List<MessageModel>? messages,
    List<chat_service.ConversationModel>? conversations,
    String? currentConversationId,
    bool? isLoading,
    bool? isTyping,
    bool? isConnected,
    String? error,
    String? currentTranscription,
    Map<String, dynamic>? currentEmotion,
  }) {
    return ChatState(
      messages: messages ?? this.messages,
      conversations: conversations ?? this.conversations,
      currentConversationId: currentConversationId ?? this.currentConversationId,
      isLoading: isLoading ?? this.isLoading,
      isTyping: isTyping ?? this.isTyping,
      isConnected: isConnected ?? this.isConnected,
      error: error ?? this.error,
      currentTranscription: currentTranscription ?? this.currentTranscription,
      currentEmotion: currentEmotion ?? this.currentEmotion,
    );
  }
}

// Chat Notifier
class ChatNotifier extends StateNotifier<ChatState> {
  final WebSocketService _webSocketService;
  final chat_service.ChatService _chatService;
  final TTSStreamingService _ttsService;
  final Ref _ref;

  ChatNotifier(
    this._webSocketService,
    this._chatService,
    this._ttsService,
    this._ref,
  ) : super(const ChatState(messages: [])) {
    _initialize();
  }

  Future<void> _initialize() async {
    print('ChatNotifier: Initializing...');

    // Set up listeners for real-time updates
    _setupChatServiceListeners();
    _setupWebSocketListeners();
    _setupTTSListeners();

    print('ChatNotifier: Connecting to WebSocket...');
    // Connect to WebSocket
    await _connectWebSocket();

    print('ChatNotifier: Loading conversations...');
    // Load conversations and messages
    await _loadConversations();

    print('ChatNotifier: Initialization complete');
  }

  void _setupChatServiceListeners() {
    _chatService.messagesStream.listen((messages) {
      state = state.copyWith(messages: messages);
    });

    _chatService.conversationsStream.listen((conversations) {
      state = state.copyWith(conversations: conversations);
    });

    _chatService.typingStream.listen((isTyping) {
      state = state.copyWith(isTyping: isTyping);
    });

    _chatService.transcriptionStream.listen((transcription) {
      state = state.copyWith(currentTranscription: transcription);
    });

    _chatService.emotionStream.listen((emotion) {
      state = state.copyWith(currentEmotion: emotion);
    });
  }

  void _setupWebSocketListeners() {
    _webSocketService.stateStream.listen((wsState) {
      final isConnected = wsState == WebSocketState.connected;
      state = state.copyWith(isConnected: isConnected);
    });

    _webSocketService.errorStream.listen((error) {
      state = state.copyWith(error: error);
    });
  }

  void _setupTTSListeners() {
    _chatService.ttsAudioStream.listen((audioData) {
      // Handle TTS audio chunks for real-time voice responses
      if (StorageService.isAutoPlayVoiceEnabled()) {
        _handleTTSAudio(audioData);
      }
    });
  }

  Future<void> _connectWebSocket() async {
    try {
      print('ChatNotifier: Calling WebSocket connect...');
      await _webSocketService.connect();
      print('ChatNotifier: WebSocket connect completed');
    } catch (e) {
      print('ChatNotifier: WebSocket connect failed: $e');
      state = state.copyWith(error: 'Failed to connect: $e');
    }
  }

  Future<void> _loadConversations() async {
    try {
      state = state.copyWith(isLoading: true);
      final conversations = await _chatService.loadConversations();

      // If no conversations exist, create a default one
      if (conversations.isEmpty) {
        print('ChatNotifier: No conversations found, creating default conversation');
        await createNewConversation(title: 'Welcome Chat');
      } else if (state.currentConversationId == null) {
        // Set first conversation as current if none is set
        await switchConversation(conversations.first.id);
      }
    } catch (e) {
      state = state.copyWith(error: 'Failed to load conversations: $e');
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  Future<void> sendMessage(String content) async {
    if (content.trim().isEmpty) return;

    final user = _ref.read(currentUserProvider);
    if (user == null) return;

    // Trigger Unity animation for user speaking
    _ref.read(unityControllerProvider.notifier).sendCommand(
      AppConstants.unityStartTalkingCommand,
      {'isUser': true},
    );

    try {
      // Send message via chat service (handles WebSocket communication)
      await _chatService.sendTextMessage(content);

      // Update user progress
      await _updateProgress(content, MessageType.text);

    } catch (e) {
      state = state.copyWith(error: e.toString());
    } finally {
      // Stop Unity talking animation
      _ref.read(unityControllerProvider.notifier).sendCommand(
        AppConstants.unityStopTalkingCommand,
        {},
      );
    }
  }

  Future<void> sendVoiceMessage(String audioPath, String transcription) async {
    final user = _ref.read(currentUserProvider);
    if (user == null) return;

    try {
      // Send transcription as text message
      await sendMessage(transcription);

      // Update progress for voice message
      await _updateProgress(transcription, MessageType.voice);

    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  Future<void> createNewConversation({String? title}) async {
    try {
      state = state.copyWith(isLoading: true);
      final conversation = await _chatService.createConversation(title: title);
      await switchConversation(conversation.id);
    } catch (e) {
      state = state.copyWith(error: 'Failed to create conversation: $e');
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  Future<void> switchConversation(String conversationId) async {
    try {
      state = state.copyWith(isLoading: true, currentConversationId: conversationId);
      await _chatService.switchConversation(conversationId);
    } catch (e) {
      state = state.copyWith(error: 'Failed to switch conversation: $e');
    } finally {
      state = state.copyWith(isLoading: false);
    }
  }

  Future<void> deleteConversation(String conversationId) async {
    try {
      await _chatService.deleteConversation(conversationId);

      // If we deleted the current conversation, clear it
      if (state.currentConversationId == conversationId) {
        state = state.copyWith(
          currentConversationId: null,
          messages: [],
        );
      }
    } catch (e) {
      state = state.copyWith(error: 'Failed to delete conversation: $e');
    }
  }

  void _handleTTSAudio(Map<String, dynamic> audioData) {
    // Stream TTS audio for real-time voice responses
    final text = audioData['text'] as String?;
    if (text != null && text.isNotEmpty) {
      _ttsService.streamTTSResponse(
        text: text,
        voiceSettings: VoiceSettings(
          voiceName: 'nova', // Could be user preference
          emotionContext: state.currentEmotion != null
              ? EmotionContext(
                  primaryEmotion: state.currentEmotion!['primary_emotion'] ?? 'neutral',
                  intensity: (state.currentEmotion!['intensity'] ?? 0.5).toDouble(),
                )
              : null,
        ),
      );
    }
  }

  Future<void> _updateProgress(String content, MessageType messageType) async {
    final user = _ref.read(currentUserProvider);
    if (user == null) return;

    // Add XP based on message type
    int xpGain = messageType == MessageType.voice
        ? AppConstants.baseXpPerVoiceMessage
        : AppConstants.baseXpPerMessage;

    await StorageService.addUserXP(xpGain);

    // Update message counts
    final currentProgress = user.progress;
    final updatedProgress = currentProgress.copyWith(
      messagesCount: currentProgress.messagesCount + 1,
      voiceMessagesCount: messageType == MessageType.voice
          ? currentProgress.voiceMessagesCount + 1
          : currentProgress.voiceMessagesCount,
    );

    // Update user with new progress
    final updatedUser = user.copyWith(progress: updatedProgress);
    await StorageService.saveUser(updatedUser);

    // Update auth state
    _ref.read(authProvider.notifier).updateUserProfile();

    // Check for level up
    final newLevel = StorageService.getUserLevel();
    if (newLevel > currentProgress.level) {
      _handleLevelUp(newLevel);
    }
  }

  void _handleLevelUp(int newLevel) {
    // Award hearts for leveling up
    StorageService.addUserHearts(AppConstants.heartsPerLevel);

    // Trigger Unity celebration animation
    _ref.read(unityControllerProvider.notifier).sendCommand(
      AppConstants.unityChangeExpressionCommand,
      {'expression': 'celebration'},
    );
  }

  void clearChat() {
    if (state.currentConversationId != null) {
      deleteConversation(state.currentConversationId!);
    }
  }

  void retryLastMessage() {
    if (state.messages.isEmpty) return;

    final lastUserMessage = state.messages
        .lastWhere((m) => m.isFromUser, orElse: () => state.messages.last);

    if (lastUserMessage.isFromUser) {
      sendMessage(lastUserMessage.content);
    }
  }

  // Connection management
  Future<void> reconnect() async {
    await _connectWebSocket();
  }

  Future<void> ensureConnected() async {
    if (!_webSocketService.isConnected) {
      await _connectWebSocket();
    }
  }

  void disconnect() {
    _webSocketService.disconnect();
  }

  // Typing indicators
  void startTyping() {
    _webSocketService.sendTypingStart();
  }

  void stopTyping() {
    _webSocketService.sendTypingStop();
  }

  // Emotion feedback
  void sendEmotionFeedback({
    required String emotion,
    required double intensity,
    String? context,
  }) {
    _webSocketService.sendEmotionFeedback(
      emotion: emotion,
      intensity: intensity,
      context: context,
    );
  }

  @override
  void dispose() {
    _chatService.dispose();
    _ttsService.dispose();
    super.dispose();
  }
}

// Service Providers
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService();
});

final webSocketServiceProvider = Provider<WebSocketService>((ref) {
  return WebSocketService(ref.read(authServiceProvider));
});

final chatServiceProvider = Provider<chat_service.ChatService>((ref) {
  return chat_service.ChatService(
    ref.read(authServiceProvider),
    ref.read(webSocketServiceProvider),
    ref.read(audioPlaybackServiceProvider),
  );
});

final ttsStreamingServiceProvider = Provider<TTSStreamingService>((ref) {
  return TTSStreamingService(ref.read(webSocketServiceProvider));
});

// Chat Provider
final chatProvider = StateNotifierProvider<ChatNotifier, ChatState>((ref) {
  return ChatNotifier(
    ref.read(webSocketServiceProvider),
    ref.read(chatServiceProvider),
    ref.read(ttsStreamingServiceProvider),
    ref,
  );
});

// Chat State Providers
final chatMessagesProvider = Provider<List<MessageModel>>((ref) {
  return ref.watch(chatProvider).messages;
});

final chatConversationsProvider = Provider<List<chat_service.ConversationModel>>((ref) {
  return ref.watch(chatProvider).conversations;
});

final currentConversationIdProvider = Provider<String?>((ref) {
  return ref.watch(chatProvider).currentConversationId;
});

final isAITypingProvider = Provider<bool>((ref) {
  return ref.watch(chatProvider).isTyping;
});

final isChatLoadingProvider = Provider<bool>((ref) {
  return ref.watch(chatProvider).isLoading;
});

final isChatConnectedProvider = Provider<bool>((ref) {
  return ref.watch(chatProvider).isConnected;
});

final chatErrorProvider = Provider<String?>((ref) {
  return ref.watch(chatProvider).error;
});

final currentTranscriptionProvider = Provider<String?>((ref) {
  return ref.watch(chatProvider).currentTranscription;
});

final currentEmotionProvider = Provider<Map<String, dynamic>?>((ref) {
  return ref.watch(chatProvider).currentEmotion;
});

// Utility providers
final messageCountProvider = Provider<int>((ref) {
  return ref.watch(chatMessagesProvider).length;
});

final lastMessageProvider = Provider<MessageModel?>((ref) {
  final messages = ref.watch(chatMessagesProvider);
  return messages.isNotEmpty ? messages.last : null;
});

final unreadMessageCountProvider = Provider<int>((ref) {
  final messages = ref.watch(chatMessagesProvider);
  return messages.where((m) => !m.isRead && !m.isFromUser).length;
});

final currentConversationProvider = Provider<chat_service.ConversationModel?>((ref) {
  final conversations = ref.watch(chatConversationsProvider);
  final currentId = ref.watch(currentConversationIdProvider);

  if (currentId == null) return null;

  try {
    return conversations.firstWhere((c) => c.id == currentId);
  } catch (e) {
    return null;
  }
});


