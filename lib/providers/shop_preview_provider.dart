import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/shop/shop_item_model.dart';
import '../models/shop/shop_preview_models.dart';
import '../models/user/user_model.dart';
import 'auth_provider.dart';
import 'shop_provider.dart';
import 'unity_provider.dart';
import '../services/relationship/relationship_gate_controller.dart';
import '../services/companion/companion_reaction_service.dart';

// Shop Preview Notifier
class ShopPreviewNotifier extends StateNotifier<ShopPreviewState> {
  final Ref _ref;

  ShopPreviewNotifier(this._ref) : super(const ShopPreviewState());

  // Enter preview mode for a specific category
  Future<void> enterPreviewMode(
    ShopItemModel item,
    ShopItemType category,
  ) async {
    print('Entering preview mode for ${item.name} in category ${category.name}');
    if (state.isInPreviewMode) {
      print('Already in preview mode, returning');
      return;
    }

    print('Setting transitioning state');
    state = state.copyWith(isTransitioning: true);

    try {
      // Get all items in the category
      final shopState = _ref.read(shopProvider);
      final categoryItems = _getCategoryItems(shopState, category);

      // Filter items based on relationship level - temporarily simplified
      print('Filtering ${categoryItems.length} items by relationship level');
      // final relationshipGate = _ref.read(relationshipGateProvider.notifier);
      // final filteredItems = relationshipGate.filterItems(categoryItems);
      final filteredItems = (available: categoryItems, locked: <ShopItemModel>[]);
      print('Filtered items: ${filteredItems.available.length} available, ${filteredItems.locked.length} locked');

      // Set up cosmetic state with current equipped items
      final user = _ref.read(currentUserProvider);
      final equippedCosmetics = _getCurrentEquippedCosmetics(user);
      final cosmeticState = CosmeticApplicationState(
        equippedCosmetics: equippedCosmetics,
        appliedCosmetics: Map.from(equippedCosmetics),
      );

      // Update state
      print('Updating state to preview mode');
      state = state.copyWith(
        isInPreviewMode: true,
        currentCategory: category,
        selectedItem: item,
        categoryItems: categoryItems,
        availableItems: filteredItems.available,
        lockedItems: filteredItems.locked,
        cosmeticState: cosmeticState,
        cameraState: state.cameraState.copyWith(
          mode: CameraMode.preview,
          isTransitioning: true,
        ),
        isTransitioning: false,
      );
      print('Preview mode state updated: isInPreviewMode = ${state.isInPreviewMode}');

      // Trigger camera transition in Unity
      await _transitionCameraToPreview();

      // Apply the selected item
      await selectPreviewItem(item);
    } catch (e) {
      print('ERROR in enterPreviewMode: $e');
      print('Stack trace: ${StackTrace.current}');
      state = state.copyWith(
        isTransitioning: false,
        error: 'Failed to enter preview mode: ${e.toString()}',
      );
    }
  }

  // Exit preview mode
  Future<void> exitPreviewMode() async {
    if (!state.isInPreviewMode) return;

    state = state.copyWith(isTransitioning: true);

    try {
      // Revert to equipped cosmetics
      await _revertToEquippedCosmetics();

      // Transition camera back to default
      await _transitionCameraToDefault();

      // Clear preview state
      state = const ShopPreviewState();
    } catch (e) {
      state = state.copyWith(
        isTransitioning: false,
        error: 'Failed to exit preview mode: ${e.toString()}',
      );
    }
  }

  // Select a different item for preview
  Future<void> selectPreviewItem(ShopItemModel item) async {
    if (!state.isInPreviewMode) return;

    // Check if item is locked
    final relationshipGate = _ref.read(relationshipGateProvider.notifier);
    final isLocked = !relationshipGate.isItemUnlocked(item);

    if (isLocked) {
      // Generate locked item dialogue and reaction
      final dialogue = await relationshipGate.generateLockedItemDialogue(item);
      final reactionService = _ref.read(companionReactionServiceProvider);
      final reaction = await reactionService.generateLockedItemReaction(item);

      // Update reaction text with generated dialogue
      final updatedReaction = CompanionReaction(
        text: dialogue,
        animation: reaction.animation,
        emotion: reaction.emotion,
        type: reaction.type,
        duration: reaction.duration,
      );

      state = state.copyWith(currentReaction: updatedReaction);
      return;
    }

    state = state.copyWith(
      selectedItem: item,
      cosmeticState: state.cosmeticState.copyWith(isApplying: true),
    );

    try {
      // Apply cosmetic in Unity
      await _applyCosmeticInUnity(item);

      // Generate companion reaction
      final reactionService = _ref.read(companionReactionServiceProvider);
      final reaction = await reactionService.generatePreviewReaction(item);

      state = state.copyWith(
        cosmeticState: state.cosmeticState.copyWith(
          appliedCosmetics: {
            ...state.cosmeticState.appliedCosmetics,
            item.type: item.id,
          },
          isApplying: false,
        ),
        currentReaction: reaction,
      );
    } catch (e) {
      state = state.copyWith(
        cosmeticState: state.cosmeticState.copyWith(isApplying: false),
        error: 'Failed to apply cosmetic: ${e.toString()}',
      );
    }
  }

  // Purchase current item
  Future<void> purchaseCurrentItem() async {
    final selectedItem = state.selectedItem;
    if (selectedItem == null || !state.isInPreviewMode) return;

    // Set purchasing state
    state = state.copyWith(isTransitioning: true);

    try {
      final user = _ref.read(currentUserProvider);
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Validate purchase conditions
      final canAfford = user.progress.hearts >= selectedItem.price;
      if (!canAfford) {
        throw Exception('Insufficient hearts');
      }

      final isOwned = _isItemOwned(selectedItem, user);
      if (isOwned) {
        throw Exception('Item already owned');
      }

      // Check relationship requirements
      final relationshipGate = _ref.read(relationshipGateProvider.notifier);
      final isUnlocked = relationshipGate.isItemUnlocked(selectedItem);
      if (!isUnlocked) {
        throw Exception('Item locked by relationship level');
      }

      // Perform purchase through shop provider
      await _ref.read(shopProvider.notifier).purchaseItem(selectedItem.id);

      // Update cosmetic state to reflect ownership
      final updatedEquipped = Map<ShopItemType, String>.from(
        state.cosmeticState.equippedCosmetics,
      );
      updatedEquipped[selectedItem.type] = selectedItem.id;

      // Generate purchase success reaction
      final reactionService = _ref.read(companionReactionServiceProvider);
      final reaction = await reactionService.generatePurchaseSuccessReaction(
        selectedItem,
      );

      // Update state with success
      state = state.copyWith(
        currentReaction: reaction,
        cosmeticState: state.cosmeticState.copyWith(
          equippedCosmetics: updatedEquipped,
          appliedCosmetics: updatedEquipped,
        ),
        isTransitioning: false,
        error: null,
      );

      // Trigger celebration animation in Unity
      _ref.read(unityControllerProvider.notifier).playAnimation('celebration');
    } catch (e) {
      // Generate purchase failure reaction
      final reactionService = _ref.read(companionReactionServiceProvider);
      final reaction = await reactionService.generatePurchaseDeclineReaction(
        selectedItem,
      );

      state = state.copyWith(
        currentReaction: reaction,
        isTransitioning: false,
        error: 'Purchase failed: ${e.toString()}',
      );

      // Show error feedback
      _showPurchaseError(e.toString());
    }
  }

  // Show purchase confirmation dialog
  Future<bool> showPurchaseConfirmation(ShopItemModel item) async {
    // This would typically show a dialog, but for now return true
    // In a real implementation, this would integrate with the UI layer
    return true;
  }

  // Handle purchase error feedback
  void _showPurchaseError(String error) {
    // This could trigger a snackbar or other error UI
    // For now, we just log the error
    print('Purchase error: $error');
  }

  // Check if item is owned by user
  bool _isItemOwned(ShopItemModel item, user) {
    if (user == null) return false;

    switch (item.type) {
      case ShopItemType.environment:
        return user.ownedEnvironments.contains(item.id);
      case ShopItemType.outfit:
      case ShopItemType.accessory:
      case ShopItemType.companion:
      case ShopItemType.pet:
        return user.ownedOutfits.contains(item.id);
      default:
        return false;
    }
  }

  // Switch category while in preview mode
  Future<void> switchCategory(ShopItemType newCategory) async {
    if (!state.isInPreviewMode || state.currentCategory == newCategory) return;

    state = state.copyWith(isTransitioning: true);

    try {
      final shopState = _ref.read(shopProvider);
      final categoryItems = _getCategoryItems(shopState, newCategory);

      final relationshipGate = _ref.read(relationshipGateProvider.notifier);
      final filteredItems = relationshipGate.filterItems(categoryItems);

      // Select first available item in new category
      final firstItem =
          filteredItems.available.isNotEmpty
              ? filteredItems.available.first
              : categoryItems.first;

      state = state.copyWith(
        currentCategory: newCategory,
        categoryItems: categoryItems,
        availableItems: filteredItems.available,
        lockedItems: filteredItems.locked,
        selectedItem: firstItem,
        isTransitioning: false,
      );

      // Apply the first item
      await selectPreviewItem(firstItem);
    } catch (e) {
      state = state.copyWith(
        isTransitioning: false,
        error: 'Failed to switch category: ${e.toString()}',
      );
    }
  }

  // Clear current error
  void clearError() {
    state = state.copyWith(error: null);
  }

  // Clear current reaction
  void clearReaction() {
    state = state.copyWith(currentReaction: null);
  }

  // Private helper methods
  List<ShopItemModel> _getCategoryItems(
    ShopState shopState,
    ShopItemType category,
  ) {
    switch (category) {
      case ShopItemType.environment:
        return shopState.environments;
      case ShopItemType.outfit:
        return shopState.outfits;
      case ShopItemType.accessory:
        return shopState.accessories;
      case ShopItemType.companion:
        return shopState.companions;
      case ShopItemType.pet:
        return shopState.pets;
      default:
        return [];
    }
  }

  Map<ShopItemType, String> _getCurrentEquippedCosmetics(UserModel? user) {
    if (user == null) return {};

    return {
      ShopItemType.environment: user.selectedEnvironment,
      // Add other equipped cosmetics based on user model
    };
  }

  Future<void> _transitionCameraToPreview() async {
    _ref.read(unityControllerProvider.notifier).sendCommand('setCameraMode', {
      'mode': 'preview',
      'duration': state.cameraState.transitionDuration.inMilliseconds,
    });

    // Wait for transition to complete
    await Future.delayed(state.cameraState.transitionDuration);

    state = state.copyWith(
      cameraState: state.cameraState.copyWith(isTransitioning: false),
    );
  }

  Future<void> _transitionCameraToDefault() async {
    _ref.read(unityControllerProvider.notifier).sendCommand('setCameraMode', {
      'mode': 'default',
      'duration': state.cameraState.transitionDuration.inMilliseconds,
    });

    await Future.delayed(state.cameraState.transitionDuration);
  }

  Future<void> _applyCosmeticInUnity(ShopItemModel item) async {
    final unityController = _ref.read(unityControllerProvider.notifier);

    switch (item.type) {
      case ShopItemType.environment:
        unityController.changeEnvironment(item.id);
        break;
      case ShopItemType.outfit:
        unityController.changeOutfit(item.id);
        break;
      case ShopItemType.accessory:
        unityController.sendCommand('changeAccessory', {'accessory': item.id});
        break;
      case ShopItemType.companion:
        unityController.sendCommand('previewCompanion', {
          'companionId': item.id,
          'personality': item.metadata['personality'] ?? 'caringFriend',
        });
        break;
      case ShopItemType.pet:
        unityController.sendCommand('previewPet', {
          'petId': item.id,
          'petType': item.metadata['petType'] ?? 'cat',
        });
        break;
      default:
        break;
    }
  }

  Future<void> _revertToEquippedCosmetics() async {
    final equippedCosmetics = state.cosmeticState.equippedCosmetics;

    for (final entry in equippedCosmetics.entries) {
      final type = entry.key;
      final itemId = entry.value;

      // Create a temporary item model for reverting
      final tempItem = ShopItemModel(
        id: itemId,
        name: '',
        description: '',
        type: type,
        rarity: ShopItemRarity.common,
        price: 0,
      );

      await _applyCosmeticInUnity(tempItem);
    }
  }
}

// Providers
final shopPreviewProvider =
    StateNotifierProvider<ShopPreviewNotifier, ShopPreviewState>((ref) {
      return ShopPreviewNotifier(ref);
    });

// Convenience providers
final isInPreviewModeProvider = Provider<bool>((ref) {
  return ref.watch(shopPreviewProvider).isInPreviewMode;
});

final selectedPreviewItemProvider = Provider<ShopItemModel?>((ref) {
  return ref.watch(shopPreviewProvider).selectedItem;
});

final previewCameraStateProvider = Provider<PreviewCameraState>((ref) {
  return ref.watch(shopPreviewProvider).cameraState;
});

final companionReactionProvider = Provider<CompanionReaction?>((ref) {
  return ref.watch(shopPreviewProvider).currentReaction;
});

final previewAvailableItemsProvider = Provider<List<ShopItemModel>>((ref) {
  return ref.watch(shopPreviewProvider).availableItems;
});

final previewLockedItemsProvider = Provider<List<ShopItemModel>>((ref) {
  return ref.watch(shopPreviewProvider).lockedItems;
});
