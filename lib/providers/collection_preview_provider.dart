import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:equatable/equatable.dart';
import '../models/shop/shop_item_model.dart';
import '../models/shop/shop_preview_models.dart';
import '../models/user/user_model.dart';
import 'auth_provider.dart';
import 'unity_provider.dart';
import 'shop_provider.dart';

// Collection Preview State
class CollectionPreviewState extends Equatable {
  final bool isInPreviewMode;
  final ShopItemModel? selectedItem;
  final ShopItemType? currentCategory;
  final List<ShopItemModel> ownedItems;
  final List<ShopItemModel> categoryItems;
  final CosmeticApplicationState cosmeticState;
  final PreviewCameraState cameraState;
  final bool isTransitioning;
  final String? error;
  final CompanionReaction? currentReaction;

  const CollectionPreviewState({
    this.isInPreviewMode = false,
    this.selectedItem,
    this.currentCategory,
    this.ownedItems = const [],
    this.categoryItems = const [],
    this.cosmeticState = const CosmeticApplicationState(),
    this.cameraState = const PreviewCameraState(),
    this.isTransitioning = false,
    this.error,
    this.currentReaction,
  });

  CollectionPreviewState copyWith({
    bool? isInPreviewMode,
    ShopItemModel? selectedItem,
    ShopItemType? currentCategory,
    List<ShopItemModel>? ownedItems,
    List<ShopItemModel>? categoryItems,
    CosmeticApplicationState? cosmeticState,
    PreviewCameraState? cameraState,
    bool? isTransitioning,
    String? error,
    CompanionReaction? currentReaction,
  }) {
    return CollectionPreviewState(
      isInPreviewMode: isInPreviewMode ?? this.isInPreviewMode,
      selectedItem: selectedItem ?? this.selectedItem,
      currentCategory: currentCategory ?? this.currentCategory,
      ownedItems: ownedItems ?? this.ownedItems,
      categoryItems: categoryItems ?? this.categoryItems,
      cosmeticState: cosmeticState ?? this.cosmeticState,
      cameraState: cameraState ?? this.cameraState,
      isTransitioning: isTransitioning ?? this.isTransitioning,
      error: error ?? this.error,
      currentReaction: currentReaction ?? this.currentReaction,
    );
  }

  @override
  List<Object?> get props => [
        isInPreviewMode,
        selectedItem,
        currentCategory,
        ownedItems,
        categoryItems,
        cosmeticState,
        cameraState,
        isTransitioning,
        error,
        currentReaction,
      ];
}

// Collection Preview Notifier
class CollectionPreviewNotifier extends StateNotifier<CollectionPreviewState> {
  final Ref _ref;

  CollectionPreviewNotifier(this._ref) : super(const CollectionPreviewState());

  // Enter preview mode for a collection item
  Future<void> enterPreviewMode(ShopItemModel item, ShopItemType category) async {
    if (state.isInPreviewMode) return;

    state = state.copyWith(isTransitioning: true);

    try {
      print('Entering collection preview mode for item: ${item.name}');

      // Get user and validate ownership
      final user = _ref.read(currentUserProvider);
      if (user == null) {
        throw Exception('User not authenticated');
      }

      if (!_isItemOwned(item, user)) {
        throw Exception('Item not owned');
      }

      // Get all owned items for the category
      final shopState = _ref.read(shopProvider);
      final categoryItems = _getCategoryItems(shopState, category);
      final ownedItems = _getOwnedItemsForCategory(user, categoryItems, category);

      // Set up cosmetic state with current equipped items
      final equippedCosmetics = _getCurrentEquippedCosmetics(user);
      final cosmeticState = CosmeticApplicationState(
        equippedCosmetics: equippedCosmetics,
        appliedCosmetics: Map.from(equippedCosmetics),
      );

      // Apply the selected item in Unity for preview
      await _applyCosmeticInUnity(item);

      // Transition camera to preview mode
      await _transitionCameraToPreview();

      // Update state
      print('Updating state to collection preview mode');
      state = state.copyWith(
        isInPreviewMode: true,
        currentCategory: category,
        selectedItem: item,
        categoryItems: categoryItems,
        ownedItems: ownedItems,
        cosmeticState: cosmeticState,
        cameraState: state.cameraState.copyWith(
          mode: CameraMode.preview,
          isTransitioning: true,
        ),
        isTransitioning: false,
      );

      print('Collection preview mode entered successfully');
    } catch (e) {
      print('Error entering collection preview mode: $e');
      state = state.copyWith(
        isTransitioning: false,
        error: 'Failed to enter preview mode: ${e.toString()}',
      );
    }
  }

  // Exit preview mode
  Future<void> exitPreviewMode() async {
    if (!state.isInPreviewMode) return;

    state = state.copyWith(isTransitioning: true);

    try {
      print('Exiting collection preview mode');

      // Revert to equipped cosmetics
      await _revertToEquippedCosmetics();

      // Transition camera back to default
      await _transitionCameraToDefault();

      // Clear preview state
      state = const CollectionPreviewState();

      print('Collection preview mode exited successfully');
    } catch (e) {
      print('Error exiting collection preview mode: $e');
      state = state.copyWith(
        isTransitioning: false,
        error: 'Failed to exit preview mode: ${e.toString()}',
      );
    }
  }

  // Select a different item for preview
  Future<void> selectPreviewItem(ShopItemModel item) async {
    if (!state.isInPreviewMode) return;

    final user = _ref.read(currentUserProvider);
    if (user == null || !_isItemOwned(item, user)) {
      return;
    }

    state = state.copyWith(isTransitioning: true);

    try {
      // Apply the new item in Unity
      await _applyCosmeticInUnity(item);

      // Update selected item
      state = state.copyWith(
        selectedItem: item,
        isTransitioning: false,
      );
    } catch (e) {
      state = state.copyWith(
        isTransitioning: false,
        error: 'Failed to select item: ${e.toString()}',
      );
    }
  }

  // Equip the currently previewed item
  Future<void> equipPreviewedItem() async {
    final selectedItem = state.selectedItem;
    if (!state.isInPreviewMode || selectedItem == null) return;

    state = state.copyWith(isTransitioning: true);

    try {
      final user = _ref.read(currentUserProvider);
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Equip the item in user profile
      await _equipItemInUserProfile(selectedItem, user);

      // Update cosmetic state to reflect the new equipped item
      final updatedEquippedCosmetics = Map<ShopItemType, String>.from(state.cosmeticState.equippedCosmetics);
      updatedEquippedCosmetics[selectedItem.type] = selectedItem.id;

      state = state.copyWith(
        cosmeticState: state.cosmeticState.copyWith(
          equippedCosmetics: updatedEquippedCosmetics,
          appliedCosmetics: Map.from(updatedEquippedCosmetics),
        ),
        isTransitioning: false,
      );

      print('Item equipped successfully: ${selectedItem.name}');
    } catch (e) {
      state = state.copyWith(
        isTransitioning: false,
        error: 'Failed to equip item: ${e.toString()}',
      );
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  // Helper methods
  List<ShopItemModel> _getCategoryItems(ShopState shopState, ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return shopState.environments;
      case ShopItemType.outfit:
        return shopState.outfits;
      case ShopItemType.accessory:
        return shopState.accessories;
      case ShopItemType.companion:
        return shopState.companions;
      case ShopItemType.pet:
        return shopState.pets;
      default:
        return [];
    }
  }

  List<ShopItemModel> _getOwnedItemsForCategory(
    UserModel user,
    List<ShopItemModel> categoryItems,
    ShopItemType type,
  ) {
    return categoryItems.where((item) => _isItemOwned(item, user)).toList();
  }

  bool _isItemOwned(ShopItemModel item, UserModel user) {
    switch (item.type) {
      case ShopItemType.environment:
        return user.ownedEnvironments.contains(item.id);
      case ShopItemType.outfit:
      case ShopItemType.accessory:
      case ShopItemType.companion:
      case ShopItemType.pet:
        return user.ownedOutfits.contains(item.id);
      default:
        return false;
    }
  }

  Map<ShopItemType, String> _getCurrentEquippedCosmetics(UserModel user) {
    return {
      ShopItemType.environment: user.selectedEnvironment,
      // Add other equipped cosmetics based on user model
    };
  }

  Future<void> _transitionCameraToPreview() async {
    _ref.read(unityControllerProvider.notifier).sendCommand('setCameraMode', {
      'mode': 'preview',
      'duration': state.cameraState.transitionDuration.inMilliseconds,
    });

    // Wait for transition to complete
    await Future.delayed(state.cameraState.transitionDuration);

    state = state.copyWith(
      cameraState: state.cameraState.copyWith(isTransitioning: false),
    );
  }

  Future<void> _transitionCameraToDefault() async {
    _ref.read(unityControllerProvider.notifier).sendCommand('setCameraMode', {
      'mode': 'default',
      'duration': state.cameraState.transitionDuration.inMilliseconds,
    });

    // Wait for transition to complete
    await Future.delayed(state.cameraState.transitionDuration);
  }

  Future<void> _applyCosmeticInUnity(ShopItemModel item) async {
    final unityController = _ref.read(unityControllerProvider.notifier);

    switch (item.type) {
      case ShopItemType.environment:
        unityController.changeEnvironment(item.id);
        break;
      case ShopItemType.outfit:
        unityController.changeOutfit(item.id);
        break;
      case ShopItemType.accessory:
        unityController.sendCommand('changeAccessory', {'accessory': item.id});
        break;
      case ShopItemType.companion:
        unityController.sendCommand('changeCompanion', {
          'companionId': item.id,
          'personality': item.metadata['personality'] ?? 'caringFriend',
        });
        break;
      case ShopItemType.pet:
        unityController.sendCommand('changePet', {'petId': item.id});
        break;
      default:
        break;
    }

    // Update applied cosmetics state
    final updatedAppliedCosmetics = Map<ShopItemType, String>.from(state.cosmeticState.appliedCosmetics);
    updatedAppliedCosmetics[item.type] = item.id;

    state = state.copyWith(
      cosmeticState: state.cosmeticState.copyWith(
        appliedCosmetics: updatedAppliedCosmetics,
      ),
    );
  }

  Future<void> _revertToEquippedCosmetics() async {
    final equippedCosmetics = state.cosmeticState.equippedCosmetics;

    for (final entry in equippedCosmetics.entries) {
      final type = entry.key;
      final itemId = entry.value;

      // Create a temporary item model for reverting
      final tempItem = ShopItemModel(
        id: itemId,
        name: '',
        description: '',
        type: type,
        rarity: ShopItemRarity.common,
        price: 0,
      );

      await _applyCosmeticInUnity(tempItem);
    }
  }

  Future<void> _equipItemInUserProfile(ShopItemModel item, UserModel user) async {
    // For now, use mock equipment state - save to storage
    switch (item.type) {
      case ShopItemType.environment:
        // Update user's selected environment
        // This would typically update the user model in the auth provider
        break;
      case ShopItemType.outfit:
        // Update user's selected outfit
        break;
      case ShopItemType.companion:
        // Update user's selected companion
        break;
      case ShopItemType.accessory:
        // Update user's selected accessory
        break;
      case ShopItemType.pet:
        // Update user's selected pet
        break;
      default:
        break;
    }
  }
}

// Providers
final collectionPreviewProvider =
    StateNotifierProvider<CollectionPreviewNotifier, CollectionPreviewState>((ref) {
      return CollectionPreviewNotifier(ref);
    });

// Convenience providers
final isInCollectionPreviewModeProvider = Provider<bool>((ref) {
  return ref.watch(collectionPreviewProvider).isInPreviewMode;
});

final selectedCollectionPreviewItemProvider = Provider<ShopItemModel?>((ref) {
  return ref.watch(collectionPreviewProvider).selectedItem;
});

final collectionPreviewCameraStateProvider = Provider<PreviewCameraState>((ref) {
  return ref.watch(collectionPreviewProvider).cameraState;
});

final collectionPreviewOwnedItemsProvider = Provider<List<ShopItemModel>>((ref) {
  return ref.watch(collectionPreviewProvider).ownedItems;
});
