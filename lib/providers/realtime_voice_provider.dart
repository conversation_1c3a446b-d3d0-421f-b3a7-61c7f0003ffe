import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../services/voice/realtime_voice_service.dart';

class RealtimeVoiceState {
  final VoiceState voiceState;
  final bool isInitialized;
  final String? error;
  final String? lastTranscription;
  final double? lastConfidence;
  final Duration? lastProcessingTime;
  final List<String> vadEvents;
  final bool isListening;

  const RealtimeVoiceState({
    this.voiceState = VoiceState.idle,
    this.isInitialized = false,
    this.error,
    this.lastTranscription,
    this.lastConfidence,
    this.lastProcessingTime,
    this.vadEvents = const [],
    this.isListening = false,
  });

  RealtimeVoiceState copyWith({
    VoiceState? voiceState,
    bool? isInitialized,
    String? error,
    String? lastTranscription,
    double? lastConfidence,
    Duration? lastProcessingTime,
    List<String>? vadEvents,
    bool? isListening,
  }) {
    return RealtimeVoiceState(
      voiceState: voiceState ?? this.voiceState,
      isInitialized: isInitialized ?? this.isInitialized,
      error: error,
      lastTranscription: lastTranscription ?? this.lastTranscription,
      lastConfidence: lastConfidence ?? this.lastConfidence,
      lastProcessingTime: lastProcessingTime ?? this.lastProcessingTime,
      vadEvents: vadEvents ?? this.vadEvents,
      isListening: isListening ?? this.isListening,
    );
  }
}

class RealtimeVoiceNotifier extends StateNotifier<RealtimeVoiceState> {
  final RealtimeVoiceService _voiceService;

  RealtimeVoiceNotifier(this._voiceService) : super(const RealtimeVoiceState()) {
    _initializeListeners();
  }

  void _initializeListeners() {
    // Listen to voice state changes
    _voiceService.stateStream.listen((voiceState) {
      state = state.copyWith(
        voiceState: voiceState,
        isListening: voiceState == VoiceState.listening,
        error: voiceState == VoiceState.error ? state.error : null,
      );
    });

    // Listen to transcription results
    _voiceService.transcriptionStream.listen((result) {
      state = state.copyWith(
        lastTranscription: result.transcription,
        lastConfidence: result.confidence,
        lastProcessingTime: result.processingTime,
        error: null,
      );
      
      debugPrint('RealtimeVoiceProvider: New transcription: "${result.transcription}"');
      debugPrint('RealtimeVoiceProvider: Processing time: ${result.processingTime.inMilliseconds}ms');
    });

    // Listen to errors
    _voiceService.errorStream.listen((error) {
      state = state.copyWith(error: error);
      debugPrint('RealtimeVoiceProvider: Error - $error');
    });

    // Listen to VAD events
    _voiceService.vadEventsStream.listen((event) {
      final updatedEvents = [...state.vadEvents, event];
      // Keep only the last 10 events to prevent memory issues
      final limitedEvents = updatedEvents.length > 10 
          ? updatedEvents.sublist(updatedEvents.length - 10)
          : updatedEvents;
      
      state = state.copyWith(vadEvents: limitedEvents);
      debugPrint('RealtimeVoiceProvider: VAD Event - $event');
    });
  }

  /// Initialize the voice service
  Future<bool> initialize() async {
    try {
      debugPrint('RealtimeVoiceProvider: Initializing voice service...');
      final success = await _voiceService.initialize();
      
      state = state.copyWith(
        isInitialized: success,
        error: success ? null : 'Failed to initialize voice service',
      );
      
      if (success) {
        debugPrint('RealtimeVoiceProvider: Voice service initialized successfully');
      } else {
        debugPrint('RealtimeVoiceProvider: Voice service initialization failed');
      }
      
      return success;
    } catch (e) {
      final errorMessage = 'Failed to initialize voice service: $e';
      state = state.copyWith(
        isInitialized: false,
        error: errorMessage,
      );
      debugPrint('RealtimeVoiceProvider: $errorMessage');
      return false;
    }
  }

  /// Start listening for voice input
  Future<bool> startListening() async {
    if (!state.isInitialized) {
      state = state.copyWith(error: 'Voice service not initialized');
      return false;
    }

    if (state.isListening) {
      debugPrint('RealtimeVoiceProvider: Already listening');
      return true;
    }

    try {
      debugPrint('RealtimeVoiceProvider: Starting voice listening...');
      final success = await _voiceService.startListening();
      
      if (!success) {
        state = state.copyWith(error: 'Failed to start listening');
      } else {
        // Clear previous transcription when starting new session
        state = state.copyWith(
          lastTranscription: null,
          lastConfidence: null,
          lastProcessingTime: null,
          error: null,
        );
        debugPrint('RealtimeVoiceProvider: Voice listening started');
      }
      
      return success;
    } catch (e) {
      final errorMessage = 'Failed to start listening: $e';
      state = state.copyWith(error: errorMessage);
      debugPrint('RealtimeVoiceProvider: $errorMessage');
      return false;
    }
  }

  /// Stop listening for voice input
  Future<void> stopListening() async {
    if (!state.isListening) {
      debugPrint('RealtimeVoiceProvider: Not currently listening');
      return;
    }

    try {
      debugPrint('RealtimeVoiceProvider: Stopping voice listening...');
      await _voiceService.stopListening();
      debugPrint('RealtimeVoiceProvider: Voice listening stopped');
    } catch (e) {
      final errorMessage = 'Failed to stop listening: $e';
      state = state.copyWith(error: errorMessage);
      debugPrint('RealtimeVoiceProvider: $errorMessage');
    }
  }

  /// Toggle listening state
  Future<bool> toggleListening() async {
    if (state.isListening) {
      await stopListening();
      return false;
    } else {
      return await startListening();
    }
  }

  /// Clear the current error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Clear VAD events
  void clearVadEvents() {
    state = state.copyWith(vadEvents: []);
  }

  /// Get current transcription text
  String? get currentTranscription => state.lastTranscription;

  /// Check if currently processing audio
  bool get isProcessing => state.voiceState == VoiceState.processing;

  /// Check if there's an active error
  bool get hasError => state.error != null;

  /// Get processing performance info
  String get performanceInfo {
    if (state.lastProcessingTime == null) return 'No processing data';
    
    final ms = state.lastProcessingTime!.inMilliseconds;
    final confidence = state.lastConfidence ?? 0.0;
    return 'Processing: ${ms}ms, Confidence: ${(confidence * 100).toStringAsFixed(1)}%';
  }

  @override
  void dispose() {
    debugPrint('RealtimeVoiceProvider: Disposing...');
    _voiceService.dispose();
    super.dispose();
  }
}

// Provider instances
final realtimeVoiceServiceProvider = Provider<RealtimeVoiceService>((ref) {
  return RealtimeVoiceService();
});

final realtimeVoiceProvider = StateNotifierProvider<RealtimeVoiceNotifier, RealtimeVoiceState>((ref) {
  final voiceService = ref.watch(realtimeVoiceServiceProvider);
  return RealtimeVoiceNotifier(voiceService);
});

// Convenience providers for specific state aspects
final voiceStateProvider = Provider<VoiceState>((ref) {
  return ref.watch(realtimeVoiceProvider).voiceState;
});

final isVoiceListeningProvider = Provider<bool>((ref) {
  return ref.watch(realtimeVoiceProvider).isListening;
});

final lastTranscriptionProvider = Provider<String?>((ref) {
  return ref.watch(realtimeVoiceProvider).lastTranscription;
});

final voiceErrorProvider = Provider<String?>((ref) {
  return ref.watch(realtimeVoiceProvider).error;
});

final vadEventsProvider = Provider<List<String>>((ref) {
  return ref.watch(realtimeVoiceProvider).vadEvents;
});
