import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../core/theme/app_theme.dart';
import '../common/glassmorphic_container.dart';

class StatsCard extends StatelessWidget {
  final String title;
  final Map<String, dynamic> stats;

  const StatsCard({
    super.key,
    required this.title,
    required this.stats,
  });

  @override
  Widget build(BuildContext context) {
    return GlassmorphicContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          ...stats.entries.map((entry) => Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: _buildStatRow(context, entry.key, entry.value),
          )),
        ],
      ),
    );
  }

  Widget _buildStatRow(BuildContext context, String label, dynamic value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppTheme.primaryColor.withValues(alpha: 0.1),
                AppTheme.primaryColor.withValues(alpha: 0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppTheme.primaryColor.withValues(alpha: 0.3),
            ),
          ),
          child: Text(
            _formatValue(value),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.primaryColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  String _formatValue(dynamic value) {
    if (value is int) {
      if (value >= 1000000) {
        return '${(value / 1000000).toStringAsFixed(1)}M';
      } else if (value >= 1000) {
        return '${(value / 1000).toStringAsFixed(1)}K';
      }
      return value.toString();
    }
    return value.toString();
  }
}

class DailyStatsCard extends StatelessWidget {
  final Map<String, int> dailyStats;

  const DailyStatsCard({
    super.key,
    required this.dailyStats,
  });

  @override
  Widget build(BuildContext context) {
    return GlassmorphicContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.today_rounded,
                color: AppTheme.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Today\'s Activity',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildDailyStatItem(
                  context,
                  'Messages',
                  dailyStats['todayMessages'] ?? 0,
                  Icons.chat_rounded,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildDailyStatItem(
                  context,
                  'Voice',
                  dailyStats['todayVoiceMessages'] ?? 0,
                  Icons.mic_rounded,
                  Colors.green,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          _buildDailyStatItem(
            context,
            'Time Spent',
            dailyStats['todayTimeSpent'] ?? 0,
            Icons.access_time_rounded,
            Colors.orange,
            suffix: ' min',
          ),
        ],
      ),
    );
  }

  Widget _buildDailyStatItem(
    BuildContext context,
    String label,
    int value,
    IconData icon,
    Color color, {
    String suffix = '',
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            '$value$suffix',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    ).animate()
        .scale(delay: Duration(milliseconds: label.hashCode % 500))
        .fadeIn();
  }
}

class WeeklyProgressChart extends StatelessWidget {
  final List<int> weeklyData;
  final String title;

  const WeeklyProgressChart({
    super.key,
    required this.weeklyData,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    final maxValue = weeklyData.isNotEmpty 
        ? weeklyData.reduce((a, b) => a > b ? a : b) 
        : 1;
    
    return GlassmorphicContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: List.generate(7, (index) {
              final value = index < weeklyData.length ? weeklyData[index] : 0;
              final height = maxValue > 0 ? (value / maxValue) * 60 : 0.0;
              final dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
              
              return Column(
                children: [
                  Container(
                    width: 20,
                    height: height + 10,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppTheme.primaryColor,
                          AppTheme.primaryColor.withOpacity(0.7),
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ).animate(delay: Duration(milliseconds: index * 100))
                      .slideY(begin: 1, duration: 600.ms, curve: Curves.easeOutBack),
                  
                  const SizedBox(height: 8),
                  
                  Text(
                    dayNames[index],
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              );
            }),
          ),
        ],
      ),
    );
  }
}
