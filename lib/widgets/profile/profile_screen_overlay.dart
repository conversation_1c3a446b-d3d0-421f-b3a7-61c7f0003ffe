import 'package:ellahai/widgets/navigation/main_navigation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:ui';

import '../../core/theme/app_theme.dart';
import '../../providers/auth_provider.dart';
import '../../providers/progress_provider.dart';
import '../../providers/shop_provider.dart';
import '../../providers/collection_detail_provider.dart';
import '../../models/user/user_model.dart';
import '../../models/shop/shop_item_model.dart';
import '../../services/storage/storage_service.dart';
import '../common/glassmorphic_container.dart';
import '../../services/relationship/relationship_gate_controller.dart';
import '../../providers/collection_preview_provider.dart';
import '../../screens/settings/edit_profile_settings_page.dart';
import '../../screens/settings/voice_settings_page.dart';
import '../../screens/settings/notifications_settings_page.dart';
import '../../screens/settings/privacy_settings_page.dart';
import '../../screens/settings/help_support_page.dart';

// Extension to help with color opacity
extension ColorExtension on Color {
  Color withValues({double? alpha}) {
    return Color.fromRGBO(red, green, blue, alpha ?? opacity);
  }
}

// Relationship level enum for profile screen
enum RelationshipLevel {
  acquaintance(1, 'Acquaintance'),
  friend(2, 'Friend'),
  closeFriend(3, 'Close Friend'),
  intimate(4, 'Intimate');

  final int level;
  final String displayName;

  const RelationshipLevel(this.level, this.displayName);

  static RelationshipLevel fromLevel(int level) {
    return RelationshipLevel.values.firstWhere(
      (e) => e.level == level,
      orElse: () => RelationshipLevel.acquaintance,
    );
  }
}


class ProfileScreenOverlay extends ConsumerStatefulWidget {
  const ProfileScreenOverlay({super.key});

  @override
  ConsumerState<ProfileScreenOverlay> createState() =>
      _ProfileScreenOverlayState();
}

class _ProfileScreenOverlayState extends ConsumerState<ProfileScreenOverlay>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _headerAnimationController;
  late Animation<double> _headerAnimation;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);

    _headerAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _headerAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _headerAnimationController,
        curve: Curves.easeOutBack,
      ),
    );

    _headerAnimationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _headerAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(currentUserProvider);
    final progressState = ref.watch(progressProvider);
    final collectionPreviewState = ref.watch(collectionPreviewProvider);
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;

    if (user == null) {
      return Center(
        child: Container(
          padding: EdgeInsets.all(isTablet ? 32 : 24),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
                strokeWidth: 3,
              ),
              SizedBox(height: isTablet ? 20 : 16),
              Text(
                'Loading Profile...',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return AnimatedOpacity(
      opacity: collectionPreviewState.isInPreviewMode ? 0.0 : 1.0,
      duration: const Duration(milliseconds: 300),
      child: IgnorePointer(
        ignoring: collectionPreviewState.isInPreviewMode,
        child: Container(
      margin: EdgeInsets.all(isTablet ? 24 : 16),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withValues(alpha: 0.15),
                  Colors.white.withValues(alpha: 0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(24),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  offset: const Offset(0, 8),
                  blurRadius: 32,
                  spreadRadius: 0,
                ),
                BoxShadow(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  offset: const Offset(0, 0),
                  blurRadius: 20,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Column(
              children: [
                // Enhanced Header with Animation
                AnimatedBuilder(
                  animation: _headerAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _headerAnimation.value,
                      child: _buildEnhancedHeader(user, isTablet),
                    );
                  },
                ),

                // Enhanced Tab Bar
                _buildEnhancedTabBar(isTablet),

                // Content
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildOverviewTab(user, progressState, isTablet),
                      _buildCollectionTab(user, isTablet),
                      _buildMemoriesTab(user, isTablet),
                      _buildCompanionsTab(user, isTablet),
                      _buildAchievementsTab(progressState.achievements, isTablet),
                      _buildSettingsTab(user, isTablet),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
        ),
      ),
    );
  }

  Widget _buildEnhancedHeader(UserModel user, bool isTablet) {
    return Container(
      padding: EdgeInsets.all(isTablet ? 28 : 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor.withValues(alpha: 0.15),
            AppTheme.secondaryColor.withValues(alpha: 0.08),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        children: [
          // Top row with close button and subscription badge
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.2),
                    width: 1,
                  ),
                ),
                child: IconButton(
                  onPressed: () => ref.read(navigationIndexProvider.notifier).state = 0,
                  icon: Icon(
                    Icons.close_rounded,
                    color: Colors.white,
                    size: isTablet ? 26 : 24,
                  ),
                  padding: EdgeInsets.all(isTablet ? 10 : 8),
                  constraints: BoxConstraints(
                    minWidth: isTablet ? 48 : 40,
                    minHeight: isTablet ? 48 : 40,
                  ),
                ),
              ),
              _buildEnhancedSubscriptionBadge(isTablet),
            ],
          ),

          SizedBox(height: isTablet ? 24 : 16),

          // Enhanced profile picture and info
          Row(
            children: [
              Container(
                width: isTablet ? 100.0 : 80.0,
                height: isTablet ? 100.0 : 80.0,
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.4),
                    width: isTablet ? 4 : 3,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.primaryColor.withValues(alpha: 0.4),
                      blurRadius: isTablet ? 28 : 20,
                      spreadRadius: isTablet ? 4 : 2,
                    ),
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      offset: const Offset(0, 8),
                      blurRadius: 16,
                    ),
                  ],
                ),
                child: user.photoURL != null
                    ? ClipOval(
                        child: Image.network(
                          user.photoURL!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Icon(
                            Icons.person,
                            color: Colors.white,
                            size: isTablet ? 50 : 40,
                          ),
                        ),
                      )
                    : Icon(
                        Icons.person,
                        color: Colors.white,
                        size: isTablet ? 50 : 40,
                      ),
              ),
              SizedBox(width: isTablet ? 28 : 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      user.name,
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontSize: isTablet ? 26 : 22,
                      ),
                    ),
                    SizedBox(height: isTablet ? 8 : 4),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: isTablet ? 12 : 10,
                        vertical: isTablet ? 6 : 4,
                      ),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppTheme.warningColor.withValues(alpha: 0.3),
                            AppTheme.warningColor.withValues(alpha: 0.1),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: AppTheme.warningColor.withValues(alpha: 0.5),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.star_rounded,
                            color: AppTheme.warningColor,
                            size: isTablet ? 22 : 20,
                          ),
                          SizedBox(width: isTablet ? 6 : 4),
                          Text(
                            'Level ${user.progress.level}',
                            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: AppTheme.warningColor,
                              fontWeight: FontWeight.w600,
                              fontSize: isTablet ? 16 : 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: isTablet ? 8 : 4),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: isTablet ? 12 : 10,
                        vertical: isTablet ? 6 : 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        user.selectedPersonality.displayName,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textSecondaryColor,
                          fontSize: isTablet ? 15 : 13,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedSubscriptionBadge(bool isTablet) {
    // Mock subscription data - replace with actual subscription provider
    const subscriptionTier = 'Premium'; // Free, Premium, Elite
    const isActive = true;

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isTablet ? 16 : 12,
        vertical: isTablet ? 10 : 6,
      ),
      decoration: BoxDecoration(
        gradient: isActive
            ? AppTheme.primaryGradient
            : LinearGradient(
                colors: [
                  Colors.grey.withValues(alpha: 0.6),
                  Colors.grey.withValues(alpha: 0.7),
                ],
              ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: (isActive ? AppTheme.primaryColor : Colors.grey).withValues(
              alpha: 0.4,
            ),
            blurRadius: isTablet ? 12 : 8,
            spreadRadius: isTablet ? 2 : 1,
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            offset: const Offset(0, 4),
            blurRadius: 8,
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isActive ? Icons.diamond_rounded : Icons.lock_outline_rounded,
            color: Colors.white,
            size: isTablet ? 20 : 16,
          ),
          SizedBox(width: isTablet ? 8 : 4),
          Text(
            subscriptionTier,
            style: TextStyle(
              color: Colors.white,
              fontSize: isTablet ? 14 : 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedTabBar(bool isTablet) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: isTablet ? 24 : 16),
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryColor.withValues(alpha: 0.3),
              offset: const Offset(0, 2),
              blurRadius: 8,
            ),
          ],
        ),
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.textSecondaryColor,
        labelStyle: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: isTablet ? 14 : 12,
        ),
        unselectedLabelStyle: TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: isTablet ? 14 : 12,
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        isScrollable: true,
        tabAlignment: TabAlignment.start,
        tabs: [
          Tab(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 12 : 8,
                vertical: isTablet ? 6 : 4,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.dashboard_rounded, size: isTablet ? 20 : 18),
                  SizedBox(width: isTablet ? 8 : 6),
                  const Text('Overview'),
                ],
              ),
            ),
          ),
          Tab(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 12 : 8,
                vertical: isTablet ? 6 : 4,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.collections_rounded, size: isTablet ? 20 : 18),
                  SizedBox(width: isTablet ? 8 : 6),
                  const Text('Collection'),
                ],
              ),
            ),
          ),
          Tab(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 12 : 8,
                vertical: isTablet ? 6 : 4,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.psychology_rounded, size: isTablet ? 20 : 18),
                  SizedBox(width: isTablet ? 8 : 6),
                  const Text('Memories'),
                ],
              ),
            ),
          ),
          Tab(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 12 : 8,
                vertical: isTablet ? 6 : 4,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.people_rounded, size: isTablet ? 20 : 18),
                  SizedBox(width: isTablet ? 8 : 6),
                  const Text('Companions'),
                ],
              ),
            ),
          ),
          Tab(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 12 : 8,
                vertical: isTablet ? 6 : 4,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.emoji_events_rounded, size: isTablet ? 20 : 18),
                  SizedBox(width: isTablet ? 8 : 6),
                  const Text('Achievements'),
                ],
              ),
            ),
          ),
          Tab(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 12 : 8,
                vertical: isTablet ? 6 : 4,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.settings_rounded, size: isTablet ? 20 : 18),
                  SizedBox(width: isTablet ? 8 : 6),
                  const Text('Settings'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab(UserModel user, ProgressState progressState, bool isTablet) {
    final padding = isTablet ? 24.0 : 16.0;
    final spacing = isTablet ? 24.0 : 16.0;

    return SingleChildScrollView(
      padding: EdgeInsets.all(padding),
      child: Column(
        children: [
          // Relationship Level Card
          _buildEnhancedRelationshipCard(user, isTablet),
          SizedBox(height: spacing),

          // Usage Stats Card
          _buildEnhancedUsageStatsCard(user, isTablet),
          SizedBox(height: spacing),

          // Progress Overview Card
          _buildProgressOverviewCard(user, progressState),
          // Quick Stats Grid
          _buildQuickStatsGrid(user, progressState),
        ],
      ),
    );
  }

  Widget _buildEnhancedRelationshipCard(UserModel user, bool isTablet) {
    // Mock relationship data - replace with actual relationship provider
    const currentLevel = 2; // Friend level
    const progress = 0.65; // 65% to next level
    const relationshipPoints = 350;
    const nextLevelPoints = 500;

    final relationshipLevel = RelationshipLevel.fromLevel(currentLevel);
    final nextLevel = RelationshipLevel.fromLevel(currentLevel + 1);

    // Define relationship level colors
    Color _getRelationshipColor(int level) {
      switch (level) {
        case 1: return const Color(0xFF9E9E9E); // Gray for Acquaintance
        case 2: return const Color(0xFF4ECDC4); // Teal for Friend
        case 3: return const Color(0xFF6C63FF); // Purple for Close Friend
        case 4: return const Color(0xFFFF6B6B); // Pink for Intimate
        default: return AppTheme.primaryColor;
      }
    }

    final relationshipColor = _getRelationshipColor(currentLevel);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            relationshipColor.withValues(alpha: 0.15),
            relationshipColor.withValues(alpha: 0.05),
            Colors.white.withValues(alpha: 0.03),
          ],
          stops: const [0.0, 0.6, 1.0],
        ),
        border: Border.all(
          color: relationshipColor.withValues(alpha: 0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            offset: const Offset(0, 12),
            blurRadius: 32,
            spreadRadius: 0,
          ),
          BoxShadow(
            color: relationshipColor.withValues(alpha: 0.2),
            offset: const Offset(0, 0),
            blurRadius: 24,
            spreadRadius: 0,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withValues(alpha: 0.1),
                  Colors.white.withValues(alpha: 0.05),
                ],
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(isTablet ? 32 : 24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header Section
                  Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(isTablet ? 16 : 12),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              relationshipColor,
                              relationshipColor.withValues(alpha: 0.8),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: relationshipColor.withValues(alpha: 0.4),
                              offset: const Offset(0, 6),
                              blurRadius: 16,
                              spreadRadius: 0,
                            ),
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              offset: const Offset(0, 2),
                              blurRadius: 8,
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.favorite_rounded,
                          color: Colors.white,
                          size: isTablet ? 32 : 28,
                        ),
                      ),
                      SizedBox(width: isTablet ? 20 : 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Relationship',
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                fontSize: isTablet ? 20 : 18,
                                color: Colors.white,
                                letterSpacing: 0.5,
                              ),
                            ),
                            Text(
                              'Level',
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                fontSize: isTablet ? 20 : 18,
                                color: Colors.white,
                                letterSpacing: 0.5,
                              ),
                            ),
                            SizedBox(height: isTablet ? 8 : 6),
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: isTablet ? 16 : 12,
                                vertical: isTablet ? 8 : 6,
                              ),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    relationshipColor.withValues(alpha: 0.4),
                                    relationshipColor.withValues(alpha: 0.2),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: relationshipColor.withValues(alpha: 0.6),
                                  width: 1.5,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: relationshipColor.withValues(alpha: 0.2),
                                    offset: const Offset(0, 2),
                                    blurRadius: 8,
                                  ),
                                ],
                              ),
                              child: Text(
                                relationshipLevel.displayName,
                                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w700,
                                  fontSize: isTablet ? 16 : 14,
                                  letterSpacing: 0.3,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Colors.white.withValues(alpha: 0.15),
                              Colors.white.withValues(alpha: 0.08),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: TextButton(
                          onPressed: () => _showAllRelationshipLevels(),
                          style: TextButton.styleFrom(
                            padding: EdgeInsets.symmetric(
                              horizontal: isTablet ? 16 : 12,
                              vertical: isTablet ? 12 : 8,
                            ),
                          ),
                          child: Text(
                            'View All',
                            style: TextStyle(
                              color: relationshipColor,
                              fontWeight: FontWeight.w600,
                              fontSize: isTablet ? 14 : 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: isTablet ? 32 : 24),

                  // Enhanced Progress Section
                  Container(
                    padding: EdgeInsets.all(isTablet ? 24 : 20),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.white.withValues(alpha: 0.08),
                          Colors.white.withValues(alpha: 0.03),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.15),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          offset: const Offset(0, 4),
                          blurRadius: 12,
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                'Progress to ${nextLevel.displayName}',
                                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                  fontSize: isTablet ? 16 : 14,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: isTablet ? 12 : 10,
                                vertical: isTablet ? 6 : 4,
                              ),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    relationshipColor.withValues(alpha: 0.3),
                                    relationshipColor.withValues(alpha: 0.2),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: relationshipColor.withValues(alpha: 0.4),
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                '$relationshipPoints / $nextLevelPoints',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w700,
                                  fontSize: isTablet ? 14 : 12,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: isTablet ? 16 : 12),
                        Container(
                          height: isTablet ? 14 : 10,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            gradient: LinearGradient(
                              colors: [
                                Colors.white.withValues(alpha: 0.1),
                                Colors.white.withValues(alpha: 0.05),
                              ],
                            ),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.1),
                              width: 1,
                            ),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: Stack(
                              children: [
                                LinearProgressIndicator(
                                  value: progress,
                                  backgroundColor: Colors.transparent,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    relationshipColor,
                                  ),
                                  minHeight: isTablet ? 14 : 10,
                                ),
                                // Shimmer effect overlay
                                Positioned.fill(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      gradient: LinearGradient(
                                        colors: [
                                          Colors.transparent,
                                          relationshipColor.withValues(alpha: 0.1),
                                          Colors.transparent,
                                        ],
                                        stops: const [0.0, 0.5, 1.0],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: isTablet ? 24 : 20),

                  // Enhanced Unlocked Features
                  Text(
                    'Unlocked Features:',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                      fontSize: isTablet ? 18 : 16,
                      color: Colors.white,
                      letterSpacing: 0.3,
                    ),
                  ),
                  SizedBox(height: isTablet ? 16 : 12),
                  Wrap(
                    spacing: isTablet ? 12 : 10,
                    runSpacing: isTablet ? 12 : 10,
                    children: [
                      'Personal Topics',
                      'Rare Cosmetics',
                      'Mild Flirtation',
                      'Special Outfits',
                    ].map((feature) => Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: isTablet ? 16 : 12,
                        vertical: isTablet ? 10 : 8,
                      ),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppTheme.successColor.withValues(alpha: 0.4),
                            AppTheme.successColor.withValues(alpha: 0.2),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: AppTheme.successColor.withValues(alpha: 0.6),
                          width: 1.5,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppTheme.successColor.withValues(alpha: 0.3),
                            offset: const Offset(0, 3),
                            blurRadius: 8,
                            spreadRadius: 0,
                          ),
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            offset: const Offset(0, 1),
                            blurRadius: 4,
                          ),
                        ],
                      ),
                      child: Text(
                        feature,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: isTablet ? 13 : 11,
                          letterSpacing: 0.2,
                        ),
                      ),
                    )).toList(),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedUsageStatsCard(UserModel user, bool isTablet) {
    // Mock usage data - replace with actual usage provider
    const voiceMinutesUsed = 45;
    const voiceMinutesLimit = 100;
    const textsUsed = 150;
    const textsLimit = 500;
    const daysUntilReset = 12;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withValues(alpha: 0.1),
            Colors.white.withValues(alpha: 0.05),
          ],
        ),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            offset: const Offset(0, 8),
            blurRadius: 24,
          ),
          BoxShadow(
            color: AppTheme.accentColor.withValues(alpha: 0.1),
            offset: const Offset(0, 0),
            blurRadius: 16,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Padding(
            padding: EdgeInsets.all(isTablet ? 28 : 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(isTablet ? 12 : 8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [AppTheme.accentColor, AppTheme.warningColor],
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: AppTheme.accentColor.withValues(alpha: 0.3),
                            offset: const Offset(0, 4),
                            blurRadius: 12,
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.analytics_rounded,
                        color: Colors.white,
                        size: isTablet ? 28 : 24,
                      ),
                    ),
                    SizedBox(width: isTablet ? 16 : 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Monthly Usage',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              fontSize: isTablet ? 18 : 16,
                              color: Colors.white,
                            ),
                          ),
                          SizedBox(height: isTablet ? 6 : 4),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: isTablet ? 10 : 8,
                              vertical: isTablet ? 4 : 2,
                            ),
                            decoration: BoxDecoration(
                              color: AppTheme.warningColor.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              'Resets in $daysUntilReset days',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: AppTheme.warningColor,
                                fontSize: isTablet ? 14 : 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                SizedBox(height: isTablet ? 28 : 20),

                // Enhanced Voice minutes usage
                _buildEnhancedUsageBar(
                  'Voice Minutes',
                  voiceMinutesUsed,
                  voiceMinutesLimit,
                  Icons.mic_rounded,
                  AppTheme.primaryColor,
                  isTablet,
                ),

                SizedBox(height: isTablet ? 20 : 16),

                // Enhanced Text messages usage
                _buildEnhancedUsageBar(
                  'Text Messages',
                  textsUsed,
                  textsLimit,
                  Icons.message_rounded,
                  AppTheme.successColor,
                  isTablet,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedUsageBar(
    String title,
    int used,
    int limit,
    IconData icon,
    Color color,
    bool isTablet,
  ) {
    final percentage = used / limit;
    final isNearLimit = percentage > 0.8;
    final displayColor = isNearLimit ? AppTheme.warningColor : color;

    return Container(
      padding: EdgeInsets.all(isTablet ? 20 : 16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(isTablet ? 10 : 8),
                decoration: BoxDecoration(
                  color: displayColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: displayColor.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Icon(
                  icon,
                  color: displayColor,
                  size: isTablet ? 22 : 20,
                ),
              ),
              SizedBox(width: isTablet ? 12 : 8),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: isTablet ? 16 : 14,
                    color: Colors.white,
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: isTablet ? 12 : 10,
                  vertical: isTablet ? 6 : 4,
                ),
                decoration: BoxDecoration(
                  color: displayColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: displayColor.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  '$used / $limit',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: displayColor,
                    fontWeight: FontWeight.w600,
                    fontSize: isTablet ? 14 : 12,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: isTablet ? 16 : 12),
          Container(
            height: isTablet ? 10 : 8,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: Colors.white.withValues(alpha: 0.1),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: LinearProgressIndicator(
                value: percentage,
                backgroundColor: Colors.transparent,
                valueColor: AlwaysStoppedAnimation<Color>(displayColor),
                minHeight: isTablet ? 10 : 8,
              ),
            ),
          ),
          if (isNearLimit) ...[
            SizedBox(height: isTablet ? 12 : 8),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 12 : 10,
                vertical: isTablet ? 6 : 4,
              ),
              decoration: BoxDecoration(
                color: AppTheme.warningColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppTheme.warningColor.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.warning_rounded,
                    color: AppTheme.warningColor,
                    size: isTablet ? 16 : 14,
                  ),
                  SizedBox(width: isTablet ? 6 : 4),
                  Text(
                    'Near limit',
                    style: TextStyle(
                      color: AppTheme.warningColor,
                      fontSize: isTablet ? 12 : 10,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildProgressOverviewCard(
    UserModel user,
    ProgressState progressState,
  ) {
    return GlassmorphicContainer(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [AppTheme.successColor, AppTheme.primaryColor],
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.trending_up_rounded,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Progress Overview',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        'Your journey with Ella',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 20),

            // XP Progress
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Level ${user.progress.level} Progress',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      '${user.progress.xpToNextLevel} XP to next level',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: LinearProgressIndicator(
                    value: _getProgressValue(user.progress.progressToNextLevel),
                    backgroundColor: Colors.white.withValues(alpha: 0.1),
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppTheme.successColor,
                    ),
                    minHeight: 8,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Hearts display
            Row(
              children: [
                Icon(
                  Icons.favorite_rounded,
                  color: AppTheme.accentColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '${user.progress.hearts} Hearts',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppTheme.accentColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                Text(
                  '${progressState.totalHeartsEarned} total earned',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  // Helper method to ensure progress value is valid
  double _getProgressValue(double value) {
    if (value.isNaN || value.isInfinite || value < 0) {
      return 0.0;
    } else if (value > 1.0) {
      return 1.0;
    }
    return value;
  }

  Widget _buildQuickStatsGrid(UserModel user, ProgressState progressState) {
    final isTablet = MediaQuery.of(context).size.width > 600;

    final stats = [
      _StatItem(
        'Days Active',
        '${progressState.daysActive}',
        Icons.calendar_today_rounded,
        AppTheme.primaryColor,
      ),
      _StatItem(
        'Messages',
        '${user.progress.messagesCount}',
        Icons.chat_bubble_rounded,
        AppTheme.successColor,
      ),
      _StatItem(
        'Voice Calls',
        '${user.progress.voiceMessagesCount}',
        Icons.mic_rounded,
        AppTheme.accentColor,
      ),
      _StatItem(
        'Time Spent',
        '${(user.progress.totalTimeSpent / 60).toStringAsFixed(1)}h',
        Icons.schedule_rounded,
        AppTheme.warningColor,
      ),
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: isTablet ? 16 : 12,
        mainAxisSpacing: isTablet ? 16 : 12,
        childAspectRatio: 1,
      ),
      itemCount: stats.length,
      itemBuilder: (context, index) {
        final stat = stats[index];
        return _buildEnhancedStatCard(stat, isTablet);
      },
    );
  }

  Widget _buildEnhancedStatCard(_StatItem stat, bool isTablet) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withValues(alpha: 0.1),
            Colors.white.withValues(alpha: 0.05),
            stat.color.withValues(alpha: 0.02),
          ],
          stops: const [0.0, 0.7, 1.0],
        ),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            offset: const Offset(0, 8),
            blurRadius: 24,
            spreadRadius: 0,
          ),
          BoxShadow(
            color: stat.color.withValues(alpha: 0.1),
            offset: const Offset(0, 0),
            blurRadius: 16,
            spreadRadius: 0,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white.withValues(alpha: 0.08),
                  Colors.white.withValues(alpha: 0.03),
                ],
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(isTablet ? 20 : 16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Icon Container
                  Container(
                    padding: EdgeInsets.all(isTablet ? 12 : 10),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          stat.color.withValues(alpha: 0.3),
                          stat.color.withValues(alpha: 0.2),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: stat.color.withValues(alpha: 0.4),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: stat.color.withValues(alpha: 0.3),
                          offset: const Offset(0, 4),
                          blurRadius: 12,
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: Icon(
                      stat.icon,
                      color: stat.color,
                      size: isTablet ? 28 : 24,
                    ),
                  ),

                  SizedBox(height: isTablet ? 12 : 10),

                  // Value Text
                  Text(
                    stat.value,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                      fontSize: isTablet ? 24 : 20,
                      letterSpacing: 0.5,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),

                  SizedBox(height: isTablet ? 6 : 4),

                  // Title Text
                  Text(
                    stat.title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.textSecondaryColor,
                      fontSize: isTablet ? 14 : 12,
                      fontWeight: FontWeight.w500,
                      letterSpacing: 0.2,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCollectionTab(UserModel user, bool isTablet) {
    return _EnhancedCollectionTab(user: user, isTablet: isTablet);
  }

  Widget _buildCollectionSection(
    String title,
    List<String> items,
    IconData icon,
    Color color,
    String Function(String) getDisplayName,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const Spacer(),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${items.length}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        if (items.isEmpty)
          GlassmorphicContainer(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.shopping_bag_outlined,
                      color: AppTheme.textSecondaryColor,
                      size: 48,
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'No ${title.toLowerCase()} owned yet',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextButton(
                      onPressed: () {
                        ref.read(navigationIndexProvider.notifier).state = 1;
                      },
                      child: const Text('Visit Shop'),
                    ),
                  ],
                ),
              ),
            ),
          )
        else
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.1, // Reduced aspect ratio for more height
            ),
            itemCount: items.length,
            itemBuilder: (context, index) {
              final item = items[index];
              final isSelected = (title == 'Environments');

              return GlassmorphicContainer(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                    border:
                        isSelected ? Border.all(color: color, width: 2) : null,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                color.withValues(alpha: 0.3),
                                color.withValues(alpha: 0.1),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(icon, color: color, size: 26),
                        ),
                        const SizedBox(height: 8),
                        Flexible(
                          child: Text(
                            getDisplayName(item),
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(
                              fontWeight: FontWeight.w600,
                              fontSize: 13,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (isSelected) ...[
                          const SizedBox(height: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: color.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              'Active',
                              style: Theme.of(
                                context,
                              ).textTheme.bodySmall?.copyWith(
                                color: color,
                                fontWeight: FontWeight.w600,
                                fontSize: 10,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
      ],
    );
  }

  Widget _buildCollectionStats(UserModel user, {int petCount = 0}) {
    final totalItems = user.ownedEnvironments.length + user.ownedOutfits.length + petCount;
    final joinDate = user.createdAt;
    final daysSinceJoin = DateTime.now().difference(joinDate).inDays;

    return GlassmorphicContainer(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Collection Stats',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total Items',
                    '$totalItems',
                    AppTheme.primaryColor,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Days Collecting',
                    '$daysSinceJoin',
                    AppTheme.successColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Environments',
                    '${user.ownedEnvironments.length}',
                    AppTheme.accentColor,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Outfits',
                    '${user.ownedOutfits.length}',
                    AppTheme.warningColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Pets',
                    '$petCount',
                    Colors.green,
                  ),
                ),
                const Expanded(child: SizedBox()), // Empty space for balance
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String title, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.headlineMedium?.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          title,
          style: Theme.of(
            context,
          ).textTheme.bodySmall?.copyWith(color: AppTheme.textSecondaryColor),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildMemoriesTab(UserModel user, bool isTablet) {
    // Mock memory data - replace with actual memory provider
    final memories = [
      _Memory(
        id: '1',
        title: 'Favorite Color',
        content: 'You mentioned that your favorite color is blue',
        category: 'Preferences',
        timestamp: DateTime.now().subtract(const Duration(days: 5)),
        importance: MemoryImportance.medium,
      ),
      _Memory(
        id: '2',
        title: 'Birthday',
        content: 'Your birthday is on March 15th',
        category: 'Personal',
        timestamp: DateTime.now().subtract(const Duration(days: 12)),
        importance: MemoryImportance.high,
      ),
      _Memory(
        id: '3',
        title: 'Pet Name',
        content: 'You have a cat named Whiskers',
        category: 'Personal',
        timestamp: DateTime.now().subtract(const Duration(days: 8)),
        importance: MemoryImportance.medium,
      ),
      _Memory(
        id: '4',
        title: 'Work Schedule',
        content: 'You work from home on Mondays and Fridays',
        category: 'Lifestyle',
        timestamp: DateTime.now().subtract(const Duration(days: 3)),
        importance: MemoryImportance.low,
      ),
      _Memory(
        id: '5',
        title: 'Hobby',
        content: 'You enjoy playing guitar in your free time',
        category: 'Interests',
        timestamp: DateTime.now().subtract(const Duration(days: 15)),
        importance: MemoryImportance.medium,
      ),
    ];

    final categories = memories.map((m) => m.category).toSet().toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with memory stats
          GlassmorphicContainer(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [AppTheme.primaryColor, AppTheme.accentColor],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.psychology_rounded,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Memories',
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          '${memories.length} memories stored',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: AppTheme.textSecondaryColor),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => _showMemoryManagementDialog(),
                    icon: const Icon(Icons.settings_rounded),
                    tooltip: 'Memory Settings',
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Memory categories
          Text(
            'Categories',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children:
                categories.map((category) {
                  final categoryMemories =
                      memories.where((m) => m.category == category).length;
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: AppTheme.primaryColor.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getCategoryIcon(category),
                          color: AppTheme.primaryColor,
                          size: 16,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          category,
                          style: Theme.of(
                            context,
                          ).textTheme.bodySmall?.copyWith(
                            color: AppTheme.primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            '$categoryMemories',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
          ),

          const SizedBox(height: 24),

          // Recent memories
          Text(
            'Recent Memories',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),

          ...memories.map((memory) => _buildMemoryCard(memory)),
        ],
      ),
    );
  }

  Widget _buildMemoryCard(_Memory memory) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: GlassmorphicContainer(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: _getImportanceColor(
                        memory.importance,
                      ).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getCategoryIcon(memory.category),
                      color: _getImportanceColor(memory.importance),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          memory.title,
                          style: Theme.of(context).textTheme.titleSmall
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          memory.category,
                          style: Theme.of(context).textTheme.bodySmall
                              ?.copyWith(color: AppTheme.textSecondaryColor),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getImportanceColor(
                        memory.importance,
                      ).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      memory.importance.name.toUpperCase(),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _getImportanceColor(memory.importance),
                        fontWeight: FontWeight.w600,
                        fontSize: 10,
                      ),
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (value) => _handleMemoryAction(memory, value),
                    itemBuilder:
                        (context) => [
                          const PopupMenuItem(
                            value: 'edit',
                            child: Row(
                              children: [
                                Icon(Icons.edit_rounded, size: 16),
                                SizedBox(width: 8),
                                Text('Edit'),
                              ],
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(
                                  Icons.delete_rounded,
                                  size: 16,
                                  color: AppTheme.errorColor,
                                ),
                                SizedBox(width: 8),
                                Text(
                                  'Delete',
                                  style: TextStyle(color: AppTheme.errorColor),
                                ),
                              ],
                            ),
                          ),
                        ],
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                memory.content,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 8),
              Text(
                _formatMemoryTimestamp(memory.timestamp),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCompanionsTab(UserModel user, bool isTablet) {
    // Mock companion data - replace with actual companion provider
    final companions = [
      _Companion(
        id: 'ella',
        name: 'Ella',
        personality: CompanionPersonality.caringFriend,
        isActive: true,
        isUnlocked: true,
        relationshipLevel: 2,
        unlockedAt: DateTime.now().subtract(const Duration(days: 30)),
        description:
            'Your caring AI companion who\'s always there to listen and support you.',
        avatar: '🤖',
      ),
      _Companion(
        id: 'alex',
        name: 'Alex',
        personality: CompanionPersonality.playfulCompanion,
        isActive: false,
        isUnlocked: true,
        relationshipLevel: 1,
        unlockedAt: DateTime.now().subtract(const Duration(days: 15)),
        description:
            'A fun-loving companion who brings energy and excitement to every conversation.',
        avatar: '🎭',
      ),
      _Companion(
        id: 'sage',
        name: 'Sage',
        personality: CompanionPersonality.wiseMentor,
        isActive: false,
        isUnlocked: false,
        relationshipLevel: 0,
        unlockedAt: null,
        description:
            'A wise mentor who offers thoughtful guidance and deep insights.',
        avatar: '🧙‍♂️',
        unlockRequirement: 'Reach relationship level 3',
      ),
      _Companion(
        id: 'valentine',
        name: 'Valentine',
        personality: CompanionPersonality.romanticPartner,
        isActive: false,
        isUnlocked: false,
        relationshipLevel: 0,
        unlockedAt: null,
        description:
            'A romantic companion for intimate conversations and emotional connection.',
        avatar: '💕',
        unlockRequirement: 'Reach relationship level 4',
      ),
      _Companion(
        id: 'dr_hope',
        name: 'Dr. Hope',
        personality: CompanionPersonality.supportiveTherapist,
        isActive: false,
        isUnlocked: false,
        relationshipLevel: 0,
        unlockedAt: null,
        description:
            'A supportive therapist focused on your mental health and wellbeing.',
        avatar: '🩺',
        unlockRequirement: 'Premium subscription required',
      ),
    ];

    final unlockedCompanions = companions.where((c) => c.isUnlocked).toList();
    final lockedCompanions = companions.where((c) => !c.isUnlocked).toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          GlassmorphicContainer(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [AppTheme.accentColor, AppTheme.primaryColor],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.people_rounded,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Companions',
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          '${unlockedCompanions.length} of ${companions.length} unlocked',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: AppTheme.textSecondaryColor),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Active companion
          if (unlockedCompanions.isNotEmpty) ...[
            Text(
              'Active Companion',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.successColor,
              ),
            ),
            const SizedBox(height: 12),

            ...unlockedCompanions
                .where((c) => c.isActive)
                .map(
                  (companion) => _buildCompanionCard(companion, isActive: true),
                ),

            const SizedBox(height: 24),

            // Available companions
            Text(
              'Available Companions',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),

            ...unlockedCompanions
                .where((c) => !c.isActive)
                .map(
                  (companion) =>
                      _buildCompanionCard(companion, isActive: false),
                ),

            const SizedBox(height: 24),
          ],

          // Locked companions
          if (lockedCompanions.isNotEmpty) ...[
            Text(
              'Locked Companions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 12),

            ...lockedCompanions.map(
              (companion) => _buildCompanionCard(
                companion,
                isActive: false,
                isLocked: true,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCompanionCard(
    _Companion companion, {
    required bool isActive,
    bool isLocked = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: GlassmorphicContainer(
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border:
                isActive
                    ? Border.all(color: AppTheme.successColor, width: 2)
                    : null,
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        gradient:
                            isLocked
                                ? LinearGradient(
                                  colors: [
                                    Colors.grey.shade600,
                                    Colors.grey.shade700,
                                  ],
                                )
                                : LinearGradient(
                                  colors: [
                                    _getPersonalityColor(companion.personality),
                                    _getPersonalityColor(
                                      companion.personality,
                                    ).withValues(alpha: 0.7),
                                  ],
                                ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow:
                            !isLocked
                                ? [
                                  BoxShadow(
                                    color: _getPersonalityColor(
                                      companion.personality,
                                    ).withValues(alpha: 0.3),
                                    blurRadius: 12,
                                    spreadRadius: 2,
                                  ),
                                ]
                                : null,
                      ),
                      child: Center(
                        child: Text(
                          companion.avatar,
                          style: const TextStyle(fontSize: 28),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                companion.name,
                                style: Theme.of(
                                  context,
                                ).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color:
                                      isLocked
                                          ? AppTheme.textSecondaryColor
                                          : Colors.white,
                                ),
                              ),
                              if (isActive) ...[
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AppTheme.successColor,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: const Text(
                                    'ACTIVE',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            companion.personality.displayName,
                            style: Theme.of(
                              context,
                            ).textTheme.bodyMedium?.copyWith(
                              color:
                                  isLocked
                                      ? Colors.grey.shade600
                                      : _getPersonalityColor(
                                        companion.personality,
                                      ),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          if (!isLocked && companion.relationshipLevel > 0) ...[
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  Icons.favorite_rounded,
                                  color: AppTheme.accentColor,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Level ${companion.relationshipLevel}',
                                  style: Theme.of(
                                    context,
                                  ).textTheme.bodySmall?.copyWith(
                                    color: AppTheme.accentColor,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                    if (!isLocked && !isActive)
                      ElevatedButton(
                        onPressed: () => _switchToCompanion(companion),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppTheme.primaryColor,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                        ),
                        child: const Text('Switch'),
                      )
                    else if (isLocked)
                      Icon(Icons.lock_rounded, color: Colors.grey.shade600),
                  ],
                ),

                const SizedBox(height: 16),

                Text(
                  companion.description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color:
                        isLocked
                            ? Colors.grey.shade600
                            : AppTheme.textSecondaryColor,
                  ),
                ),

                if (isLocked && companion.unlockRequirement != null) ...[
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppTheme.warningColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: AppTheme.warningColor.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline_rounded,
                          color: AppTheme.warningColor,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'Unlock requirement: ${companion.unlockRequirement}',
                            style: Theme.of(
                              context,
                            ).textTheme.bodySmall?.copyWith(
                              color: AppTheme.warningColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],

                if (!isLocked && companion.unlockedAt != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    'Unlocked ${_formatMemoryTimestamp(companion.unlockedAt!)}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAchievementsTab(List<String> achievements, bool isTablet) {
    final allAchievements = _getAllAchievements();
    final unlockedAchievements =
        allAchievements.where((a) => achievements.contains(a.id)).toList();
    final lockedAchievements =
        allAchievements.where((a) => !achievements.contains(a.id)).toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Progress overview
          GlassmorphicContainer(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: AppTheme.primaryGradient,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.emoji_events_rounded,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Achievements',
                          style: Theme.of(context).textTheme.titleLarge
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          '${unlockedAchievements.length} of ${allAchievements.length} unlocked',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: AppTheme.textSecondaryColor),
                        ),
                      ],
                    ),
                  ),
                  CircularProgressIndicator(
                    value: unlockedAchievements.length / allAchievements.length,
                    backgroundColor: Colors.white.withValues(alpha: 0.1),
                    valueColor: AlwaysStoppedAnimation<Color>(
                      AppTheme.primaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Unlocked achievements
          if (unlockedAchievements.isNotEmpty) ...[
            Text(
              'Unlocked (${unlockedAchievements.length})',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.successColor,
              ),
            ),
            const SizedBox(height: 12),
            ...unlockedAchievements.map(
              (achievement) => _buildAchievementCard(achievement, true),
            ),
            const SizedBox(height: 24),
          ],

          // Locked achievements
          if (lockedAchievements.isNotEmpty) ...[
            Text(
              'Locked (${lockedAchievements.length})',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            const SizedBox(height: 12),
            ...lockedAchievements.map(
              (achievement) => _buildAchievementCard(achievement, false),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildAchievementCard(_Achievement achievement, bool isUnlocked) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: GlassmorphicContainer(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient:
                      isUnlocked
                          ? LinearGradient(
                            colors: [
                              achievement.color,
                              achievement.color.withValues(alpha: 0.7),
                            ],
                          )
                          : LinearGradient(
                            colors: [
                              Colors.grey.shade600,
                              Colors.grey.shade700,
                            ],
                          ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow:
                      isUnlocked
                          ? [
                            BoxShadow(
                              color: achievement.color.withValues(alpha: 0.3),
                              blurRadius: 8,
                              spreadRadius: 1,
                            ),
                          ]
                          : null,
                ),
                child: Icon(achievement.icon, color: Colors.white, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      achievement.title,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color:
                            isUnlocked
                                ? Colors.white
                                : AppTheme.textSecondaryColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      achievement.description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color:
                            isUnlocked
                                ? AppTheme.textSecondaryColor
                                : Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              if (isUnlocked)
                Icon(
                  Icons.check_circle_rounded,
                  color: AppTheme.successColor,
                  size: 24,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSettingsTab(UserModel user, bool isTablet) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          GlassmorphicContainer(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(
                    Icons.person_outline_rounded,
                    color: AppTheme.primaryColor,
                  ),
                  title: const Text('Edit Profile'),
                  subtitle: const Text('Update your profile information'),
                  trailing: const Icon(Icons.chevron_right_rounded),
                  onTap: () => Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const EditProfileSettingsPage(),
                    ),
                  ),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(
                    Icons.record_voice_over_rounded,
                    color: AppTheme.primaryColor,
                  ),
                  title: const Text('Voice Settings'),
                  subtitle: const Text('Configure voice preferences'),
                  trailing: const Icon(Icons.chevron_right_rounded),
                  onTap: () => Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const VoiceSettingsPage(),
                    ),
                  ),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(
                    Icons.notifications_rounded,
                    color: AppTheme.primaryColor,
                  ),
                  title: const Text('Notifications'),
                  subtitle: const Text('Manage notification preferences'),
                  trailing: const Icon(Icons.chevron_right_rounded),
                  onTap: () => Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const NotificationsSettingsPage(),
                    ),
                  ),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(
                    Icons.privacy_tip_rounded,
                    color: AppTheme.primaryColor,
                  ),
                  title: const Text('Privacy & Data'),
                  subtitle: const Text('Data and privacy settings'),
                  trailing: const Icon(Icons.chevron_right_rounded),
                  onTap: () => Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const PrivacySettingsPage(),
                    ),
                  ),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(
                    Icons.help_outline_rounded,
                    color: AppTheme.primaryColor,
                  ),
                  title: const Text('Help & Support'),
                  subtitle: const Text('Get help and contact support'),
                  trailing: const Icon(Icons.chevron_right_rounded),
                  onTap: () => Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const HelpSupportPage(),
                    ),
                  ),
                ),
                const Divider(height: 1),
                ListTile(
                  leading: const Icon(
                    Icons.logout_rounded,
                    color: AppTheme.errorColor,
                  ),
                  title: const Text(
                    'Sign Out',
                    style: TextStyle(color: AppTheme.errorColor),
                  ),
                  subtitle: const Text('Sign out of your account'),
                  trailing: const Icon(Icons.chevron_right_rounded),
                  onTap: _showSignOutDialog,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showSignOutDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.surfaceColor,
            title: const Text('Sign Out'),
            content: const Text('Are you sure you want to sign out?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  ref.read(authProvider.notifier).signOut();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.errorColor,
                ),
                child: const Text('Sign Out'),
              ),
            ],
          ),
    );
  }

  void _showAllRelationshipLevels() {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            backgroundColor: AppTheme.surfaceColor,
            child: Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Relationship Levels',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 20),
                  ...RelationshipLevel.values.map(
                    (level) => Container(
                      margin: const EdgeInsets.only(bottom: 16),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppTheme.glassMorphColor,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.1),
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                width: 8,
                                height: 8,
                                decoration: BoxDecoration(
                                  color: _getRelationshipLevelColor(
                                    level.level,
                                  ),
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                level.displayName,
                                style: Theme.of(context).textTheme.titleSmall
                                    ?.copyWith(fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _getRelationshipLevelDescription(level),
                            style: Theme.of(context).textTheme.bodySmall
                                ?.copyWith(color: AppTheme.textSecondaryColor),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Align(
                    alignment: Alignment.centerRight,
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Close'),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  Color _getRelationshipLevelColor(int level) {
    switch (level) {
      case 1:
        return AppTheme.textSecondaryColor;
      case 2:
        return AppTheme.successColor;
      case 3:
        return AppTheme.primaryColor;
      case 4:
        return AppTheme.accentColor;
      default:
        return AppTheme.textSecondaryColor;
    }
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'preferences':
        return Icons.tune_rounded;
      case 'personal':
        return Icons.person_rounded;
      case 'lifestyle':
        return Icons.energy_savings_leaf_rounded;
      case 'interests':
        return Icons.interests_rounded;
      case 'work':
        return Icons.work_rounded;
      default:
        return Icons.memory_rounded;
    }
  }

  Color _getImportanceColor(MemoryImportance importance) {
    switch (importance) {
      case MemoryImportance.low:
        return AppTheme.textSecondaryColor;
      case MemoryImportance.medium:
        return AppTheme.warningColor;
      case MemoryImportance.high:
        return AppTheme.accentColor;
    }
  }

  Color _getPersonalityColor(CompanionPersonality personality) {
    switch (personality) {
      case CompanionPersonality.caringFriend:
        return AppTheme.successColor;
      case CompanionPersonality.playfulCompanion:
        return AppTheme.warningColor;
      case CompanionPersonality.wiseMentor:
        return AppTheme.primaryColor;
      case CompanionPersonality.romanticPartner:
        return AppTheme.accentColor;
      case CompanionPersonality.supportiveTherapist:
        return Colors.teal;
    }
  }

  String _formatMemoryTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }

  void _showMemoryManagementDialog() {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            backgroundColor: AppTheme.surfaceColor,
            child: Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Memory Management',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 20),

                  ListTile(
                    leading: const Icon(
                      Icons.auto_delete_rounded,
                      color: AppTheme.primaryColor,
                    ),
                    title: const Text('Auto-delete old memories'),
                    subtitle: const Text('Remove memories older than 6 months'),
                    trailing: Switch(
                      value: true, // Mock value
                      onChanged: (value) {},
                    ),
                  ),

                  ListTile(
                    leading: const Icon(
                      Icons.priority_high_rounded,
                      color: AppTheme.warningColor,
                    ),
                    title: const Text('Priority memories only'),
                    subtitle: const Text('Only remember high importance items'),
                    trailing: Switch(
                      value: false, // Mock value
                      onChanged: (value) {},
                    ),
                  ),

                  ListTile(
                    leading: const Icon(
                      Icons.download_rounded,
                      color: AppTheme.successColor,
                    ),
                    title: const Text('Export memories'),
                    subtitle: const Text('Download your memory data'),
                    trailing: const Icon(Icons.chevron_right_rounded),
                    onTap: () {},
                  ),

                  ListTile(
                    leading: const Icon(
                      Icons.delete_forever_rounded,
                      color: AppTheme.errorColor,
                    ),
                    title: const Text(
                      'Clear all memories',
                      style: TextStyle(color: AppTheme.errorColor),
                    ),
                    subtitle: const Text(
                      'Permanently delete all stored memories',
                    ),
                    trailing: const Icon(Icons.chevron_right_rounded),
                    onTap: () => _showClearMemoriesDialog(),
                  ),

                  const SizedBox(height: 20),
                  Align(
                    alignment: Alignment.centerRight,
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: const Text('Close'),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  void _showClearMemoriesDialog() {
    Navigator.of(context).pop(); // Close memory management dialog
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.surfaceColor,
            title: const Text('Clear All Memories'),
            content: const Text(
              'Are you sure you want to delete all memories? This action cannot be undone and will affect how your companion remembers you.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // TODO: Implement clear memories functionality
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('All memories cleared')),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.errorColor,
                ),
                child: const Text('Clear All'),
              ),
            ],
          ),
    );
  }

  void _handleMemoryAction(_Memory memory, String action) {
    switch (action) {
      case 'edit':
        _showEditMemoryDialog(memory);
        break;
      case 'delete':
        _showDeleteMemoryDialog(memory);
        break;
    }
  }

  void _showEditMemoryDialog(_Memory memory) {
    final titleController = TextEditingController(text: memory.title);
    final contentController = TextEditingController(text: memory.content);

    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            backgroundColor: AppTheme.surfaceColor,
            child: Container(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Edit Memory',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 20),

                  TextField(
                    controller: titleController,
                    decoration: const InputDecoration(
                      labelText: 'Title',
                      border: OutlineInputBorder(),
                    ),
                  ),

                  const SizedBox(height: 16),

                  TextField(
                    controller: contentController,
                    decoration: const InputDecoration(
                      labelText: 'Content',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                  ),

                  const SizedBox(height: 20),

                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('Cancel'),
                      ),
                      const SizedBox(width: 8),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          // TODO: Implement edit memory functionality
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Memory updated')),
                          );
                        },
                        child: const Text('Save'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
    );
  }

  void _showDeleteMemoryDialog(_Memory memory) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.surfaceColor,
            title: const Text('Delete Memory'),
            content: Text('Are you sure you want to delete "${memory.title}"?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // TODO: Implement delete memory functionality
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Memory deleted')),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.errorColor,
                ),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  void _switchToCompanion(_Companion companion) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.surfaceColor,
            title: Text('Switch to ${companion.name}'),
            content: Text(
              'Are you sure you want to switch to ${companion.name}? Your current conversation context will be saved.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // TODO: Implement companion switching functionality
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Switched to ${companion.name}')),
                  );
                },
                child: const Text('Switch'),
              ),
            ],
          ),
    );
  }

  String _getRelationshipLevelDescription(RelationshipLevel level) {
    switch (level) {
      case RelationshipLevel.acquaintance:
        return "Getting to know each other through basic conversation and general topics.";
      case RelationshipLevel.friend:
        return "Comfortable with personal topics and mild flirtation.";
      case RelationshipLevel.closeFriend:
        return "Deep emotional connection with intimate conversation and support.";
      case RelationshipLevel.intimate:
        return "Closest bond with full trust and intimate content access.";
    }
  }

  String _getEnvironmentDisplayName(String environment) {
    return environment
        .split('_')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }

  String _getOutfitDisplayName(String outfit) {
    return outfit
        .split('_')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }

  List<_Achievement> _getAllAchievements() {
    return [
      _Achievement(
        id: 'first_chat',
        title: 'First Words',
        description: 'Send your first message to Ella',
        icon: Icons.chat_rounded,
        color: AppTheme.primaryColor,
      ),
      _Achievement(
        id: 'voice_pioneer',
        title: 'Voice Pioneer',
        description: 'Send your first voice message',
        icon: Icons.mic_rounded,
        color: AppTheme.accentColor,
      ),
      _Achievement(
        id: 'level_5',
        title: 'Rising Star',
        description: 'Reach level 5',
        icon: Icons.star_rounded,
        color: AppTheme.warningColor,
      ),
      _Achievement(
        id: 'level_10',
        title: 'Experienced',
        description: 'Reach level 10',
        icon: Icons.stars_rounded,
        color: AppTheme.successColor,
      ),
      _Achievement(
        id: 'chatterbox',
        title: 'Chatterbox',
        description: 'Send 100 messages',
        icon: Icons.forum_rounded,
        color: AppTheme.primaryColor,
      ),
      _Achievement(
        id: 'shopaholic',
        title: 'Shopaholic',
        description: 'Purchase 5 items from the shop',
        icon: Icons.shopping_bag_rounded,
        color: AppTheme.accentColor,
      ),
    ];
  }
}

class _StatItem {
  final String title;
  final String value;
  final IconData icon;
  final Color color;

  _StatItem(this.title, this.value, this.icon, this.color);
}

class _Achievement {
  final String id;
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  _Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}

class _Memory {
  final String id;
  final String title;
  final String content;
  final String category;
  final DateTime timestamp;
  final MemoryImportance importance;

  _Memory({
    required this.id,
    required this.title,
    required this.content,
    required this.category,
    required this.timestamp,
    required this.importance,
  });
}

enum MemoryImportance { low, medium, high }

class _Companion {
  final String id;
  final String name;
  final CompanionPersonality personality;
  final bool isActive;
  final bool isUnlocked;
  final int relationshipLevel;
  final DateTime? unlockedAt;
  final String description;
  final String avatar;
  final String? unlockRequirement;

  _Companion({
    required this.id,
    required this.name,
    required this.personality,
    required this.isActive,
    required this.isUnlocked,
    required this.relationshipLevel,
    this.unlockedAt,
    required this.description,
    required this.avatar,
    this.unlockRequirement,
  });

  String _getPetDisplayName(String pet) {
    return pet
        .split('_')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }
}

// Enhanced Collection Tab Widget
class _EnhancedCollectionTab extends ConsumerStatefulWidget {
  final UserModel user;
  final bool isTablet;

  const _EnhancedCollectionTab({
    required this.user,
    required this.isTablet,
  });

  @override
  ConsumerState<_EnhancedCollectionTab> createState() => _EnhancedCollectionTabState();
}

class _EnhancedCollectionTabState extends ConsumerState<_EnhancedCollectionTab>
    with TickerProviderStateMixin {
  late TabController _categoryTabController;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  final List<_CollectionCategory> _categories = [
    _CollectionCategory(
      type: ShopItemType.environment,
      name: 'Environments',
      icon: Icons.landscape_rounded,
      color: AppTheme.primaryColor,
    ),
    _CollectionCategory(
      type: ShopItemType.outfit,
      name: 'Outfits',
      icon: Icons.checkroom_rounded,
      color: AppTheme.accentColor,
    ),
    _CollectionCategory(
      type: ShopItemType.accessory,
      name: 'Accessories',
      icon: Icons.diamond_rounded,
      color: Colors.purple,
    ),
    _CollectionCategory(
      type: ShopItemType.companion,
      name: 'Companions',
      icon: Icons.person_rounded,
      color: Colors.blue,
    ),
    _CollectionCategory(
      type: ShopItemType.pet,
      name: 'Pets',
      icon: Icons.pets_rounded,
      color: Colors.green,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _categoryTabController = TabController(length: _categories.length, vsync: this);
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(_fadeController);
    _fadeController.forward();
  }

  @override
  void dispose() {
    _categoryTabController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Column(
        children: [
          const SizedBox(height: 4),

          // Category tabs
          _buildCategoryTabs(),

          // Collection content
          Expanded(
            child: TabBarView(
              controller: _categoryTabController,
              children: _categories.map((category) {
                return _buildCategoryContent(category);
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryTabs() {
    return AnimatedBuilder(
      animation: _categoryTabController,
      builder: (context, child) {
        return Container(
          height: 36,
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _categories.length,
            itemBuilder: (context, index) {
              final category = _categories[index];
              final isSelected = _categoryTabController.index == index;

              return GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  _categoryTabController.animateTo(index);
                },
                child: Container(
                  margin: const EdgeInsets.only(right: 8),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    gradient: isSelected
                        ? LinearGradient(
                            colors: [category.color, category.color.withValues(alpha: 0.8)],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          )
                        : null,
                    color: isSelected
                        ? null
                        : Colors.white.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(
                      color: isSelected
                          ? category.color
                          : Colors.white.withValues(alpha: 0.1),
                      width: isSelected ? 2 : 1,
                    ),
                    boxShadow: isSelected ? [
                      BoxShadow(
                        color: category.color.withValues(alpha: 0.3),
                        offset: const Offset(0, 2),
                        blurRadius: 8,
                      ),
                    ] : null,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        category.icon,
                        size: 16,
                        color: isSelected ? Colors.white : AppTheme.textSecondaryColor,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        category.name,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: isSelected ? Colors.white : AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ).animate(delay: Duration(milliseconds: index * 100))
                .fadeIn(duration: 300.ms)
                .slideX(begin: 0.3, end: 0);
            },
          ),
        );
      },
    );
  }

  Widget _buildCategoryContent(_CollectionCategory category) {
    final shopState = ref.watch(shopProvider);
    final ownedItems = _getOwnedItemsForCategory(category.type, shopState);

    return SingleChildScrollView(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Category header
          _buildCategoryHeader(category, ownedItems.length),

          const SizedBox(height: 12),

          // Items grid or empty state
          if (ownedItems.isEmpty)
            _buildEmptyState(category)
          else
            _buildItemsGrid(ownedItems, category),

          const SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildCategoryHeader(_CollectionCategory category, int itemCount) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: category.color.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Icon(category.icon, color: category.color, size: 28),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                category.name,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '$itemCount items owned',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: category.color.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: category.color.withValues(alpha: 0.3)),
          ),
          child: Text(
            '$itemCount',
            style: TextStyle(
              color: category.color,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(_CollectionCategory category) {
    return GlassmorphicContainer(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Center(
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: category.color.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  category.icon,
                  color: category.color.withValues(alpha: 0.6),
                  size: 48,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                'No ${category.name.toLowerCase()} owned yet',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppTheme.textSecondaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Visit the shop to discover amazing ${category.name.toLowerCase()}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              GlassmorphicButton(
                onPressed: () {
                  ref.read(navigationIndexProvider.notifier).state = 1;
                },
                backgroundColor: category.color,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.shopping_bag_rounded, color: Colors.white, size: 18),
                    const SizedBox(width: 8),
                    const Text(
                      'Visit Shop',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildItemsGrid(List<ShopItemModel> items, _CollectionCategory category) {
    final crossAxisCount = widget.isTablet ? 3 : 2;
    final spacing = widget.isTablet ? 20.0 : 16.0;

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.zero,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
        childAspectRatio: widget.isTablet ? 0.85 : 0.75,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return _buildItemCard(item, category, index)
            .animate(delay: Duration(milliseconds: index * 100))
            .fadeIn(duration: 400.ms)
            .slideY(begin: 0.3, end: 0);
      },
    );
  }

  Widget _buildItemCard(ShopItemModel item, _CollectionCategory category, int index) {
    final isCurrentlyEquipped = _isItemCurrentlyEquipped(item);

    return GestureDetector(
      onTap: () {
        // Add haptic feedback for better interaction
        HapticFeedback.lightImpact();
        // Enter collection preview mode instead of detail mode
        ref.read(collectionPreviewProvider.notifier).enterPreviewMode(item, category.type);
      },
      child: GlassmorphicContainer(
        padding: const EdgeInsets.all(0),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: isCurrentlyEquipped
                ? Border.all(color: category.color, width: 2)
                : null,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Item image with equipped indicator
              Expanded(
                flex: 3,
                child: Stack(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                        color: category.color.withValues(alpha: 0.1),
                      ),
                      child: item.imageUrl != null
                          ? ClipRRect(
                              borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
                              child: Image.network(
                                item.imageUrl!,
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) =>
                                    _buildItemIcon(item, category),
                              ),
                            )
                          : _buildItemIcon(item, category),
                    ),

                    // Equipped indicator in top-right corner
                    if (isCurrentlyEquipped)
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: category.color,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.3),
                                offset: const Offset(0, 2),
                                blurRadius: 4,
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.check_rounded,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                  ],
                ),
              ),

              // Item info
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Item name
                      Text(
                        item.name,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: 13,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 4),

                      // Rarity badge
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: _getRarityColor(item.rarity).withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(
                            color: _getRarityColor(item.rarity).withValues(alpha: 0.3),
                          ),
                        ),
                        child: Text(
                          item.rarity.name.toUpperCase(),
                          style: TextStyle(
                            color: _getRarityColor(item.rarity),
                            fontSize: 9,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),

                      const Spacer(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ).animate(delay: Duration(milliseconds: index * 100))
        .fadeIn(duration: 400.ms)
        .slideY(begin: 0.3, end: 0),
    );
  }

  Widget _buildItemIcon(ShopItemModel item, _CollectionCategory category) {
    return Container(
      decoration: BoxDecoration(
        color: category.color.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Center(
        child: Icon(
          category.icon,
          color: category.color,
          size: 40,
        ),
      ),
    );
  }

  Color _getRarityColor(ShopItemRarity rarity) {
    switch (rarity) {
      case ShopItemRarity.common:
        return Colors.grey;
      case ShopItemRarity.rare:
        return Colors.blue;
      case ShopItemRarity.epic:
        return Colors.purple;
      case ShopItemRarity.legendary:
        return Colors.orange;
    }
  }

  bool _isItemCurrentlyEquipped(ShopItemModel item) {
    // Use mock equipment state from storage for now
    switch (item.type) {
      case ShopItemType.environment:
        return widget.user.selectedEnvironment == item.id;
      case ShopItemType.outfit:
        final selectedOutfit = StorageService.getProgress<String>('selected_outfit');
        return selectedOutfit == item.id;
      case ShopItemType.companion:
        final selectedCompanion = StorageService.getProgress<String>('selected_companion');
        return selectedCompanion == item.id;
      case ShopItemType.accessory:
        final selectedAccessory = StorageService.getProgress<String>('selected_accessory');
        return selectedAccessory == item.id;
      case ShopItemType.pet:
        final selectedPet = StorageService.getProgress<String>('selected_pet');
        return selectedPet == item.id;
      default:
        return false;
    }
  }

  List<ShopItemModel> _getOwnedItemsForCategory(ShopItemType type, ShopState shopState) {
    List<ShopItemModel> categoryItems;

    switch (type) {
      case ShopItemType.environment:
        categoryItems = shopState.environments;
        break;
      case ShopItemType.outfit:
        categoryItems = shopState.outfits;
        break;
      case ShopItemType.accessory:
        categoryItems = shopState.accessories;
        break;
      case ShopItemType.companion:
        categoryItems = shopState.companions;
        break;
      case ShopItemType.pet:
        categoryItems = shopState.pets;
        break;
      default:
        categoryItems = [];
    }

    return categoryItems.where((item) => _isItemOwned(item)).toList();
  }

  bool _isItemOwned(ShopItemModel item) {
    switch (item.type) {
      case ShopItemType.environment:
        return widget.user.ownedEnvironments.contains(item.id);
      case ShopItemType.outfit:
      case ShopItemType.accessory:
      case ShopItemType.companion:
      case ShopItemType.pet:
        return widget.user.ownedOutfits.contains(item.id);
      default:
        return false;
    }
  }
}

// Collection Category Model
class _CollectionCategory {
  final ShopItemType type;
  final String name;
  final IconData icon;
  final Color color;

  const _CollectionCategory({
    required this.type,
    required this.name,
    required this.icon,
    required this.color,
  });
}