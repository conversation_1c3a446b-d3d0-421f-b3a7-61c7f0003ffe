import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_theme.dart';
import '../../models/shop/shop_item_model.dart';
import '../../providers/shop_preview_provider.dart';
import '../common/glassmorphic_container.dart';

class CategorySwitcher extends ConsumerWidget {
  final ShopItemType currentCategory;
  final Function(ShopItemType) onCategoryChanged;
  final bool isInPreviewMode;

  const CategorySwitcher({
    super.key,
    required this.currentCategory,
    required this.onCategoryChanged,
    this.isInPreviewMode = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final categories = [
      ShopItemType.environment,
      ShopItemType.outfit,
      ShopItemType.accessory,
    ];

    return GlassmorphicContainer(
      padding: const EdgeInsets.all(4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: categories.map((category) {
          final isSelected = category == currentCategory;
          final index = categories.indexOf(category);
          
          return Padding(
            padding: EdgeInsets.only(right: index < categories.length - 1 ? 4 : 0),
            child: _CategoryButton(
              category: category,
              isSelected: isSelected,
              onTap: () => onCategoryChanged(category),
              isInPreviewMode: isInPreviewMode,
            ).animate(delay: Duration(milliseconds: index * 100))
              .fadeIn(duration: 300.ms)
              .scale(begin: const Offset(0.8, 0.8), end: const Offset(1.0, 1.0)),
          );
        }).toList(),
      ),
    );
  }
}

class _CategoryButton extends StatelessWidget {
  final ShopItemType category;
  final bool isSelected;
  final VoidCallback onTap;
  final bool isInPreviewMode;

  const _CategoryButton({
    required this.category,
    required this.isSelected,
    required this.onTap,
    required this.isInPreviewMode,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: isSelected ? AppTheme.primaryGradient : null,
          color: isSelected ? null : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected 
                ? Colors.transparent 
                : Colors.white.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _getIconForCategory(category),
              size: isInPreviewMode ? 16 : 20,
              color: isSelected ? Colors.white : AppTheme.textSecondaryColor,
            ),
            if (!isInPreviewMode) ...[
              const SizedBox(width: 8),
              Text(
                _getCategoryDisplayName(category),
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  color: isSelected ? Colors.white : AppTheme.textSecondaryColor,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  IconData _getIconForCategory(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return Icons.landscape_rounded;
      case ShopItemType.outfit:
        return Icons.checkroom_rounded;
      case ShopItemType.accessory:
        return Icons.diamond_rounded;
      default:
        return Icons.shopping_bag_rounded;
    }
  }

  String _getCategoryDisplayName(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return 'Environments';
      case ShopItemType.outfit:
        return 'Outfits';
      case ShopItemType.accessory:
        return 'Accessories';
      default:
        return 'Items';
    }
  }
}

// Compact version for preview mode
class CompactCategorySwitcher extends ConsumerWidget {
  final ShopItemType currentCategory;
  final Function(ShopItemType) onCategoryChanged;

  const CompactCategorySwitcher({
    super.key,
    required this.currentCategory,
    required this.onCategoryChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GestureDetector(
      onTap: _showCategorySelector,
      child: GlassmorphicContainer(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              _getIconForCategory(currentCategory),
              color: AppTheme.primaryColor,
              size: 18,
            ),
            const SizedBox(width: 6),
            Text(
              _getCategoryDisplayName(currentCategory),
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.keyboard_arrow_down_rounded,
              color: AppTheme.textSecondaryColor,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _showCategorySelector() {
    // This would show a dropdown or modal for category selection
    // Implementation depends on your preferred UX pattern
  }

  IconData _getIconForCategory(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return Icons.landscape_rounded;
      case ShopItemType.outfit:
        return Icons.checkroom_rounded;
      case ShopItemType.accessory:
        return Icons.diamond_rounded;
      default:
        return Icons.shopping_bag_rounded;
    }
  }

  String _getCategoryDisplayName(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return 'Environments';
      case ShopItemType.outfit:
        return 'Outfits';
      case ShopItemType.accessory:
        return 'Accessories';
      default:
        return 'Items';
    }
  }
}