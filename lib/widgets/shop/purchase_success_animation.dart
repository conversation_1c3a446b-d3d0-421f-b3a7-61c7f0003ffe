import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_theme.dart';
import '../../models/shop/shop_item_model.dart';
import '../common/glassmorphic_container.dart';

class PurchaseSuccessAnimation extends StatefulWidget {
  final ShopItemModel item;
  final VoidCallback onComplete;

  const PurchaseSuccessAnimation({
    super.key,
    required this.item,
    required this.onComplete,
  });

  @override
  State<PurchaseSuccessAnimation> createState() =>
      _PurchaseSuccessAnimationState();
}

class _PurchaseSuccessAnimationState extends State<PurchaseSuccessAnimation>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late AnimationController _particleController;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _particleController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _startAnimation();
  }

  void _startAnimation() async {
    _controller.forward();
    _particleController.forward();

    await Future.delayed(const Duration(milliseconds: 2500));
    widget.onComplete();
  }

  @override
  void dispose() {
    _controller.dispose();
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black.withValues(alpha: 0.8),
      child: Stack(
        children: [
          // Particle effects
          ...List.generate(20, (index) => _buildParticle(index)),

          // Main success content
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Success icon
                Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        gradient: AppTheme.primaryGradient,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppTheme.primaryColor.withValues(alpha: 0.5),
                            blurRadius: 30,
                            spreadRadius: 10,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.check_rounded,
                        size: 60,
                        color: Colors.white,
                      ),
                    )
                    .animate(controller: _controller)
                    .scale(
                      begin: const Offset(0, 0),
                      end: const Offset(1, 1),
                      duration: 600.ms,
                      curve: Curves.elasticOut,
                    )
                    .then(delay: 200.ms)
                    .shake(duration: 300.ms),

                const SizedBox(height: 32),

                // Success message
                GlassmorphicContainer(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        children: [
                          Text(
                            'Purchase Successful!',
                            style: Theme.of(
                              context,
                            ).textTheme.headlineSmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          const SizedBox(height: 12),

                          Text(
                            'You now own ${widget.item.name}',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(color: AppTheme.textSecondaryColor),
                            textAlign: TextAlign.center,
                          ),

                          const SizedBox(height: 16),

                          // Item preview
                          Container(
                            width: 80,
                            height: 80,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: _getRarityColor(widget.item.rarity),
                                width: 2,
                              ),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(10),
                              child:
                                  widget.item.imageUrl != null
                                      ? Image.network(
                                        widget.item.imageUrl!,
                                        fit: BoxFit.cover,
                                        errorBuilder:
                                            (context, error, stackTrace) =>
                                                _buildItemIcon(),
                                      )
                                      : _buildItemIcon(),
                            ),
                          ),
                        ],
                      ),
                    )
                    .animate(controller: _controller)
                    .fadeIn(delay: 400.ms, duration: 600.ms)
                    .slideY(
                      begin: 0.3,
                      end: 0,
                      delay: 400.ms,
                      duration: 600.ms,
                    ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildParticle(int index) {
    final random = (index * 37) % 100; // Pseudo-random based on index
    final startX = (random / 100) * MediaQuery.of(context).size.width;
    final startY = MediaQuery.of(context).size.height * 0.6;
    final endY = startY - (100 + (random % 200));
    final endX = startX + (-50 + (random % 100));

    return Positioned(
      left: startX,
      top: startY,
      child: Container(
            width: 6 + (random % 4),
            height: 6 + (random % 4),
            decoration: BoxDecoration(
              color: _getParticleColor(index),
              shape: BoxShape.circle,
            ),
          )
          .animate(controller: _particleController)
          .move(
            begin: Offset.zero,
            end: Offset(endX - startX, endY - startY),
            duration: (1000 + (random % 500)).ms,
            curve: Curves.easeOut,
          )
          .fadeOut(delay: (800 + (random % 400)).ms, duration: 400.ms),
    );
  }

  Widget _buildItemIcon() {
    return Container(
      decoration: BoxDecoration(
        gradient: _getGradientForType(widget.item.type),
      ),
      child: Center(
        child: Icon(
          _getIconForType(widget.item.type),
          size: 32,
          color: Colors.white,
        ),
      ),
    );
  }

  Color _getParticleColor(int index) {
    final colors = [
      AppTheme.primaryColor,
      AppTheme.accentColor,
      AppTheme.successColor,
      Colors.yellow,
      Colors.pink,
    ];
    return colors[index % colors.length];
  }

  LinearGradient _getGradientForType(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return const LinearGradient(
          colors: [Color(0xFF4ECDC4), Color(0xFF44A08D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case ShopItemType.outfit:
        return const LinearGradient(
          colors: [Color(0xFF6C63FF), Color(0xFF9C27B0)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case ShopItemType.accessory:
        return const LinearGradient(
          colors: [Color(0xFFFF6B6B), Color(0xFFFFE66D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      default:
        return AppTheme.primaryGradient;
    }
  }

  IconData _getIconForType(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return Icons.landscape_rounded;
      case ShopItemType.outfit:
        return Icons.checkroom_rounded;
      case ShopItemType.accessory:
        return Icons.diamond_rounded;
      default:
        return Icons.shopping_bag_rounded;
    }
  }

  Color _getRarityColor(ShopItemRarity rarity) {
    switch (rarity) {
      case ShopItemRarity.common:
        return Colors.grey;
      case ShopItemRarity.rare:
        return Colors.blue;
      case ShopItemRarity.epic:
        return Colors.purple;
      case ShopItemRarity.legendary:
        return Colors.orange;
    }
  }
}
