import 'package:ellahai/models/shop/shop_preview_models.dart';
import 'package:ellahai/models/user/user_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:ui';

import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import '../../providers/shop_provider.dart';
import '../../providers/auth_provider.dart';
import '../../providers/shop_preview_provider.dart';
import '../../services/relationship/relationship_gate_controller.dart';
import '../common/glassmorphic_container.dart';
import '../navigation/main_navigation.dart';
import '../../models/shop/shop_item_model.dart';

class ShopScreenOverlay extends ConsumerStatefulWidget {
  const ShopScreenOverlay({super.key});

  @override
  ConsumerState<ShopScreenOverlay> createState() => _ShopScreenOverlayState();
}

class _ShopScreenOverlayState extends ConsumerState<ShopScreenOverlay>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(currentUserProvider);
    final shopState = ref.watch(shopProvider);
    final previewState = ref.watch(shopPreviewProvider);
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;

    print('=== SHOP OVERLAY BUILD ===');
    print('Shop state loading: ${shopState.isLoading}');
    print(
      'Shop items count: ${shopState.environments.length} environments, ${shopState.outfits.length} outfits, ${shopState.accessories.length} accessories, ${shopState.pets.length} pets',
    );
    print('Preview state: isInPreviewMode = ${previewState.isInPreviewMode}');
    print('User: ${user?.id ?? 'null'}');
    print('========================');

    return AnimatedOpacity(
      opacity: previewState.isInPreviewMode ? 0.0 : 1.0,
      duration: const Duration(milliseconds: 300),
      child: IgnorePointer(
        ignoring: previewState.isInPreviewMode,
        child: Container(
          margin: EdgeInsets.all(isTablet ? 24 : 16),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(24),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withValues(alpha: 0.15),
                      Colors.white.withValues(alpha: 0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(24),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.2),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      offset: const Offset(0, 8),
                      blurRadius: 32,
                      spreadRadius: 0,
                    ),
                    BoxShadow(
                      color: AppTheme.primaryColor.withValues(alpha: 0.1),
                      offset: const Offset(0, 0),
                      blurRadius: 20,
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    _buildEnhancedHeader(user?.progress.hearts ?? 0, isTablet),
                    _buildEnhancedTabBar(isTablet),
                    Expanded(
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          _buildEnvironmentsTab(shopState.environments, isTablet),
                          _buildOutfitsTab(shopState.outfits, isTablet),
                          _buildAccessoriesTab(shopState.accessories, isTablet),
                          _buildCompanionsTab(shopState.companions, isTablet),
                          _buildPetsTab(shopState.pets, isTablet),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedHeader(int hearts, bool isTablet) {
    return Container(
      padding: EdgeInsets.all(isTablet ? 24 : 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryColor.withValues(alpha: 0.1),
            AppTheme.secondaryColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: IconButton(
              onPressed: () => ref.read(navigationIndexProvider.notifier).state = 0,
              icon: const Icon(
                Icons.close_rounded,
                color: Colors.white,
                size: 24,
              ),
              padding: const EdgeInsets.all(8),
              constraints: const BoxConstraints(minWidth: 40, minHeight: 40),
            ),
          ),
          Expanded(
            child: Padding(
            padding: const EdgeInsets.all(16),
            child:Text(
                    'Shop',
                    style: Theme.of(
                      context,
                    ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
            ),
          GlassmorphicContainer(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  AppConstants.currencySymbol,
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(width: 4),
                Text(
                  hearts.toString(),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnhancedTabBar(bool isTablet) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: isTablet ? 24 : 16),
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryColor.withValues(alpha: 0.3),
              offset: const Offset(0, 2),
              blurRadius: 8,
            ),
          ],
        ),
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.textSecondaryColor,
        labelStyle: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: isTablet ? 13 : 11,
        ),
        unselectedLabelStyle: TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: isTablet ? 13 : 11,
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        tabs: [
          Tab(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 10 : 6,
                vertical: isTablet ? 6 : 4,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.landscape_rounded, size: isTablet ? 20 : 18),
                  if (isTablet) ...[
                    const SizedBox(width: 4),
                    const Text('Environments'),
                  ],
                ],
              ),
            ),
          ),
          Tab(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 10 : 6,
                vertical: isTablet ? 6 : 4,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.checkroom_rounded, size: isTablet ? 20 : 18),
                  if (isTablet) ...[
                    const SizedBox(width: 4),
                    const Text('Outfits'),
                  ],
                ],
              ),
            ),
          ),
          Tab(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 10 : 6,
                vertical: isTablet ? 6 : 4,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.diamond_rounded, size: isTablet ? 20 : 18),
                  if (isTablet) ...[
                    const SizedBox(width: 4),
                    const Text('Accessories'),
                  ],
                ],
              ),
            ),
          ),
          Tab(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 10 : 6,
                vertical: isTablet ? 6 : 4,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.people_rounded, size: isTablet ? 20 : 18),
                  if (isTablet) ...[
                    const SizedBox(width: 4),
                    const Text('Companions'),
                  ],
                ],
              ),
            ),
          ),
          Tab(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 10 : 6,
                vertical: isTablet ? 6 : 4,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.pets_rounded, size: isTablet ? 20 : 18),
                  if (isTablet) ...[
                    const SizedBox(width: 4),
                    const Text('Pets'),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEnvironmentsTab(List<ShopItemModel> environments, bool isTablet) {
    if (environments.isEmpty) {
      return _buildEmptyState(
        'No environments available',
        'Discover beautiful spaces for your companion',
        Icons.landscape_rounded,
        isTablet,
      );
    }

    final crossAxisCount = isTablet ? 3 : 2;
    final padding = isTablet ? 24.0 : 16.0;
    final spacing = isTablet ? 20.0 : 16.0;

    return GridView.builder(
      padding: EdgeInsets.all(padding),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: isTablet ? 0.75 : 0.6, // Reduced aspect ratio for more height
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
      ),
      itemCount: environments.length,
      itemBuilder: (context, index) {
        final item = environments[index];
        return _buildEnhancedShopItem(item, isTablet);
      },
    );
  }

  Widget _buildOutfitsTab(List<ShopItemModel> outfits, bool isTablet) {
    if (outfits.isEmpty) {
      return _buildEmptyState(
        'No outfits available',
        'Style your companion with amazing outfits',
        Icons.checkroom_rounded,
        isTablet,
      );
    }

    final crossAxisCount = isTablet ? 3 : 2;
    final padding = isTablet ? 24.0 : 16.0;
    final spacing = isTablet ? 20.0 : 16.0;

    return GridView.builder(
      padding: EdgeInsets.all(padding),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: isTablet ? 0.7 : 0.55, // Reduced aspect ratio for more height
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
      ),
      itemCount: outfits.length,
      itemBuilder: (context, index) {
        final item = outfits[index];
        return _buildEnhancedShopItem(item, isTablet);
      },
    );
  }

  Widget _buildAccessoriesTab(List<ShopItemModel> accessories, bool isTablet) {
    if (accessories.isEmpty) {
      return _buildEmptyState(
        'No accessories available',
        'Add sparkle with beautiful accessories',
        Icons.diamond_rounded,
        isTablet,
      );
    }

    final crossAxisCount = isTablet ? 3 : 2;
    final padding = isTablet ? 24.0 : 16.0;
    final spacing = isTablet ? 20.0 : 16.0;

    return GridView.builder(
      padding: EdgeInsets.all(padding),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: isTablet ? 0.75 : 0.7, // Increased aspect ratio to prevent overflow
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
      ),
      itemCount: accessories.length,
      itemBuilder: (context, index) {
        final item = accessories[index];
        return _buildEnhancedShopItem(item, isTablet);
      },
    );
  }

  Widget _buildCompanionsTab(List<ShopItemModel> companions, bool isTablet) {
    if (companions.isEmpty) {
      return _buildEmptyState(
        'No companions available',
        'Meet amazing AI companions',
        Icons.people_rounded,
        isTablet,
      );
    }

    final crossAxisCount = isTablet ? 3 : 2;
    final padding = isTablet ? 24.0 : 16.0;
    final spacing = isTablet ? 20.0 : 16.0;

    return GridView.builder(
      padding: EdgeInsets.all(padding),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: isTablet ? 0.5 : 0.6, // Reduced aspect ratio for more height
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
      ),
      itemCount: companions.length,
      itemBuilder: (context, index) {
        final item = companions[index];
        return _buildEnhancedCompanionItem(item, isTablet);
      },
    );
  }

  Widget _buildPetsTab(List<ShopItemModel> pets, bool isTablet) {
    if (pets.isEmpty) {
      return _buildEmptyState(
        'No pets available',
        'Adopt adorable virtual pets',
        Icons.pets_rounded,
        isTablet,
      );
    }

    final crossAxisCount = isTablet ? 3 : 2;
    final padding = isTablet ? 24.0 : 16.0;
    final spacing = isTablet ? 20.0 : 16.0;

    return GridView.builder(
      padding: EdgeInsets.all(padding),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: isTablet ? 0.75 : 0.65, // Reduced aspect ratio for more height
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
      ),
      itemCount: pets.length,
      itemBuilder: (context, index) {
        final item = pets[index];
        return _buildEnhancedShopItem(item, isTablet);
      },
    );
  }

  Widget _buildEmptyState(String title, String subtitle, IconData icon, bool isTablet) {
    return Center(
      child: Container(
        margin: EdgeInsets.all(isTablet ? 48 : 32),
        padding: EdgeInsets.all(isTablet ? 32 : 24),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.1),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.all(isTablet ? 20 : 16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppTheme.primaryColor.withValues(alpha: 0.2),
                    AppTheme.accentColor.withValues(alpha: 0.1),
                  ],
                ),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: isTablet ? 48 : 40,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            SizedBox(height: isTablet ? 20 : 16),
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: isTablet ? 20 : 18,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: isTablet ? 12 : 8),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
                fontSize: isTablet ? 16 : 14,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: isTablet ? 24 : 20),
            Container(
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryColor.withValues(alpha: 0.3),
                    offset: const Offset(0, 4),
                    blurRadius: 12,
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(12),
                  onTap: () {
                    // Refresh shop items - using existing method
                    ref.read(shopProvider.notifier).refreshShop();
                  },
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: isTablet ? 24 : 20,
                      vertical: isTablet ? 14 : 12,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.refresh_rounded,
                          color: Colors.white,
                          size: isTablet ? 20 : 18,
                        ),
                        SizedBox(width: isTablet ? 10 : 8),
                        Text(
                          'Refresh',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: isTablet ? 16 : 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedShopItem(ShopItemModel item, bool isTablet) {
    return _EnhancedShopItemWidget(
      item: item,
      isTablet: isTablet,
      onTap: (ShopItemModel tappedItem) {
        // Enter preview mode for the selected item
        ref
            .read(shopPreviewProvider.notifier)
            .enterPreviewMode(tappedItem, tappedItem.type);
      },
    );
  }

  Widget _buildEnhancedCompanionItem(ShopItemModel item, bool isTablet) {
    return _EnhancedCompanionItemWidget(
      item: item,
      isTablet: isTablet,
      onTap: (ShopItemModel tappedItem) {
        // Enter preview mode for the selected companion
        ref
            .read(shopPreviewProvider.notifier)
            .enterPreviewMode(tappedItem, tappedItem.type);
      },
    );
  }
}

class _ShopItemWidget extends ConsumerStatefulWidget {
  final ShopItemModel item;
  final Function(ShopItemModel) onTap;

  const _ShopItemWidget({required this.item, required this.onTap});

  @override
  ConsumerState<_ShopItemWidget> createState() => _ShopItemWidgetState();
}

class _ShopItemWidgetState extends ConsumerState<_ShopItemWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print('=== BUILDING SHOP ITEM WIDGET ===');
    print('Item: ${widget.item.name} (${widget.item.id})');

    final user = ref.watch(currentUserProvider);
    final relationshipGate = ref.read(relationshipGateControllerProvider);
    final currentRelationshipLevel =
        relationshipGate.getCurrentRelationshipLevel();

    final isOwned = _isItemOwned(widget.item, user);
    final canAfford = (user?.progress.hearts ?? 0) >= widget.item.price;
    final isLocked =
        !relationshipGate.isItemUnlocked(widget.item, currentRelationshipLevel);

    print('Item state: owned=$isOwned, canAfford=$canAfford, locked=$isLocked');
    print(
      'User hearts: ${user?.progress.hearts ?? 0}, Item price: ${widget.item.price}',
    );
    print('Relationship level: $currentRelationshipLevel');
    print('================================');

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTapDown: (details) {
        print(
          '🔥 onTapDown for item: ${widget.item.name} at ${details.localPosition}',
        );
        _scaleController.forward();
      },
      onTapUp: (details) {
        print(
          '🔥 onTapUp for item: ${widget.item.name} at ${details.localPosition}',
        );
        _scaleController.reverse();
      },
      onTapCancel: () {
        print('🔥 onTapCancel for item: ${widget.item.name}');
        _scaleController.reverse();
      },
      onTap: () {
        print(
          '🔥🔥🔥 GestureDetector onTap triggered for item: ${widget.item.name}',
        );
        print('🔥🔥🔥 Calling widget.onTap with item: ${widget.item.id}');
        widget.onTap(widget.item);
        print('🔥🔥🔥 widget.onTap call completed');
      },
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: GlassmorphicContainer(
              child: Stack(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Item Image
                      Expanded(
                        flex: 3,
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(12),
                            ),
                            image:
                                widget.item.imageUrl != null
                                    ? DecorationImage(
                                      image: NetworkImage(
                                        widget.item.imageUrl!,
                                      ),
                                      fit: BoxFit.cover,
                                    )
                                    : null,
                            gradient:
                                widget.item.imageUrl == null
                                    ? _getGradientForType(widget.item.type)
                                    : null,
                          ),
                          child: Stack(
                            children: [
                              if (widget.item.imageUrl == null)
                                Center(
                                  child: Icon(
                                    _getIconForType(widget.item.type),
                                    size: 40,
                                    color: Colors.white,
                                  ),
                                ),

                              // Rarity indicator
                              Positioned(
                                top: 8,
                                left: 8,
                                child: _buildRarityBadge(widget.item.rarity),
                              ),

                              // Preview button
                              Positioned(
                                top: 8,
                                right: 8,
                                child: Container(
                                  padding: const EdgeInsets.all(6),
                                  decoration: BoxDecoration(
                                    color: Colors.black.withValues(alpha: 0.5),
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.visibility_rounded,
                                    size: 16,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Item Info
                      Expanded(
                        flex: 2,
                        child: Padding(
                          padding: const EdgeInsets.all(12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.item.name,
                                style: Theme.of(context).textTheme.titleSmall
                                    ?.copyWith(fontWeight: FontWeight.w600),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                widget.item.description,
                                style: Theme.of(
                                  context,
                                ).textTheme.bodySmall?.copyWith(
                                  color: AppTheme.textSecondaryColor,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const Spacer(),
                              _buildItemStatus(
                                widget.item,
                                isOwned,
                                canAfford,
                                isLocked,
                                currentRelationshipLevel,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),

                  // Lock overlay
                  if (isLocked)
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.6),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.lock_rounded,
                              color: AppTheme.warningColor,
                              size: 32,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Locked',
                              style: TextStyle(
                                color: AppTheme.warningColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              relationshipGate.getUnlockRequirement(
                                widget.item,
                              ),
                              style: TextStyle(
                                color: AppTheme.warningColor,
                                fontSize: 10,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ).animate().fadeIn(
              delay: Duration(milliseconds: 100 * (widget.item.hashCode % 10)),
            ),
          );
        },
      ),
    );
  }

  Widget _buildItemStatus(
    ShopItemModel item,
    bool isOwned,
    bool canAfford,
    bool isLocked,
    int relationshipLevel,
  ) {
    if (isOwned) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: AppTheme.successColor.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppTheme.successColor),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.check_rounded, size: 16, color: AppTheme.successColor),
            const SizedBox(width: 4),
            Text(
              'Owned',
              style: TextStyle(
                color: AppTheme.successColor,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ],
        ),
      );
    }

    if (isLocked) {
      final relationshipGate = ref.read(relationshipGateControllerProvider);
      final requiredLevel = relationshipGate.getRequiredRelationshipLevel(item);
      final relationshipLevelEnum = RelationshipLevel.fromLevel(requiredLevel);

      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: AppTheme.warningColor.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppTheme.warningColor),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.lock_rounded, size: 16, color: AppTheme.warningColor),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                relationshipLevelEnum.displayName,
                style: TextStyle(
                  color: AppTheme.warningColor,
                  fontWeight: FontWeight.w600,
                  fontSize: 10,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      );
    }

    return GestureDetector(
      onTap: canAfford ? () => _purchaseItem(item) : null,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          gradient: canAfford ? AppTheme.primaryGradient : null,
          color: canAfford ? null : Colors.grey.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              AppConstants.currencySymbol,
              style: TextStyle(
                color: canAfford ? Colors.white : Colors.grey,
                fontSize: 12,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              item.price.toString(),
              style: TextStyle(
                color: canAfford ? Colors.white : Colors.grey,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _purchaseItem(ShopItemModel item) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.surfaceColor,
            title: Text('Purchase ${item.name}?'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'This will cost ${item.price} ${AppConstants.currencyName}.',
                ),
                const SizedBox(height: 16),
                if (item.imageUrl != null)
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      image: DecorationImage(
                        image: NetworkImage(item.imageUrl!),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  ref.read(shopProvider.notifier).purchaseItem(item.id);
                },
                child: const Text('Purchase'),
              ),
            ],
          ),
    );
  }

  // Helper methods
  bool _isItemOwned(ShopItemModel item, user) {
    if (user == null) return false;

    switch (item.type) {
      case ShopItemType.environment:
        return user.ownedEnvironments.contains(item.id);
      case ShopItemType.outfit:
      case ShopItemType.accessory:
        return user.ownedOutfits.contains(item.id);
      default:
        return false;
    }
  }

  Widget _buildRarityBadge(ShopItemRarity rarity) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: _getRarityColor(rarity).withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        _getRarityText(rarity),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 8,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  LinearGradient _getGradientForType(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return const LinearGradient(
          colors: [Color(0xFF4ECDC4), Color(0xFF44A08D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case ShopItemType.outfit:
        return const LinearGradient(
          colors: [Color(0xFF6C63FF), Color(0xFF9C27B0)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case ShopItemType.accessory:
        return const LinearGradient(
          colors: [Color(0xFFFF6B6B), Color(0xFFFFE66D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case ShopItemType.pet:
        return const LinearGradient(
          colors: [Color(0xFF8BC34A), Color(0xFF4CAF50)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      default:
        return AppTheme.primaryGradient;
    }
  }

  IconData _getIconForType(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return Icons.landscape_rounded;
      case ShopItemType.outfit:
        return Icons.checkroom_rounded;
      case ShopItemType.accessory:
        return Icons.diamond_rounded;
      case ShopItemType.pet:
        return Icons.pets_rounded;
      default:
        return Icons.shopping_bag_rounded;
    }
  }

  Color _getRarityColor(ShopItemRarity rarity) {
    switch (rarity) {
      case ShopItemRarity.common:
        return Colors.grey;
      case ShopItemRarity.rare:
        return Colors.blue;
      case ShopItemRarity.epic:
        return Colors.purple;
      case ShopItemRarity.legendary:
        return Colors.orange;
    }
  }

  String _getRarityText(ShopItemRarity rarity) {
    switch (rarity) {
      case ShopItemRarity.common:
        return 'C';
      case ShopItemRarity.rare:
        return 'R';
      case ShopItemRarity.epic:
        return 'E';
      case ShopItemRarity.legendary:
        return 'L';
    }
  }
}

class _CompanionItemWidget extends ConsumerStatefulWidget {
  final ShopItemModel item;
  final Function(ShopItemModel) onTap;

  const _CompanionItemWidget({required this.item, required this.onTap});

  @override
  ConsumerState<_CompanionItemWidget> createState() =>
      _CompanionItemWidgetState();
}

class _CompanionItemWidgetState extends ConsumerState<_CompanionItemWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print('=== BUILDING COMPANION ITEM WIDGET ===');
    print('Companion: ${widget.item.name} (${widget.item.id})');

    final user = ref.watch(currentUserProvider);
    final relationshipGate = ref.read(relationshipGateControllerProvider);
    final currentRelationshipLevel =
        relationshipGate.getCurrentRelationshipLevel();

    final isOwned = _isCompanionOwned(widget.item, user);
    final canAfford = (user?.progress.hearts ?? 0) >= widget.item.price;
    final isLocked =
        !relationshipGate.isItemUnlocked(widget.item, currentRelationshipLevel);

    print(
      'Companion state: owned=$isOwned, canAfford=$canAfford, locked=$isLocked',
    );
    print(
      'User hearts: ${user?.progress.hearts ?? 0}, Companion price: ${widget.item.price}',
    );
    print('Relationship level: $currentRelationshipLevel');
    print('====================================');

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTapDown: (details) {
        print(
          '🔥 onTapDown for companion: ${widget.item.name} at ${details.localPosition}',
        );
        _scaleController.forward();
      },
      onTapUp: (details) {
        print(
          '🔥 onTapUp for companion: ${widget.item.name} at ${details.localPosition}',
        );
        _scaleController.reverse();
      },
      onTapCancel: () {
        print('🔥 onTapCancel for companion: ${widget.item.name}');
        _scaleController.reverse();
      },
      onTap: () {
        print(
          '🔥🔥🔥 GestureDetector onTap triggered for companion: ${widget.item.name}',
        );
        print('🔥🔥🔥 Calling widget.onTap with companion: ${widget.item.id}');
        widget.onTap(widget.item);
        print('🔥🔥🔥 widget.onTap call completed');
      },
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: GlassmorphicContainer(
              child: Stack(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Companion 3D Model Preview
                      Expanded(
                        flex: 3,
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(12),
                            ),
                            gradient: _getCompanionGradient(widget.item),
                          ),
                          child: Stack(
                            children: [
                              // 3D Model placeholder - in real implementation this would show Unity preview
                              Center(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Container(
                                      width: 80,
                                      height: 80,
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        gradient: _getPersonalityGradient(
                                          widget.item,
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: _getPersonalityColor(
                                              widget.item,
                                            ).withValues(alpha: 0.3),
                                            blurRadius: 20,
                                            spreadRadius: 2,
                                          ),
                                        ],
                                      ),
                                      child: Center(
                                        child: Text(
                                          _getCompanionAvatar(widget.item),
                                          style: const TextStyle(fontSize: 32),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: Colors.black.withValues(
                                          alpha: 0.6,
                                        ),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Text(
                                        _getPersonalityDisplayName(widget.item),
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 10,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // Rarity indicator
                              Positioned(
                                top: 8,
                                left: 8,
                                child: _buildRarityBadge(widget.item.rarity),
                              ),

                              // Preview button
                              Positioned(
                                top: 8,
                                right: 8,
                                child: Container(
                                  padding: const EdgeInsets.all(6),
                                  decoration: BoxDecoration(
                                    color: Colors.black.withValues(alpha: 0.5),
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.visibility_rounded,
                                    size: 16,
                                    color: Colors.white,
                                  ),
                                ),
                              ),

                              // Relationship level indicator
                              if (!isLocked)
                                Positioned(
                                  bottom: 8,
                                  left: 8,
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 6,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: AppTheme.accentColor.withValues(
                                        alpha: 0.8,
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const Icon(
                                          Icons.favorite_rounded,
                                          color: Colors.white,
                                          size: 10,
                                        ),
                                        const SizedBox(width: 2),
                                        Text(
                                          'Lv.${_getRequiredRelationshipLevel(widget.item)}',
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 8,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),

                      // Companion Info
                      Expanded(
                        flex: 2,
                        child: Padding(
                          padding: const EdgeInsets.all(12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.item.name,
                                style: Theme.of(context).textTheme.titleSmall
                                    ?.copyWith(fontWeight: FontWeight.bold),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                widget.item.description,
                                style: Theme.of(
                                  context,
                                ).textTheme.bodySmall?.copyWith(
                                  color: AppTheme.textSecondaryColor,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const Spacer(),
                              _buildCompanionStatus(
                                widget.item,
                                isOwned,
                                canAfford,
                                isLocked,
                                currentRelationshipLevel,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),

                  // Lock overlay
                  if (isLocked)
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.6),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.lock_rounded,
                              color: AppTheme.warningColor,
                              size: 32,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Locked',
                              style: TextStyle(
                                color: AppTheme.warningColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              relationshipGate.getUnlockRequirement(
                                widget.item,
                              ),
                              style: TextStyle(
                                color: AppTheme.warningColor,
                                fontSize: 10,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ).animate().fadeIn(
              delay: Duration(milliseconds: 100 * (widget.item.hashCode % 10)),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCompanionStatus(
    ShopItemModel item,
    bool isOwned,
    bool canAfford,
    bool isLocked,
    int relationshipLevel,
  ) {
    if (isOwned) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: AppTheme.successColor.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppTheme.successColor),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.check_rounded, size: 16, color: AppTheme.successColor),
            const SizedBox(width: 4),
            Text(
              'Unlocked',
              style: TextStyle(
                color: AppTheme.successColor,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ],
        ),
      );
    }

    if (isLocked) {
      final relationshipGate = ref.read(relationshipGateControllerProvider);
      final requiredLevel = relationshipGate.getRequiredRelationshipLevel(item);
      final relationshipLevelEnum = RelationshipLevel.fromLevel(requiredLevel);

      return Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: AppTheme.warningColor.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppTheme.warningColor),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.lock_rounded, size: 16, color: AppTheme.warningColor),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                relationshipLevelEnum.displayName,
                style: TextStyle(
                  color: AppTheme.warningColor,
                  fontWeight: FontWeight.w600,
                  fontSize: 10,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      );
    }

    return GestureDetector(
      onTap: canAfford ? () => _purchaseCompanion(item) : null,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          gradient: canAfford ? AppTheme.primaryGradient : null,
          color: canAfford ? null : Colors.grey.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              AppConstants.currencySymbol,
              style: TextStyle(
                color: canAfford ? Colors.white : Colors.grey,
                fontSize: 12,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              item.price.toString(),
              style: TextStyle(
                color: canAfford ? Colors.white : Colors.grey,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _purchaseCompanion(ShopItemModel item) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppTheme.surfaceColor,
            title: Text('Unlock ${item.name}?'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'This will cost ${item.price} ${AppConstants.currencyName}.',
                ),
                const SizedBox(height: 16),
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: _getCompanionGradient(item),
                  ),
                  child: Center(
                    child: Text(
                      _getCompanionAvatar(item),
                      style: const TextStyle(fontSize: 40),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _getPersonalityDisplayName(item),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: _getPersonalityColor(item),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  ref.read(shopProvider.notifier).purchaseItem(item.id);
                },
                child: const Text('Unlock'),
              ),
            ],
          ),
    );
  }

  // Helper methods for companions
  bool _isCompanionOwned(ShopItemModel item, user) {
    if (user == null) return false;
    // For now, check if companion is in owned outfits (simplified)
    // In a real implementation, this would check a separate companions list
    return user.ownedOutfits.contains(item.id);
  }

  LinearGradient _getCompanionGradient(ShopItemModel item) {
    return LinearGradient(
      colors: [
        _getPersonalityColor(item),
        _getPersonalityColor(item).withValues(alpha: 0.7),
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }

  LinearGradient _getPersonalityGradient(ShopItemModel item) {
    final color = _getPersonalityColor(item);
    return LinearGradient(
      colors: [color.withValues(alpha: 0.8), color.withValues(alpha: 0.6)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }

  Color _getPersonalityColor(ShopItemModel item) {
    // Extract personality from item metadata or name
    final personality = _getPersonalityFromItem(item);
    switch (personality) {
      case CompanionPersonality.caringFriend:
        return AppTheme.successColor;
      case CompanionPersonality.playfulCompanion:
        return AppTheme.warningColor;
      case CompanionPersonality.wiseMentor:
        return AppTheme.primaryColor;
      case CompanionPersonality.romanticPartner:
        return AppTheme.accentColor;
      case CompanionPersonality.supportiveTherapist:
        return Colors.teal;
    }
  }

  String _getCompanionAvatar(ShopItemModel item) {
    final personality = _getPersonalityFromItem(item);
    switch (personality) {
      case CompanionPersonality.caringFriend:
        return '🤗';
      case CompanionPersonality.playfulCompanion:
        return '🎭';
      case CompanionPersonality.wiseMentor:
        return '🧙‍♂️';
      case CompanionPersonality.romanticPartner:
        return '💕';
      case CompanionPersonality.supportiveTherapist:
        return '🩺';
    }
  }

  String _getPersonalityDisplayName(ShopItemModel item) {
    return _getPersonalityFromItem(item).displayName;
  }

  CompanionPersonality _getPersonalityFromItem(ShopItemModel item) {
    // Extract personality from item metadata
    final personalityName = item.metadata['personality'] as String?;
    if (personalityName != null) {
      return CompanionPersonality.values.firstWhere(
        (p) => p.name == personalityName,
        orElse: () => CompanionPersonality.caringFriend,
      );
    }

    // Fallback: determine from item name/id
    if (item.id.contains('playful') ||
        item.name.toLowerCase().contains('playful')) {
      return CompanionPersonality.playfulCompanion;
    } else if (item.id.contains('wise') ||
        item.name.toLowerCase().contains('sage')) {
      return CompanionPersonality.wiseMentor;
    } else if (item.id.contains('romantic') ||
        item.name.toLowerCase().contains('valentine')) {
      return CompanionPersonality.romanticPartner;
    } else if (item.id.contains('therapist') ||
        item.name.toLowerCase().contains('dr')) {
      return CompanionPersonality.supportiveTherapist;
    }

    return CompanionPersonality.caringFriend;
  }

  int _getRequiredRelationshipLevel(ShopItemModel item) {
    return item.metadata['relationshipRequired'] as int? ?? 1;
  }

  Widget _buildRarityBadge(ShopItemRarity rarity) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: _getRarityColor(rarity).withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        _getRarityText(rarity),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 8,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Color _getRarityColor(ShopItemRarity rarity) {
    switch (rarity) {
      case ShopItemRarity.common:
        return Colors.grey;
      case ShopItemRarity.rare:
        return Colors.blue;
      case ShopItemRarity.epic:
        return Colors.purple;
      case ShopItemRarity.legendary:
        return Colors.orange;
    }
  }

  String _getRarityText(ShopItemRarity rarity) {
    switch (rarity) {
      case ShopItemRarity.common:
        return 'C';
      case ShopItemRarity.rare:
        return 'R';
      case ShopItemRarity.epic:
        return 'E';
      case ShopItemRarity.legendary:
        return 'L';
    }
  }
}

// Enhanced Shop Item Widget with improved design
class _EnhancedShopItemWidget extends ConsumerStatefulWidget {
  final ShopItemModel item;
  final bool isTablet;
  final Function(ShopItemModel) onTap;

  const _EnhancedShopItemWidget({
    required this.item,
    required this.isTablet,
    required this.onTap,
  });

  @override
  ConsumerState<_EnhancedShopItemWidget> createState() => _EnhancedShopItemWidgetState();
}

class _EnhancedShopItemWidgetState extends ConsumerState<_EnhancedShopItemWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(currentUserProvider);
    final relationshipGate = ref.read(relationshipGateControllerProvider);
    final currentRelationshipLevel = relationshipGate.getCurrentRelationshipLevel();

    final isOwned = _isItemOwned(widget.item, user);
    final canAfford = (user?.progress.hearts ?? 0) >= widget.item.price;
    final isLocked = !relationshipGate.isItemUnlocked(widget.item, currentRelationshipLevel);

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTapDown: (_) => _scaleController.forward(),
      onTapUp: (_) => _scaleController.reverse(),
      onTapCancel: () => _scaleController.reverse(),
      onTap: () => widget.onTap(widget.item),
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withValues(alpha: 0.1),
                    Colors.white.withValues(alpha: 0.05),
                  ],
                ),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.2),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    offset: const Offset(0, 8),
                    blurRadius: 24,
                  ),
                  if (!isLocked)
                    BoxShadow(
                      color: _getItemTypeColor(widget.item.type).withValues(alpha: 0.1),
                      offset: const Offset(0, 0),
                      blurRadius: 16,
                    ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Stack(
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Enhanced Item Image
                          Expanded(
                            flex: 3,
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: const BorderRadius.vertical(
                                  top: Radius.circular(20),
                                ),
                                gradient: widget.item.imageUrl == null
                                    ? _getGradientForType(widget.item.type)
                                    : null,
                              ),
                              child: Stack(
                                children: [
                                  if (widget.item.imageUrl != null)
                                    Positioned.fill(
                                      child: Image.network(
                                        widget.item.imageUrl!,
                                        fit: BoxFit.cover,
                                        errorBuilder: (context, error, stackTrace) =>
                                            _buildFallbackIcon(),
                                      ),
                                    )
                                  else
                                    _buildFallbackIcon(),

                                  // Enhanced Rarity Badge
                                  Positioned(
                                    top: widget.isTablet ? 12 : 8,
                                    left: widget.isTablet ? 12 : 8,
                                    child: _buildEnhancedRarityBadge(widget.item.rarity),
                                  ),

                                  // Enhanced Preview Button
                                  Positioned(
                                    top: widget.isTablet ? 12 : 8,
                                    right: widget.isTablet ? 12 : 8,
                                    child: Container(
                                      padding: EdgeInsets.all(widget.isTablet ? 8 : 6),
                                      decoration: BoxDecoration(
                                        color: Colors.black.withValues(alpha: 0.6),
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: Colors.white.withValues(alpha: 0.3),
                                          width: 1,
                                        ),
                                      ),
                                      child: Icon(
                                        Icons.visibility_rounded,
                                        size: widget.isTablet ? 18 : 16,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          // Enhanced Item Info
                          Flexible(
                            flex: 2,
                            child: Container(
                              padding: EdgeInsets.all(widget.isTablet ? 14 : 10),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.white.withValues(alpha: 0.05),
                                    Colors.white.withValues(alpha: 0.02),
                                  ],
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    widget.item.name,
                                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                      fontSize: widget.isTablet ? 15 : 13,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  SizedBox(height: widget.isTablet ? 4 : 2),
                                  Flexible(
                                    child: Text(
                                      widget.item.description,
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                        color: AppTheme.textSecondaryColor,
                                        fontSize: widget.isTablet ? 12 : 10,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  SizedBox(height: widget.isTablet ? 8 : 6),
                                  _buildEnhancedItemStatus(
                                    widget.item,
                                    isOwned,
                                    canAfford,
                                    isLocked,
                                    currentRelationshipLevel,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),

                      // Enhanced Lock Overlay
                      if (isLocked)
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.7),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  padding: EdgeInsets.all(widget.isTablet ? 16 : 12),
                                  decoration: BoxDecoration(
                                    color: AppTheme.warningColor.withValues(alpha: 0.2),
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: AppTheme.warningColor.withValues(alpha: 0.5),
                                      width: 2,
                                    ),
                                  ),
                                  child: Icon(
                                    Icons.lock_rounded,
                                    color: AppTheme.warningColor,
                                    size: widget.isTablet ? 28 : 24,
                                  ),
                                ),
                                SizedBox(height: widget.isTablet ? 12 : 8),
                                Text(
                                  'Locked',
                                  style: TextStyle(
                                    color: AppTheme.warningColor,
                                    fontWeight: FontWeight.w600,
                                    fontSize: widget.isTablet ? 16 : 14,
                                  ),
                                ),
                                SizedBox(height: widget.isTablet ? 6 : 4),
                                Padding(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: widget.isTablet ? 16 : 12,
                                  ),
                                  child: Text(
                                    relationshipGate.getUnlockRequirement(widget.item),
                                    style: TextStyle(
                                      color: AppTheme.warningColor.withValues(alpha: 0.8),
                                      fontSize: widget.isTablet ? 12 : 10,
                                    ),
                                    textAlign: TextAlign.center,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ).animate().fadeIn(
              delay: Duration(milliseconds: 100 * (widget.item.hashCode % 10)),
            ),
          );
        },
      ),
    );
  }

  // Helper methods for enhanced shop item
  bool _isItemOwned(ShopItemModel item, user) {
    if (user == null) return false;
    switch (item.type) {
      case ShopItemType.environment:
        return user.ownedEnvironments.contains(item.id);
      case ShopItemType.outfit:
      case ShopItemType.accessory:
        return user.ownedOutfits.contains(item.id);
      default:
        return false;
    }
  }

  Color _getItemTypeColor(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return const Color(0xFF4ECDC4);
      case ShopItemType.outfit:
        return const Color(0xFF6C63FF);
      case ShopItemType.accessory:
        return const Color(0xFFFF6B6B);
      case ShopItemType.pet:
        return const Color(0xFF8BC34A);
      default:
        return AppTheme.primaryColor;
    }
  }

  LinearGradient _getGradientForType(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return const LinearGradient(
          colors: [Color(0xFF4ECDC4), Color(0xFF44A08D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case ShopItemType.outfit:
        return const LinearGradient(
          colors: [Color(0xFF6C63FF), Color(0xFF9C27B0)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case ShopItemType.accessory:
        return const LinearGradient(
          colors: [Color(0xFFFF6B6B), Color(0xFFFFE66D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case ShopItemType.pet:
        return const LinearGradient(
          colors: [Color(0xFF8BC34A), Color(0xFF4CAF50)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      default:
        return AppTheme.primaryGradient;
    }
  }

  Widget _buildFallbackIcon() {
    return Center(
      child: Container(
        padding: EdgeInsets.all(widget.isTablet ? 20 : 16),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.1),
          shape: BoxShape.circle,
        ),
        child: Icon(
          _getIconForType(widget.item.type),
          size: widget.isTablet ? 32 : 28,
          color: Colors.white,
        ),
      ),
    );
  }

  IconData _getIconForType(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return Icons.landscape_rounded;
      case ShopItemType.outfit:
        return Icons.checkroom_rounded;
      case ShopItemType.accessory:
        return Icons.diamond_rounded;
      case ShopItemType.pet:
        return Icons.pets_rounded;
      default:
        return Icons.shopping_bag_rounded;
    }
  }

  Widget _buildEnhancedRarityBadge(ShopItemRarity rarity) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: widget.isTablet ? 10 : 8,
        vertical: widget.isTablet ? 6 : 4,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _getRarityColor(rarity),
            _getRarityColor(rarity).withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: _getRarityColor(rarity).withValues(alpha: 0.3),
            offset: const Offset(0, 2),
            blurRadius: 8,
          ),
        ],
      ),
      child: Text(
        _getRarityText(rarity),
        style: TextStyle(
          color: Colors.white,
          fontSize: widget.isTablet ? 10 : 8,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Color _getRarityColor(ShopItemRarity rarity) {
    switch (rarity) {
      case ShopItemRarity.common:
        return Colors.grey;
      case ShopItemRarity.rare:
        return Colors.blue;
      case ShopItemRarity.epic:
        return Colors.purple;
      case ShopItemRarity.legendary:
        return Colors.orange;
    }
  }

  String _getRarityText(ShopItemRarity rarity) {
    switch (rarity) {
      case ShopItemRarity.common:
        return 'COMMON';
      case ShopItemRarity.rare:
        return 'RARE';
      case ShopItemRarity.epic:
        return 'EPIC';
      case ShopItemRarity.legendary:
        return 'LEGENDARY';
    }
  }

  Widget _buildEnhancedItemStatus(
    ShopItemModel item,
    bool isOwned,
    bool canAfford,
    bool isLocked,
    int relationshipLevel,
  ) {
    if (isOwned) {
      return Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(
          vertical: widget.isTablet ? 10 : 8,
          horizontal: widget.isTablet ? 12 : 8,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppTheme.successColor.withValues(alpha: 0.3),
              AppTheme.successColor.withValues(alpha: 0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppTheme.successColor.withValues(alpha: 0.5),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_rounded,
              size: widget.isTablet ? 18 : 16,
              color: AppTheme.successColor,
            ),
            SizedBox(width: widget.isTablet ? 6 : 4),
            Text(
              'Owned',
              style: TextStyle(
                color: AppTheme.successColor,
                fontWeight: FontWeight.w600,
                fontSize: widget.isTablet ? 14 : 12,
              ),
            ),
          ],
        ),
      );
    }

    if (isLocked) {
      return Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(
          vertical: widget.isTablet ? 10 : 8,
          horizontal: widget.isTablet ? 12 : 8,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppTheme.warningColor.withValues(alpha: 0.3),
              AppTheme.warningColor.withValues(alpha: 0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppTheme.warningColor.withValues(alpha: 0.5),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lock_rounded,
              size: widget.isTablet ? 18 : 16,
              color: AppTheme.warningColor,
            ),
            SizedBox(width: widget.isTablet ? 6 : 4),
            Expanded(
              child: Text(
                'Locked',
                style: TextStyle(
                  color: AppTheme.warningColor,
                  fontWeight: FontWeight.w600,
                  fontSize: widget.isTablet ? 14 : 12,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: canAfford ? AppTheme.primaryGradient : null,
        color: canAfford ? null : Colors.grey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: canAfford ? null : Border.all(
          color: Colors.grey.withValues(alpha: 0.5),
          width: 1,
        ),
        boxShadow: canAfford ? [
          BoxShadow(
            color: AppTheme.primaryColor.withValues(alpha: 0.3),
            offset: const Offset(0, 2),
            blurRadius: 8,
          ),
        ] : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: canAfford ? () => _purchaseItem(item) : null,
          child: Padding(
            padding: EdgeInsets.symmetric(
              vertical: widget.isTablet ? 10 : 8,
              horizontal: widget.isTablet ? 12 : 8,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.favorite_rounded,
                  color: canAfford ? Colors.white : Colors.grey,
                  size: widget.isTablet ? 16 : 14,
                ),
                SizedBox(width: widget.isTablet ? 6 : 4),
                Text(
                  item.price.toString(),
                  style: TextStyle(
                    color: canAfford ? Colors.white : Colors.grey,
                    fontWeight: FontWeight.w600,
                    fontSize: widget.isTablet ? 14 : 12,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _purchaseItem(ShopItemModel item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.surfaceColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Text('Purchase ${item.name}?'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'This will cost ${item.price} hearts.',
            ),
            const SizedBox(height: 16),
            if (item.imageUrl != null)
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  image: DecorationImage(
                    image: NetworkImage(item.imageUrl!),
                    fit: BoxFit.cover,
                  ),
                ),
              )
            else
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: _getGradientForType(item.type),
                ),
                child: Icon(
                  _getIconForType(item.type),
                  size: 40,
                  color: Colors.white,
                ),
              ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(shopProvider.notifier).purchaseItem(item.id);
            },
            child: const Text('Purchase'),
          ),
        ],
      ),
    );
  }
}

// Enhanced Companion Item Widget
class _EnhancedCompanionItemWidget extends ConsumerStatefulWidget {
  final ShopItemModel item;
  final bool isTablet;
  final Function(ShopItemModel) onTap;

  const _EnhancedCompanionItemWidget({
    required this.item,
    required this.isTablet,
    required this.onTap,
  });

  @override
  ConsumerState<_EnhancedCompanionItemWidget> createState() => _EnhancedCompanionItemWidgetState();
}

class _EnhancedCompanionItemWidgetState extends ConsumerState<_EnhancedCompanionItemWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(currentUserProvider);
    final relationshipGate = ref.read(relationshipGateControllerProvider);
    final currentRelationshipLevel = relationshipGate.getCurrentRelationshipLevel();

    final canAfford = (user?.progress.hearts ?? 0) >= widget.item.price;
    final isLocked = !relationshipGate.isItemUnlocked(widget.item, currentRelationshipLevel);

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTapDown: (_) => _scaleController.forward(),
      onTapUp: (_) => _scaleController.reverse(),
      onTapCancel: () => _scaleController.reverse(),
      onTap: () => widget.onTap(widget.item),
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withValues(alpha: 0.1),
                    Colors.white.withValues(alpha: 0.05),
                  ],
                ),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.2),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    offset: const Offset(0, 8),
                    blurRadius: 24,
                  ),
                  if (!isLocked)
                    BoxShadow(
                      color: AppTheme.accentColor.withValues(alpha: 0.2),
                      offset: const Offset(0, 0),
                      blurRadius: 16,
                    ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(20),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Stack(
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          // Companion Avatar
                          Expanded(
                            flex: 2,
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: const BorderRadius.vertical(
                                  top: Radius.circular(20),
                                ),
                                gradient: LinearGradient(
                                  colors: [
                                    AppTheme.accentColor.withValues(alpha: 0.3),
                                    AppTheme.primaryColor.withValues(alpha: 0.2),
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                              ),
                              child: Stack(
                                children: [
                                  if (widget.item.imageUrl != null)
                                    Positioned.fill(
                                      child: Image.network(
                                        widget.item.imageUrl!,
                                        fit: BoxFit.cover,
                                        errorBuilder: (context, error, stackTrace) =>
                                            _buildCompanionFallback(),
                                      ),
                                    )
                                  else
                                    _buildCompanionFallback(),

                                  // Companion Type Badge
                                  Positioned(
                                    top: widget.isTablet ? 12 : 8,
                                    left: widget.isTablet ? 12 : 8,
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: widget.isTablet ? 10 : 8,
                                        vertical: widget.isTablet ? 6 : 4,
                                      ),
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: [
                                            AppTheme.accentColor,
                                            AppTheme.accentColor.withValues(alpha: 0.8),
                                          ],
                                        ),
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(
                                          color: Colors.white.withValues(alpha: 0.3),
                                          width: 1,
                                        ),
                                      ),
                                      child: Text(
                                        'AI',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontSize: widget.isTablet ? 10 : 8,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),

                                  // Preview Button
                                  Positioned(
                                    top: widget.isTablet ? 12 : 8,
                                    right: widget.isTablet ? 12 : 8,
                                    child: Container(
                                      padding: EdgeInsets.all(widget.isTablet ? 8 : 6),
                                      decoration: BoxDecoration(
                                        color: Colors.black.withValues(alpha: 0.6),
                                        shape: BoxShape.circle,
                                        border: Border.all(
                                          color: Colors.white.withValues(alpha: 0.3),
                                          width: 1,
                                        ),
                                      ),
                                      child: Icon(
                                        Icons.chat_rounded,
                                        size: widget.isTablet ? 18 : 16,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          // Companion Info
                          Expanded(
                            flex: 2,
                            child: Container(
                              padding: EdgeInsets.all(widget.isTablet ? 16 : 12),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    Colors.white.withValues(alpha: 0.05),
                                    Colors.white.withValues(alpha: 0.02),
                                  ],
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    widget.item.name,
                                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: Colors.white,
                                      fontSize: widget.isTablet ? 16 : 14,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  SizedBox(height: widget.isTablet ? 6 : 4),
                                  Text(
                                    widget.item.description,
                                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: AppTheme.textSecondaryColor,
                                      fontSize: widget.isTablet ? 13 : 11,
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const Spacer(),
                                  _buildCompanionStatus(canAfford, isLocked),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),

                      // Lock Overlay
                      if (isLocked)
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.7),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  padding: EdgeInsets.all(widget.isTablet ? 16 : 12),
                                  decoration: BoxDecoration(
                                    color: AppTheme.warningColor.withValues(alpha: 0.2),
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: AppTheme.warningColor.withValues(alpha: 0.5),
                                      width: 2,
                                    ),
                                  ),
                                  child: Icon(
                                    Icons.lock_rounded,
                                    color: AppTheme.warningColor,
                                    size: widget.isTablet ? 28 : 24,
                                  ),
                                ),
                                SizedBox(height: widget.isTablet ? 12 : 8),
                                Text(
                                  'Locked',
                                  style: TextStyle(
                                    color: AppTheme.warningColor,
                                    fontWeight: FontWeight.w600,
                                    fontSize: widget.isTablet ? 16 : 14,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ).animate().fadeIn(
              delay: Duration(milliseconds: 100 * (widget.item.hashCode % 10)),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCompanionFallback() {
    return Center(
      child: Container(
        padding: EdgeInsets.all(widget.isTablet ? 24 : 20),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.1),
          shape: BoxShape.circle,
        ),
        child: Icon(
          Icons.person_rounded,
          size: widget.isTablet ? 40 : 32,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildCompanionStatus(bool canAfford, bool isLocked) {
    if (isLocked) {
      return Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(
          vertical: widget.isTablet ? 10 : 8,
          horizontal: widget.isTablet ? 12 : 8,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppTheme.warningColor.withValues(alpha: 0.3),
              AppTheme.warningColor.withValues(alpha: 0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppTheme.warningColor.withValues(alpha: 0.5),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lock_rounded,
              size: widget.isTablet ? 18 : 16,
              color: AppTheme.warningColor,
            ),
            SizedBox(width: widget.isTablet ? 6 : 4),
            Text(
              'Locked',
              style: TextStyle(
                color: AppTheme.warningColor,
                fontWeight: FontWeight.w600,
                fontSize: widget.isTablet ? 14 : 12,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: canAfford ? AppTheme.primaryGradient : null,
        color: canAfford ? null : Colors.grey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: canAfford ? null : Border.all(
          color: Colors.grey.withValues(alpha: 0.5),
          width: 1,
        ),
        boxShadow: canAfford ? [
          BoxShadow(
            color: AppTheme.primaryColor.withValues(alpha: 0.3),
            offset: const Offset(0, 2),
            blurRadius: 8,
          ),
        ] : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: canAfford ? () => _purchaseCompanion() : null,
          child: Padding(
            padding: EdgeInsets.symmetric(
              vertical: widget.isTablet ? 10 : 8,
              horizontal: widget.isTablet ? 12 : 8,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.favorite_rounded,
                  color: canAfford ? Colors.white : Colors.grey,
                  size: widget.isTablet ? 16 : 14,
                ),
                SizedBox(width: widget.isTablet ? 6 : 4),
                Text(
                  widget.item.price.toString(),
                  style: TextStyle(
                    color: canAfford ? Colors.white : Colors.grey,
                    fontWeight: FontWeight.w600,
                    fontSize: widget.isTablet ? 14 : 12,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _purchaseCompanion() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.surfaceColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Text('Unlock ${widget.item.name}?'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'This will cost ${widget.item.price} hearts.',
            ),
            const SizedBox(height: 16),
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  colors: [
                    AppTheme.accentColor.withValues(alpha: 0.3),
                    AppTheme.primaryColor.withValues(alpha: 0.2),
                  ],
                ),
              ),
              child: const Icon(
                Icons.person_rounded,
                size: 40,
                color: Colors.white,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(shopProvider.notifier).purchaseItem(widget.item.id);
            },
            child: const Text('Unlock'),
          ),
        ],
      ),
    );
  }
}
