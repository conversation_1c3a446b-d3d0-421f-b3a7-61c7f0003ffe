import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import '../../models/shop/shop_item_model.dart';
import '../../providers/auth_provider.dart';
import '../../services/relationship/relationship_gate_controller.dart';
import '../common/glassmorphic_container.dart';

class PurchaseConfirmationDialog extends ConsumerStatefulWidget {
  final ShopItemModel item;
  final VoidCallback onConfirm;
  final VoidCallback onCancel;

  const PurchaseConfirmationDialog({
    super.key,
    required this.item,
    required this.onConfirm,
    required this.onCancel,
  });

  @override
  ConsumerState<PurchaseConfirmationDialog> createState() => _PurchaseConfirmationDialogState();
}

class _PurchaseConfirmationDialogState extends ConsumerState<PurchaseConfirmationDialog>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _pulseController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
    
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _scaleController.forward();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(currentUserProvider);
    final relationshipGate = ref.read(relationshipGateControllerProvider);
    
    final canAfford = (user?.progress.hearts ?? 0) >= widget.item.price;
    final isLocked = !relationshipGate.isItemUnlocked(widget.item, relationshipGate.getCurrentRelationshipLevel());
    final heartsAfterPurchase = (user?.progress.hearts ?? 0) - widget.item.price;

    return Dialog(
      backgroundColor: Colors.transparent,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: GlassmorphicContainer(
          width: 320,
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Item preview
              _buildItemPreview(),
              
              const SizedBox(height: 20),
              
              // Purchase details
              _buildPurchaseDetails(canAfford, heartsAfterPurchase),
              
              const SizedBox(height: 24),
              
              // Action buttons
              _buildActionButtons(canAfford, isLocked),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildItemPreview() {
    return Column(
      children: [
        // Item image
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _pulseAnimation.value,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: _getRarityColor(widget.item.rarity),
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: _getRarityColor(widget.item.rarity).withValues(alpha: 0.3),
                      blurRadius: 20,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(14),
                  child: widget.item.imageUrl != null
                      ? Image.network(
                          widget.item.imageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => _buildItemIcon(),
                        )
                      : _buildItemIcon(),
                ),
              ),
            );
          },
        ),
        
        const SizedBox(height: 16),
        
        // Item name and rarity
        Text(
          widget.item.name,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 8),
        
        _buildRarityBadge(),
      ],
    );
  }

  Widget _buildItemIcon() {
    return Container(
      decoration: BoxDecoration(
        gradient: _getGradientForType(widget.item.type),
      ),
      child: Center(
        child: Icon(
          _getIconForType(widget.item.type),
          size: 48,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildRarityBadge() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getRarityColor(widget.item.rarity).withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getRarityColor(widget.item.rarity),
          width: 1,
        ),
      ),
      child: Text(
        widget.item.rarity.name.toUpperCase(),
        style: TextStyle(
          color: _getRarityColor(widget.item.rarity),
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildPurchaseDetails(bool canAfford, int heartsAfterPurchase) {
    return Column(
      children: [
        // Price display
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Price: ',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
            Text(
              AppConstants.currencySymbol,
              style: TextStyle(
                color: canAfford ? AppTheme.accentColor : AppTheme.errorColor,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              widget.item.price.toString(),
              style: TextStyle(
                color: canAfford ? Colors.white : AppTheme.errorColor,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // Hearts after purchase
        if (canAfford)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'After purchase: ',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
              ),
              Text(
                AppConstants.currencySymbol,
                style: const TextStyle(
                  color: AppTheme.accentColor,
                  fontSize: 14,
                ),
              ),
              const SizedBox(width: 2),
              Text(
                heartsAfterPurchase.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        
        // Insufficient funds warning
        if (!canAfford)
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.errorColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppTheme.errorColor),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.warning_rounded,
                  color: AppTheme.errorColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Insufficient hearts to purchase this item',
                    style: TextStyle(
                      color: AppTheme.errorColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildActionButtons(bool canAfford, bool isLocked) {
    return Row(
      children: [
        // Cancel button
        Expanded(
          child: TextButton(
            onPressed: widget.onCancel,
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
                side: BorderSide(
                  color: AppTheme.textSecondaryColor,
                  width: 1,
                ),
              ),
            ),
            child: Text(
              'Cancel',
              style: TextStyle(
                color: AppTheme.textSecondaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        
        const SizedBox(width: 12),
        
        // Purchase button
        Expanded(
          child: ElevatedButton(
            onPressed: (canAfford && !isLocked) ? widget.onConfirm : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: canAfford ? AppTheme.successColor : AppTheme.textSecondaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  canAfford ? Icons.shopping_cart_rounded : Icons.block_rounded,
                  size: 18,
                ),
                const SizedBox(width: 6),
                Text(
                  canAfford ? 'Purchase' : 'Cannot Buy',
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Helper methods
  LinearGradient _getGradientForType(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return const LinearGradient(
          colors: [Color(0xFF4ECDC4), Color(0xFF44A08D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case ShopItemType.outfit:
        return const LinearGradient(
          colors: [Color(0xFF6C63FF), Color(0xFF9C27B0)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case ShopItemType.accessory:
        return const LinearGradient(
          colors: [Color(0xFFFF6B6B), Color(0xFFFFE66D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      default:
        return AppTheme.primaryGradient;
    }
  }

  IconData _getIconForType(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return Icons.landscape_rounded;
      case ShopItemType.outfit:
        return Icons.checkroom_rounded;
      case ShopItemType.accessory:
        return Icons.diamond_rounded;
      default:
        return Icons.shopping_bag_rounded;
    }
  }

  Color _getRarityColor(ShopItemRarity rarity) {
    switch (rarity) {
      case ShopItemRarity.common:
        return Colors.grey;
      case ShopItemRarity.rare:
        return Colors.blue;
      case ShopItemRarity.epic:
        return Colors.purple;
      case ShopItemRarity.legendary:
        return Colors.orange;
    }
  }
}

// Helper function to show purchase confirmation dialog
Future<bool?> showPurchaseConfirmationDialog(
  BuildContext context,
  ShopItemModel item,
) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: false,
    builder: (context) => PurchaseConfirmationDialog(
      item: item,
      onConfirm: () => Navigator.of(context).pop(true),
      onCancel: () => Navigator.of(context).pop(false),
    ),
  );
}