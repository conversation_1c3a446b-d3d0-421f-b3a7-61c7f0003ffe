import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import '../../models/shop/shop_item_model.dart';
import '../../models/shop/shop_preview_models.dart';
import '../../providers/shop_preview_provider.dart';
import '../../providers/auth_provider.dart';
import '../../services/relationship/relationship_gate_controller.dart';
import '../common/glassmorphic_container.dart';
import 'horizontal_item_selector.dart';
import 'purchase_confirmation_dialog.dart';
import '../navigation/main_navigation.dart';

class PreviewModeOverlay extends ConsumerStatefulWidget {
  const PreviewModeOverlay({super.key});

  @override
  ConsumerState<PreviewModeOverlay> createState() => _PreviewModeOverlayState();
}

// Keyboard-style overlay that takes up space instead of overlaying
class PreviewModeKeyboardOverlay extends ConsumerStatefulWidget {
  const PreviewModeKeyboardOverlay({super.key});

  @override
  ConsumerState<PreviewModeKeyboardOverlay> createState() => _PreviewModeKeyboardOverlayState();
}

class _PreviewModeOverlayState extends ConsumerState<PreviewModeOverlay>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    // Start animations
    _slideController.forward();
    _fadeController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final previewState = ref.watch(shopPreviewProvider);
    print(
      'Preview overlay build: isInPreviewMode = ${previewState.isInPreviewMode}',
    );

    if (!previewState.isInPreviewMode) {
      print('Not in preview mode, returning empty widget');
      return const SizedBox.shrink();
    }

    print('Building preview overlay UI');

    return WillPopScope(
      onWillPop: () async {
        print('🔙 Back button pressed in preview mode');
        _exitPreviewMode();
        return false; // Prevent default back navigation
      },
      child: Positioned.fill(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: GestureDetector(
            onTap: () {
              // Tap outside to close (optional)
              print('🔙 Tapped outside preview overlay');
              // Uncomment if you want tap-outside-to-close behavior
              // _exitPreviewMode();
            },
            child: Container(
              width: double.infinity,
              height: double.infinity,
              child: Stack(
                children: [
                  // Top controls
                  _buildTopControls(previewState),

                  // Bottom preview panel
                  SlideTransition(
                    position: _slideAnimation,
                    child: _buildBottomPanel(previewState),
                  ),

                  // Companion reaction overlay
                  if (previewState.currentReaction != null)
                    _buildReactionOverlay(previewState.currentReaction!),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTopControls(ShopPreviewState previewState) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Exit button
              GlassmorphicButton(
                onPressed: _exitPreviewMode,
                padding: const EdgeInsets.all(12),
                child: const Icon(
                  Icons.close_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),

              const Spacer(),

              // Category switcher
              if (previewState.currentCategory != null)
                _buildCategorySwitcher(previewState.currentCategory!),

              const Spacer(),

              // Hearts display
              _buildHeartsDisplay(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategorySwitcher(ShopItemType currentCategory) {
    return GlassmorphicContainer(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getIconForCategory(currentCategory),
            color: AppTheme.primaryColor,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            _getCategoryDisplayName(currentCategory),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeartsDisplay() {
    final user = ref.watch(currentUserProvider);
    final hearts = user?.progress.hearts ?? 0;

    return GlassmorphicContainer(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            AppConstants.currencySymbol,
            style: const TextStyle(color: AppTheme.accentColor, fontSize: 16),
          ),
          const SizedBox(width: 4),
          Text(
            hearts.toString(),
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomPanel(ShopPreviewState previewState) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: GlassmorphicBottomSheet(
        height: 380,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Selected item info
            if (previewState.selectedItem != null) ...[
              _buildSelectedItemInfo(previewState.selectedItem!),
              const SizedBox(height: 12),
            ],

            // Item selector
            Text(
              'Choose ${_getCategoryDisplayName(previewState.currentCategory ?? ShopItemType.outfit)}',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),

            const SizedBox(height: 8),

            // Horizontal item selector
            HorizontalItemSelector(
              items: previewState.availableItems,
              lockedItems: previewState.lockedItems,
              selectedItem: previewState.selectedItem,
              onItemSelected: _onItemSelected,
            ),

            const SizedBox(height: 12),

            // Action buttons
            _buildActionButtons(previewState),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedItemInfo(ShopItemModel item) {
    final relationshipGate = ref.read(relationshipGateControllerProvider);
    final isLocked =
        !relationshipGate.isItemUnlocked(
          item,
          relationshipGate.getCurrentRelationshipLevel(),
        );

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Item image/icon
          Container(
            width: 45,
            height: 45,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child:
                  item.imageUrl != null
                      ? Image.network(
                        item.imageUrl!,
                        fit: BoxFit.cover,
                        errorBuilder:
                            (context, error, stackTrace) =>
                                _buildItemIcon(item),
                      )
                      : _buildItemIcon(item),
            ),
          ),

          const SizedBox(width: 12),

          // Item details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        item.name,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(width: 8),
                    _buildRarityBadge(item.rarity),
                  ],
                ),

                const SizedBox(height: 2),

                Text(
                  item.description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textSecondaryColor,
                    fontSize: 11,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 4),

                if (isLocked) _buildLockInfo(item) else _buildPriceInfo(item),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemIcon(ShopItemModel item) {
    return Container(
      decoration: BoxDecoration(gradient: _getGradientForType(item.type)),
      child: Center(
        child: Icon(
          _getIconForCategory(item.type),
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildRarityBadge(ShopItemRarity rarity) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: _getRarityColor(rarity).withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: _getRarityColor(rarity), width: 1),
      ),
      child: Text(
        rarity.name.substring(0, 1).toUpperCase(),
        style: TextStyle(
          color: _getRarityColor(rarity),
          fontSize: 9,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildLockInfo(ShopItemModel item) {
    final relationshipGate = ref.read(relationshipGateControllerProvider);
    final requiredLevel = relationshipGate.getRequiredRelationshipLevel(item);
    final relationshipLevel = RelationshipLevel.fromLevel(requiredLevel);

    return Row(
      children: [
        Icon(Icons.lock_rounded, size: 12, color: AppTheme.warningColor),
        const SizedBox(width: 4),
        Expanded(
          child: Text(
            'Requires ${relationshipLevel.displayName}',
            style: TextStyle(
              color: AppTheme.warningColor,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _buildPriceInfo(ShopItemModel item) {
    final user = ref.watch(currentUserProvider);
    final canAfford = (user?.progress.hearts ?? 0) >= item.price;

    return Row(
      children: [
        Text(
          AppConstants.currencySymbol,
          style: TextStyle(
            color:
                canAfford ? AppTheme.accentColor : AppTheme.textSecondaryColor,
            fontSize: 12,
          ),
        ),
        const SizedBox(width: 2),
        Text(
          item.price.toString(),
          style: TextStyle(
            color: canAfford ? Colors.white : AppTheme.textSecondaryColor,
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
        if (!canAfford) ...[
          const SizedBox(width: 6),
          Expanded(
            child: Text(
              'Insufficient hearts',
              style: TextStyle(color: AppTheme.errorColor, fontSize: 10),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildActionButtons(ShopPreviewState previewState) {
    final selectedItem = previewState.selectedItem;
    if (selectedItem == null) return const SizedBox.shrink();

    final user = ref.watch(currentUserProvider);
    final relationshipGate = ref.read(relationshipGateControllerProvider);
    final isLocked =
        !relationshipGate.isItemUnlocked(
          selectedItem,
          relationshipGate.getCurrentRelationshipLevel(),
        );
    final canAfford = (user?.progress.hearts ?? 0) >= selectedItem.price;
    final isOwned = _isItemOwned(selectedItem, user);

    return Row(
      children: [
        // Try On / Preview button
        Expanded(
          child: GlassmorphicButton(
            onPressed: isLocked ? null : () => _tryOnItem(selectedItem),
            backgroundColor: AppTheme.primaryColor,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.visibility_rounded,
                  color: isLocked ? AppTheme.textSecondaryColor : Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  isLocked ? 'Locked' : 'Try On',
                  style: TextStyle(
                    color:
                        isLocked ? AppTheme.textSecondaryColor : Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),

        const SizedBox(width: 12),

        // Purchase button
        Expanded(
          child: GlassmorphicButton(
            onPressed:
                (!isLocked && canAfford && !isOwned)
                    ? () => _purchaseItem(selectedItem)
                    : null,
            backgroundColor: AppTheme.successColor,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  isOwned ? Icons.check_rounded : Icons.shopping_cart_rounded,
                  color:
                      (!isLocked && canAfford && !isOwned)
                          ? Colors.white
                          : AppTheme.textSecondaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  isOwned ? 'Owned' : 'Buy',
                  style: TextStyle(
                    color:
                        (!isLocked && canAfford && !isOwned)
                            ? Colors.white
                            : AppTheme.textSecondaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildReactionOverlay(CompanionReaction reaction) {
    return Positioned(
      top: MediaQuery.of(context).size.height * 0.3,
      left: 16,
      right: 16,
      child: GlassmorphicContainer(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.psychology_rounded,
                    color: Colors.white,
                    size: 20,
                  ),
                ),

                const SizedBox(width: 12),

                Expanded(
                  child: Text(
                    reaction.text,
                    style: Theme.of(
                      context,
                    ).textTheme.bodyMedium?.copyWith(color: Colors.white),
                  ),
                ),
              ],
            ),
          )
          .animate()
          .fadeIn(duration: 300.ms)
          .slideY(begin: -0.2, end: 0)
          .then(delay: reaction.duration)
          .fadeOut(duration: 300.ms),
    );
  }

  // Helper methods
  IconData _getIconForCategory(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return Icons.landscape_rounded;
      case ShopItemType.outfit:
        return Icons.checkroom_rounded;
      case ShopItemType.accessory:
        return Icons.diamond_rounded;
      case ShopItemType.companion:
        return Icons.people_rounded;
      case ShopItemType.pet:
        return Icons.pets_rounded;
      default:
        return Icons.shopping_bag_rounded;
    }
  }

  String _getCategoryDisplayName(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return 'Environments';
      case ShopItemType.outfit:
        return 'Outfits';
      case ShopItemType.accessory:
        return 'Accessories';
      case ShopItemType.companion:
        return 'Companions';
      case ShopItemType.pet:
        return 'Pets';
      default:
        return 'Items';
    }
  }

  LinearGradient _getGradientForType(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return const LinearGradient(
          colors: [Color(0xFF4ECDC4), Color(0xFF44A08D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case ShopItemType.outfit:
        return const LinearGradient(
          colors: [Color(0xFF6C63FF), Color(0xFF9C27B0)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case ShopItemType.accessory:
        return const LinearGradient(
          colors: [Color(0xFFFF6B6B), Color(0xFFFFE66D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case ShopItemType.companion:
        return const LinearGradient(
          colors: [Color(0xFF667eea), Color(0xFF764ba2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case ShopItemType.pet:
        return const LinearGradient(
          colors: [Color(0xFF8BC34A), Color(0xFF4CAF50)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      default:
        return AppTheme.primaryGradient;
    }
  }

  Color _getRarityColor(ShopItemRarity rarity) {
    switch (rarity) {
      case ShopItemRarity.common:
        return Colors.grey;
      case ShopItemRarity.rare:
        return Colors.blue;
      case ShopItemRarity.epic:
        return Colors.purple;
      case ShopItemRarity.legendary:
        return Colors.orange;
    }
  }

  bool _isItemOwned(ShopItemModel item, user) {
    if (user == null) return false;

    switch (item.type) {
      case ShopItemType.environment:
        return user.ownedEnvironments.contains(item.id);
      case ShopItemType.outfit:
      case ShopItemType.accessory:
      case ShopItemType.companion:
      case ShopItemType.pet:
        return user.ownedOutfits.contains(item.id);
      default:
        return false;
    }
  }

  // Action methods
  void _exitPreviewMode() async {
    try {
      print('🔙 Exiting preview mode');
      await _slideController.reverse();
      await _fadeController.reverse();

      if (mounted) {
        await ref.read(shopPreviewProvider.notifier).exitPreviewMode();

        // Ensure we're on the shop tab to show the shop overlay
        final currentIndex = ref.read(navigationIndexProvider);
        if (currentIndex != 1) {
          ref.read(navigationIndexProvider.notifier).state = 1;
        }

        print('🔙 Shop overlay should now be visible');
      }
    } catch (e) {
      print('🔴 Error exiting preview mode: $e');
      // Force exit even if there's an error
      if (mounted) {
        ref.read(shopPreviewProvider.notifier).clearError();
        // Still ensure we're on shop tab
        ref.read(navigationIndexProvider.notifier).state = 1;
      }
    }
  }

  void _purchaseItem(ShopItemModel item) async {
    final confirmed = await showPurchaseConfirmationDialog(context, item);
    if (confirmed == true) {
      await ref.read(shopPreviewProvider.notifier).purchaseCurrentItem();
    }
  }

  void _onItemSelected(ShopItemModel item) {
    ref.read(shopPreviewProvider.notifier).selectPreviewItem(item);
  }

  void _tryOnItem(ShopItemModel item) {
    ref.read(shopPreviewProvider.notifier).selectPreviewItem(item);
  }
}

// Keyboard-style overlay implementation
class _PreviewModeKeyboardOverlayState extends ConsumerState<PreviewModeKeyboardOverlay>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // Start animation when widget is created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final previewState = ref.watch(shopPreviewProvider);

    if (!previewState.isInPreviewMode) {
      return const SizedBox.shrink();
    }

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          _exitPreviewMode();
        }
      },
      child: SlideTransition(
        position: _slideAnimation,
        child: SizedBox(
          height: 380, // Same height as the original bottom panel
          width: double.infinity,
          child: _buildBottomPanel(previewState),
        ),
      ),
    );
  }

  // Reuse the same bottom panel from the original overlay
  Widget _buildBottomPanel(ShopPreviewState previewState) {
    return GlassmorphicBottomSheet(
      height: null, // Let it expand to fill available space
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Selected item info
          if (previewState.selectedItem != null) ...[
            _buildSelectedItemInfo(previewState.selectedItem!),
            const SizedBox(height: 12),
          ],

          // Item selector
          Text(
            'Choose ${_getCategoryDisplayName(previewState.currentCategory ?? ShopItemType.outfit)}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),

          // Horizontal item selector (same as original)
          HorizontalItemSelector(
            items: previewState.availableItems,
            lockedItems: previewState.lockedItems,
            selectedItem: previewState.selectedItem,
            onItemSelected: _onItemSelected,
          ),

          const SizedBox(height: 12),

          // Action buttons
          _buildActionButtons(previewState),
        ],
      ),
    );
  }

  // Helper methods (simplified versions of the original overlay methods)
  void _exitPreviewMode() async {
    await _slideController.reverse();
    if (mounted) {
      await ref.read(shopPreviewProvider.notifier).exitPreviewMode();
      final currentIndex = ref.read(navigationIndexProvider);
      if (currentIndex != 1) {
        ref.read(navigationIndexProvider.notifier).state = 1;
      }
    }
  }

  Widget _buildSelectedItemInfo(ShopItemModel item) {
    // Simplified version - you can expand this
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(Icons.checkroom, color: Colors.white),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${item.price} coins',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Action methods
  void _onItemSelected(ShopItemModel item) {
    ref.read(shopPreviewProvider.notifier).selectPreviewItem(item);
  }

  void _tryOnItem(ShopItemModel item) {
    ref.read(shopPreviewProvider.notifier).selectPreviewItem(item);
  }

  void _purchaseItem(ShopItemModel item) async {
    final confirmed = await showPurchaseConfirmationDialog(context, item);
    if (confirmed == true) {
      await ref.read(shopPreviewProvider.notifier).purchaseCurrentItem();
    }
  }

  Widget _buildActionButtons(ShopPreviewState previewState) {
    final selectedItem = previewState.selectedItem;
    if (selectedItem == null) return const SizedBox.shrink();

    final relationshipGate = ref.read(relationshipGateControllerProvider);
    final isLocked = !relationshipGate.isItemUnlocked(
      selectedItem,
      relationshipGate.getCurrentRelationshipLevel(),
    );

    return Row(
      children: [
        // Try On / Preview button
        Expanded(
          child: GlassmorphicButton(
            onPressed: isLocked ? null : () => _tryOnItem(selectedItem),
            backgroundColor: AppTheme.primaryColor,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.visibility_rounded,
                  color: isLocked ? AppTheme.textSecondaryColor : Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  isLocked ? 'Locked' : 'Try On',
                  style: TextStyle(
                    color: isLocked ? AppTheme.textSecondaryColor : Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 12),
        // Purchase button
        Expanded(
          child: GlassmorphicButton(
            onPressed: isLocked ? null : () => _purchaseItem(selectedItem),
            backgroundColor: AppTheme.accentColor,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.shopping_cart_rounded,
                  color: isLocked ? AppTheme.textSecondaryColor : Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  isLocked ? 'Locked' : '${selectedItem.price} coins',
                  style: TextStyle(
                    color: isLocked ? AppTheme.textSecondaryColor : Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  String _getCategoryDisplayName(ShopItemType category) {
    switch (category) {
      case ShopItemType.outfit:
        return 'Outfits';
      case ShopItemType.accessory:
        return 'Accessories';
      case ShopItemType.environment:
        return 'Environments';
      case ShopItemType.companion:
        return 'Companions';
      case ShopItemType.pet:
        return 'Pets';
      default:
        return 'Items';
    }
  }
}
