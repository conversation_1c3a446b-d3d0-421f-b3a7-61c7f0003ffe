import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_theme.dart';
import '../../models/shop/shop_item_model.dart';
import '../../models/shop/shop_preview_models.dart';
import '../../providers/shop_preview_provider.dart';
import '../../services/relationship/relationship_gate_controller.dart';
import '../common/glassmorphic_container.dart';

class HorizontalItemSelector extends ConsumerStatefulWidget {
  final List<ShopItemModel> items;
  final List<ShopItemModel> lockedItems;
  final ShopItemModel? selectedItem;
  final Function(ShopItemModel) onItemSelected;
  final ScrollController? scrollController;

  const HorizontalItemSelector({
    super.key,
    required this.items,
    required this.lockedItems,
    this.selectedItem,
    required this.onItemSelected,
    this.scrollController,
  });

  @override
  ConsumerState<HorizontalItemSelector> createState() => _HorizontalItemSelectorState();
}

class _HorizontalItemSelectorState extends ConsumerState<HorizontalItemSelector> {
  late ScrollController _scrollController;
  int? _selectedIndex;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.scrollController ?? ScrollController();
    _updateSelectedIndex();
  }

  @override
  void didUpdateWidget(HorizontalItemSelector oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedItem != widget.selectedItem) {
      _updateSelectedIndex();
      _scrollToSelected();
    }
  }

  void _updateSelectedIndex() {
    if (widget.selectedItem != null) {
      final allItems = [...widget.items, ...widget.lockedItems];
      _selectedIndex = allItems.indexWhere((item) => item.id == widget.selectedItem!.id);
    } else {
      _selectedIndex = null;
    }
  }

  void _scrollToSelected() {
    if (_selectedIndex != null && _scrollController.hasClients) {
      final itemWidth = 120.0;
      final spacing = 12.0;
      final targetOffset = (_selectedIndex! * (itemWidth + spacing)) - (MediaQuery.of(context).size.width / 2) + (itemWidth / 2);
      
      _scrollController.animateTo(
        targetOffset.clamp(0.0, _scrollController.position.maxScrollExtent),
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final allItems = [...widget.items, ...widget.lockedItems];
    
    if (allItems.isEmpty) {
      return const SizedBox.shrink();
    }

    return SizedBox(
      height: 140,
      child: ListView.builder(
        controller: _scrollController,
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: allItems.length,
        itemBuilder: (context, index) {
          final item = allItems[index];
          final isLocked = widget.lockedItems.contains(item);
          final isSelected = _selectedIndex == index;
          
          return Padding(
            padding: const EdgeInsets.only(right: 12),
            child: _ItemCard(
              item: item,
              isSelected: isSelected,
              isLocked: isLocked,
              onTap: () => widget.onItemSelected(item),
            ).animate(delay: Duration(milliseconds: index * 50))
              .fadeIn(duration: 300.ms)
              .slideX(begin: 0.3, end: 0),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    if (widget.scrollController == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }
}

class _ItemCard extends ConsumerWidget {
  final ShopItemModel item;
  final bool isSelected;
  final bool isLocked;
  final VoidCallback onTap;

  const _ItemCard({
    required this.item,
    required this.isSelected,
    required this.isLocked,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final relationshipLevel = ref.watch(currentRelationshipLevelProvider);
    
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 100,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected 
                ? AppTheme.primaryColor
                : Colors.white.withValues(alpha: 0.2),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: AppTheme.primaryColor.withValues(alpha: 0.3),
              blurRadius: 12,
              spreadRadius: 2,
            ),
          ] : null,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Stack(
            children: [
              // Background/Image
              _buildItemBackground(),
              
              // Glassmorphic overlay
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.7),
                    ],
                  ),
                ),
              ),
              
              // Content
              Padding(
                padding: const EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Rarity indicator
                    _buildRarityIndicator(),
                    
                    const Spacer(),
                    
                    // Item name
                    Text(
                      item.name,
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // Price or lock indicator
                    _buildBottomInfo(context, relationshipLevel),
                  ],
                ),
              ),
              
              // Lock overlay
              if (isLocked) _buildLockOverlay(context, relationshipLevel),
              
              // Selection indicator
              if (isSelected) _buildSelectionIndicator(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildItemBackground() {
    if (item.imageUrl != null) {
      return Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: NetworkImage(item.imageUrl!),
            fit: BoxFit.cover,
          ),
        ),
      );
    } else {
      return Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: _getGradientForType(item.type),
        ),
        child: Center(
          child: Icon(
            _getIconForType(item.type),
            size: 32,
            color: Colors.white.withValues(alpha: 0.8),
          ),
        ),
      );
    }
  }

  Widget _buildRarityIndicator() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: _getRarityColor(item.rarity).withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        _getRarityText(item.rarity),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 8,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildBottomInfo(BuildContext context, int relationshipLevel) {
    if (isLocked) {
      final controller = ProviderScope.containerOf(context).read(relationshipGateControllerProvider);
      final requiredLevel = controller.getRequiredRelationshipLevel(item);
      final relationshipLevelEnum = RelationshipLevel.fromLevel(requiredLevel);
      
      return Row(
        children: [
          Icon(
            Icons.lock_rounded,
            size: 12,
            color: AppTheme.warningColor,
          ),
          const SizedBox(width: 2),
          Expanded(
            child: Text(
              relationshipLevelEnum.displayName,
              style: TextStyle(
                color: AppTheme.warningColor,
                fontSize: 8,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      );
    } else {
      return Row(
        children: [
          Icon(
            Icons.favorite,
            size: 12,
            color: AppTheme.accentColor,
          ),
          const SizedBox(width: 2),
          Text(
            '${item.price}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      );
    }
  }

  Widget _buildLockOverlay(BuildContext context, int relationshipLevel) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.6),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lock_rounded,
              color: AppTheme.warningColor,
              size: 24,
            ),
            const SizedBox(height: 4),
            Text(
              'Locked',
              style: TextStyle(
                color: AppTheme.warningColor,
                fontSize: 10,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectionIndicator() {
    return Positioned(
      top: 8,
      right: 8,
      child: Container(
        width: 20,
        height: 20,
        decoration: BoxDecoration(
          color: AppTheme.primaryColor,
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 2),
        ),
        child: const Icon(
          Icons.check,
          size: 12,
          color: Colors.white,
        ),
      ),
    );
  }

  LinearGradient _getGradientForType(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return const LinearGradient(
          colors: [Color(0xFF4ECDC4), Color(0xFF44A08D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case ShopItemType.outfit:
        return const LinearGradient(
          colors: [Color(0xFF6C63FF), Color(0xFF9C27B0)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case ShopItemType.accessory:
        return const LinearGradient(
          colors: [Color(0xFFFF6B6B), Color(0xFFFFE66D)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      default:
        return AppTheme.primaryGradient;
    }
  }

  IconData _getIconForType(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return Icons.landscape_rounded;
      case ShopItemType.outfit:
        return Icons.checkroom_rounded;
      case ShopItemType.accessory:
        return Icons.diamond_rounded;
      default:
        return Icons.shopping_bag_rounded;
    }
  }

  Color _getRarityColor(ShopItemRarity rarity) {
    switch (rarity) {
      case ShopItemRarity.common:
        return Colors.grey;
      case ShopItemRarity.rare:
        return Colors.blue;
      case ShopItemRarity.epic:
        return Colors.purple;
      case ShopItemRarity.legendary:
        return Colors.orange;
    }
  }

  String _getRarityText(ShopItemRarity rarity) {
    switch (rarity) {
      case ShopItemRarity.common:
        return 'C';
      case ShopItemRarity.rare:
        return 'R';
      case ShopItemRarity.epic:
        return 'E';
      case ShopItemRarity.legendary:
        return 'L';
    }
  }
}

// Loading state widget
class HorizontalItemSelectorLoading extends StatelessWidget {
  const HorizontalItemSelectorLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 140,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: 5,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.only(right: 12),
            child: Container(
              width: 100,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.2),
                ),
              ),
            ).animate(onPlay: (controller) => controller.repeat())
              .shimmer(duration: 1500.ms, color: Colors.white.withValues(alpha: 0.1)),
          );
        },
      ),
    );
  }
}

// Empty state widget
class HorizontalItemSelectorEmpty extends StatelessWidget {
  final String message;

  const HorizontalItemSelectorEmpty({
    super.key,
    this.message = 'No items available',
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 140,
      child: Center(
        child: GlassmorphicContainer(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.shopping_bag_outlined,
                size: 32,
                color: AppTheme.textSecondaryColor,
              ),
              const SizedBox(height: 8),
              Text(
                message,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}