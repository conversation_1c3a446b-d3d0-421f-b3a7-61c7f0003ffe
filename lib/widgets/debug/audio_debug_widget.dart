import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'dart:typed_data';
import 'dart:math' as math;
import '../../services/voice/realtime_voice_service.dart';
import '../../providers/chat_provider.dart';

/// Debug widget for testing audio recording and transcription
class AudioDebugWidget extends ConsumerStatefulWidget {
  const AudioDebugWidget({super.key});

  @override
  ConsumerState<AudioDebugWidget> createState() => _AudioDebugWidgetState();
}

class _AudioDebugWidgetState extends ConsumerState<AudioDebugWidget> {
  final RealtimeVoiceService _voiceService = RealtimeVoiceService();
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isInitialized = false;
  bool _isListening = false;
  bool _isRecordingForPlayback = false;
  bool _hasRecording = false;
  bool _isPlaying = false;
  String _lastTranscription = '';
  String _lastError = '';
  final List<String> _vadEvents = [];
  final List<String> _debugLogs = [];
  String? _recordedAudioPath;
  String? _lastTranscriptionAudioPath;
  bool _hasTranscriptionAudio = false;
  bool _debugMode = false;

  @override
  void initState() {
    super.initState();
    _initializeService();
  }

  Future<void> _initializeService() async {
    try {
      final success = await _voiceService.initialize();
      setState(() {
        _isInitialized = success;
      });

      // Add custom audio processor to capture audio for playback (don't overwrite existing ones)
      _voiceService.addAudioProcessor(_captureAudioForPlayback);
      setState(() {
        _debugLogs.add('Debug audio processor added (preserving existing processors)');
      });

      // Listen to transcription results
      _voiceService.transcriptionStream.listen((result) {
        setState(() {
          _lastTranscription = result.transcription;
          _debugLogs.add('Transcription: "${result.transcription}" (confidence: ${result.confidence})');
        });
      });

      // Listen to errors
      _voiceService.errorStream.listen((error) {
        setState(() {
          _lastError = error;
          _debugLogs.add('Error: $error');
        });
      });

      // Listen to VAD events
      _voiceService.vadEventsStream.listen((event) {
        setState(() {
          _vadEvents.add('${DateTime.now().toIso8601String()}: $event');
          if (_vadEvents.length > 10) {
            _vadEvents.removeAt(0);
          }
          _debugLogs.add('VAD: $event');

          // Special handling for speech end to trigger audio capture
          if (event.contains('Speech end') || event.contains('Real speech end')) {
            _debugLogs.add('Speech end detected - should trigger audio capture');
          }
        });
      });

      // Listen to state changes
      _voiceService.stateStream.listen((state) {
        setState(() {
          _isListening = state == VoiceState.listening;
          _debugLogs.add('State: $state');
        });
      });

      // Listen to audio player state
      _audioPlayer.onPlayerStateChanged.listen((state) {
        setState(() {
          _isPlaying = state == PlayerState.playing;
        });
      });

      // Listen to chat service transcription results (actual backend transcriptions)
      final chatService = ref.read(chatServiceProvider);
      chatService.transcriptionStream.listen((transcription) {
        setState(() {
          _debugLogs.add('🎯 Backend transcription received: "$transcription"');
          _lastTranscription = transcription;

          // When we get a transcription, mark that we have transcription audio available
          if (_recordedAudioPath != null) {
            // Copy the recorded audio as transcription audio
            _copyRecordedAudioAsTranscriptionAudio();
            _debugLogs.add('✅ Transcription audio is now available for playback');
          }
        });
      });

    } catch (e) {
      setState(() {
        _lastError = 'Failed to initialize: $e';
      });
    }
  }



  /// Capture audio for playback before sending to backend
  Future<void> _captureAudioForPlayback(Uint8List audioData, String chunkId, bool isFinal) async {
    try {
      setState(() {
        _debugLogs.add('🎯 _captureAudioForPlayback called: ${audioData.length} bytes, isFinal: $isFinal, chunkId: $chunkId');
      });

      if (audioData.isEmpty) {
        setState(() {
          _debugLogs.add('❌ Audio data is empty, skipping capture');
        });
        return;
      }

      // Save the audio data for playback
      final tempDir = await getTemporaryDirectory();
      final audioFile = File('${tempDir.path}/debug_audio_$chunkId.wav');

      setState(() {
        _debugLogs.add('📁 Creating WAV file from ${audioData.length} bytes of PCM data');
        _debugLogs.add('🎵 Using 16kHz sample rate (fixed from 48kHz mismatch)');
      });

      // Create a simple WAV file header for the PCM data
      final wavData = _createWavFile(audioData);
      await audioFile.writeAsBytes(wavData);

      setState(() {
        _debugLogs.add('💾 WAV file created: ${wavData.length} bytes total');
        _debugLogs.add('⏱️ Estimated duration: ${(audioData.length / 2 / 16000).toStringAsFixed(2)}s');
      });

      // Also save as transcription audio (this is what gets sent to backend)
      final transcriptionFile = File('${tempDir.path}/transcription_audio_$chunkId.wav');
      await transcriptionFile.writeAsBytes(wavData);

      setState(() {
        _recordedAudioPath = audioFile.path;
        _hasRecording = true;
        _lastTranscriptionAudioPath = transcriptionFile.path;
        _hasTranscriptionAudio = true;
        _debugLogs.add('✅ Audio captured successfully!');
        _debugLogs.add('📂 Audio file: ${audioFile.path}');
        _debugLogs.add('📂 Transcription file: ${transcriptionFile.path}');
        _debugLogs.add('🎵 _hasRecording: $_hasRecording');
        _debugLogs.add('🎯 _hasTranscriptionAudio: $_hasTranscriptionAudio');
      });

      // Note: We're not sending to backend automatically anymore
      // User can choose to send after listening

    } catch (e) {
      setState(() {
        _lastError = 'Failed to capture audio: $e';
        _debugLogs.add('❌ Error capturing audio: $e');
      });
    }
  }

  /// Create a WAV file from PCM data
  Uint8List _createWavFile(Uint8List pcmData) {
    // Use 16kHz sample rate to match the actual audio data
    final sampleRate = 16000;  // Fixed: was 48000, causing 3x speed playback
    final channels = 1;
    final bitsPerSample = 16;
    final byteRate = sampleRate * channels * bitsPerSample ~/ 8;
    final blockAlign = channels * bitsPerSample ~/ 8;
    final dataSize = pcmData.length;
    final fileSize = 36 + dataSize;

    final header = <int>[
      // RIFF header
      0x52, 0x49, 0x46, 0x46, // "RIFF"
      fileSize & 0xff, (fileSize >> 8) & 0xff, (fileSize >> 16) & 0xff, (fileSize >> 24) & 0xff,
      0x57, 0x41, 0x56, 0x45, // "WAVE"

      // fmt chunk
      0x66, 0x6d, 0x74, 0x20, // "fmt "
      16, 0, 0, 0, // chunk size
      1, 0, // audio format (PCM)
      channels, 0, // number of channels
      sampleRate & 0xff, (sampleRate >> 8) & 0xff, (sampleRate >> 16) & 0xff, (sampleRate >> 24) & 0xff,
      byteRate & 0xff, (byteRate >> 8) & 0xff, (byteRate >> 16) & 0xff, (byteRate >> 24) & 0xff,
      blockAlign, 0, // block align
      bitsPerSample, 0, // bits per sample

      // data chunk
      0x64, 0x61, 0x74, 0x61, // "data"
      dataSize & 0xff, (dataSize >> 8) & 0xff, (dataSize >> 16) & 0xff, (dataSize >> 24) & 0xff,
    ];

    return Uint8List.fromList([...header, ...pcmData]);
  }

  /// Play the recorded audio
  Future<void> _playRecordedAudio() async {
    if (_recordedAudioPath == null) return;

    try {
      setState(() {
        _debugLogs.add('Playing recorded audio...');
      });

      await _audioPlayer.play(DeviceFileSource(_recordedAudioPath!));

    } catch (e) {
      setState(() {
        _lastError = 'Failed to play audio: $e';
        _debugLogs.add('Error playing audio: $e');
      });
    }
  }

  /// Stop audio playback
  Future<void> _stopPlayback() async {
    await _audioPlayer.stop();
  }

  /// Copy recorded audio as transcription audio
  Future<void> _copyRecordedAudioAsTranscriptionAudio() async {
    if (_recordedAudioPath == null) return;

    try {
      final tempDir = await getTemporaryDirectory();
      final transcriptionFile = File('${tempDir.path}/transcription_audio_${DateTime.now().millisecondsSinceEpoch}.wav');

      // Copy the recorded audio file
      final recordedFile = File(_recordedAudioPath!);
      await recordedFile.copy(transcriptionFile.path);

      setState(() {
        _lastTranscriptionAudioPath = transcriptionFile.path;
        _hasTranscriptionAudio = true;
      });

    } catch (e) {
      setState(() {
        _debugLogs.add('Error copying audio as transcription audio: $e');
      });
    }
  }

  /// Play the last transcription audio (what was actually sent to backend)
  Future<void> _playTranscriptionAudio() async {
    if (_lastTranscriptionAudioPath == null) return;

    try {
      setState(() {
        _debugLogs.add('Playing transcription audio (what was sent to backend)...');
      });

      await _audioPlayer.play(DeviceFileSource(_lastTranscriptionAudioPath!));

    } catch (e) {
      setState(() {
        _lastError = 'Failed to play transcription audio: $e';
        _debugLogs.add('Error playing transcription audio: $e');
      });
    }
  }

  /// Send the recorded audio to backend for transcription
  Future<void> _sendToBackend() async {
    if (_recordedAudioPath == null) return;

    try {
      // Read the audio file and extract PCM data (skip WAV header)
      final audioFile = File(_recordedAudioPath!);
      final wavData = await audioFile.readAsBytes();
      final pcmData = wavData.sublist(44); // Skip 44-byte WAV header

      setState(() {
        _debugLogs.add('📤 Sending audio to backend for transcription...');
        _debugLogs.add('📊 Audio data: ${pcmData.length} bytes of PCM data');
      });

      // Get the WebSocket service and send the audio
      final webSocketService = ref.read(webSocketServiceProvider);

      setState(() {
        _debugLogs.add('🔌 WebSocket connection status: ${webSocketService.isConnected}');
        _debugLogs.add('🔌 WebSocket state: ${webSocketService.state}');
      });

      if (!webSocketService.isConnected) {
        setState(() {
          _lastError = 'WebSocket not connected';
          _debugLogs.add('❌ WebSocket not connected - cannot send audio');
          _debugLogs.add('💡 Try connecting to chat first or check network connection');
        });
        return;
      }

      // Generate a unique chunk ID for this audio
      final chunkId = 'debug_${DateTime.now().millisecondsSinceEpoch}';

      setState(() {
        _debugLogs.add('🌐 WebSocket connected, sending audio chunk...');
        _debugLogs.add('🆔 Chunk ID: $chunkId');
      });

      // Send the audio chunk via WebSocket
      webSocketService.sendAudioChunk(
        audioData: Uint8List.fromList(pcmData),
        chunkId: chunkId,
        isFinal: true, // This is a complete audio chunk
        conversationId: null, // Let the service handle conversation ID
      );

      setState(() {
        _debugLogs.add('✅ Audio sent to backend successfully!');
        _debugLogs.add('⏳ Waiting for transcription response...');
      });

    } catch (e) {
      setState(() {
        _lastError = 'Failed to send to backend: $e';
        _debugLogs.add('❌ Error sending to backend: $e');
      });
    }
  }

  Future<void> _startRecordingForPlayback() async {
    if (!_isInitialized) return;

    setState(() {
      _debugLogs.clear();
      _vadEvents.clear();
      _lastTranscription = '';
      _lastError = '';
      _hasRecording = false;
      _recordedAudioPath = null;
      _hasTranscriptionAudio = false;
      _lastTranscriptionAudioPath = null;
      _isRecordingForPlayback = true;
      _debugLogs.add('🎙️ Starting recording for playback...');
      _debugLogs.add('🔧 Audio processor callback is set: ${_voiceService.hasAudioProcessor}');

      // Enable debug mode to bypass audio validation
      _voiceService.setDebugMode(true);
      _debugLogs.add('🐛 Debug mode enabled - bypassing audio validation');
    });

    final success = await _voiceService.startListening();
    if (!success) {
      setState(() {
        _lastError = 'Failed to start recording';
        _isRecordingForPlayback = false;
        _debugLogs.add('❌ Failed to start recording');
      });
    } else {
      setState(() {
        _debugLogs.add('✅ Recording started successfully');
        _debugLogs.add('🎯 Waiting for VAD to detect speech...');
      });
    }
  }

  Future<void> _stopRecordingForPlayback() async {
    setState(() {
      _debugLogs.add('Stopping recording...');
    });

    await _voiceService.stopListening();

    setState(() {
      _isRecordingForPlayback = false;
      _debugLogs.add('Recording stopped. _hasRecording: $_hasRecording');
    });
  }

  Future<void> _testAudioRecording() async {
    await _voiceService.testAudioRecording();
  }

  /// Connect WebSocket for audio sending
  Future<void> _connectWebSocket() async {
    try {
      setState(() {
        _debugLogs.add('🔌 Connecting WebSocket...');
      });

      final webSocketService = ref.read(webSocketServiceProvider);
      await webSocketService.connect();

      setState(() {
        _debugLogs.add('✅ WebSocket connected successfully');
      });

    } catch (e) {
      setState(() {
        _lastError = 'Failed to connect WebSocket: $e';
        _debugLogs.add('❌ WebSocket connection failed: $e');
      });
    }
  }

  /// Force capture current audio buffer (for debugging)
  Future<void> _forceAudioCapture() async {
    setState(() {
      _debugLogs.add('🔧 Force capturing current audio buffer...');
    });

    // Create a dummy audio capture to test the system (1 second at 16kHz)
    final dummyAudio = Uint8List.fromList(List.generate(16000 * 2, (i) => (math.sin(i * 0.1) * 100).round())); // 2 bytes per sample for 16-bit
    await _captureAudioForPlayback(dummyAudio, 'force_${DateTime.now().millisecondsSinceEpoch}', true);
  }

  /// Create a test audio file to verify playback functionality
  Future<void> _createTestAudio() async {
    try {
      setState(() {
        _debugLogs.add('Creating test audio...');
      });

      // Generate a simple sine wave tone (440Hz for 2 seconds)
      const sampleRate = 16000;  // Match the actual recording sample rate
      const duration = 2; // seconds
      const frequency = 440; // Hz (A4 note)
      const amplitude = 0.3;

      final samples = <double>[];
      for (int i = 0; i < sampleRate * duration; i++) {
        final t = i / sampleRate;
        final sample = amplitude * math.sin(2 * math.pi * frequency * t);
        samples.add(sample);
      }

      // Convert to PCM16 bytes
      final pcmData = <int>[];
      for (final sample in samples) {
        final intSample = (sample * 32767).round().clamp(-32768, 32767);
        pcmData.add(intSample & 0xFF);
        pcmData.add((intSample >> 8) & 0xFF);
      }

      // Create WAV file
      final audioBytes = Uint8List.fromList(pcmData);
      final tempDir = await getTemporaryDirectory();
      final audioFile = File('${tempDir.path}/test_audio_${DateTime.now().millisecondsSinceEpoch}.wav');

      final wavData = _createWavFile(audioBytes);
      await audioFile.writeAsBytes(wavData);

      setState(() {
        _recordedAudioPath = audioFile.path;
        _hasRecording = true;
        _debugLogs.add('Test audio created: ${audioBytes.length} bytes');
        _debugLogs.add('Test audio file: ${audioFile.path}');
      });

    } catch (e) {
      setState(() {
        _lastError = 'Failed to create test audio: $e';
        _debugLogs.add('Error creating test audio: $e');
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Audio Debug'),
        backgroundColor: Colors.blue,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Status',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text('Initialized: $_isInitialized'),
                    Text('Listening: $_isListening'),
                    Text('Recording for Playback: $_isRecordingForPlayback'),
                    Text('Has Recording: $_hasRecording'),
                    Text('Has Transcription Audio: $_hasTranscriptionAudio'),
                    Text('Playing: $_isPlaying'),
                    Row(
                      children: [
                        Text('Debug Mode: '),
                        Switch(
                          value: _debugMode,
                          onChanged: (value) {
                            setState(() {
                              _debugMode = value;
                              _voiceService.setDebugMode(value);
                              _debugLogs.add('🐛 Debug mode ${value ? 'enabled' : 'disabled'}');
                            });
                          },
                        ),
                      ],
                    ),
                    Text('WebSocket Connected: ${ref.watch(webSocketServiceProvider).isConnected}'),
                    Text('State: ${_voiceService.currentState}'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Controls Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Recording Controls',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        ElevatedButton.icon(
                          onPressed: _isInitialized && !_isListening && !_isRecordingForPlayback
                              ? _startRecordingForPlayback : null,
                          icon: const Icon(Icons.mic, size: 18),
                          label: const Text('Record & Preview'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red.shade600,
                            foregroundColor: Colors.white,
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: _isRecordingForPlayback ? _stopRecordingForPlayback : null,
                          icon: const Icon(Icons.stop, size: 18),
                          label: const Text('Stop Recording'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey.shade600,
                            foregroundColor: Colors.white,
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: _isInitialized ? _testAudioRecording : null,
                          icon: const Icon(Icons.science, size: 18),
                          label: const Text('Quick Test'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue.shade600,
                            foregroundColor: Colors.white,
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: _isInitialized ? _createTestAudio : null,
                          icon: const Icon(Icons.audiotrack, size: 18),
                          label: const Text('Create Test Audio'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.teal.shade600,
                            foregroundColor: Colors.white,
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: _isListening ? _forceAudioCapture : null,
                          icon: const Icon(Icons.save_alt, size: 18),
                          label: const Text('Force Capture'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red.shade700,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Playback Controls Section
            Card(
              color: _hasRecording ? Colors.green.shade50 : Colors.grey.shade100,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          'Playback & Send',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: _hasRecording ? Colors.green : Colors.grey,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            _hasRecording ? 'READY' : 'NO RECORDING',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _hasRecording
                          ? 'Recording available! Listen before sending to backend.'
                          : 'No recording available. Use "Record & Preview" first.',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _hasRecording ? Colors.green.shade700 : Colors.grey.shade600,
                      ),
                    ),
                    if (_recordedAudioPath != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        'File: ${_recordedAudioPath!.split('/').last}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.blue.shade600,
                          fontFamily: 'monospace',
                        ),
                      ),
                    ],
                    const SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        ElevatedButton.icon(
                          onPressed: _hasRecording && !_isPlaying ? _playRecordedAudio : null,
                          icon: const Icon(Icons.play_arrow, size: 18),
                          label: const Text('Play Recording'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _hasRecording ? Colors.green.shade600 : Colors.grey,
                            foregroundColor: Colors.white,
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: _isPlaying ? _stopPlayback : null,
                          icon: const Icon(Icons.stop, size: 18),
                          label: const Text('Stop Playback'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange.shade600,
                            foregroundColor: Colors.white,
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: _hasRecording && !_isPlaying ? _sendToBackend : null,
                          icon: const Icon(Icons.send, size: 18),
                          label: const Text('Send to Backend'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _hasRecording ? Colors.purple.shade600 : Colors.grey,
                            foregroundColor: Colors.white,
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: _hasRecording && !_hasTranscriptionAudio ? _copyRecordedAudioAsTranscriptionAudio : null,
                          icon: const Icon(Icons.copy, size: 18),
                          label: const Text('Mark as Transcription'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _hasRecording && !_hasTranscriptionAudio ? Colors.indigo.shade600 : Colors.grey,
                            foregroundColor: Colors.white,
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: _connectWebSocket,
                          icon: const Icon(Icons.wifi, size: 18),
                          label: const Text('Connect WebSocket'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.cyan.shade600,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Results Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Transcription Results',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text('Last Transcription: $_lastTranscription'),
                    if (_lastError.isNotEmpty)
                      Text(
                        'Last Error: $_lastError',
                        style: const TextStyle(color: Colors.red),
                      ),
                    if (_hasTranscriptionAudio) ...[
                      const SizedBox(height: 12),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.audiotrack, color: Colors.blue.shade600, size: 16),
                                const SizedBox(width: 8),
                                Text(
                                  'Audio sent to backend available',
                                  style: TextStyle(
                                    color: Colors.blue.shade700,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            ElevatedButton.icon(
                              onPressed: !_isPlaying ? _playTranscriptionAudio : null,
                              icon: const Icon(Icons.play_arrow, size: 16),
                              label: const Text('Play Transcription Audio'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue.shade600,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // VAD Events Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'VAD Events',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 100,
                      child: ListView.builder(
                        itemCount: _vadEvents.length,
                        itemBuilder: (context, index) {
                          return Text(
                            _vadEvents[index],
                            style: const TextStyle(fontSize: 12),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Debug Logs Section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Debug Logs',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      height: 200,
                      child: ListView.builder(
                        itemCount: _debugLogs.length,
                        itemBuilder: (context, index) {
                          return Text(
                            _debugLogs[index],
                            style: const TextStyle(fontSize: 12),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _voiceService.stopListening();

    // Remove our audio processor to avoid memory leaks
    _voiceService.removeAudioProcessor(_captureAudioForPlayback);

    _audioPlayer.dispose();

    // Clean up temporary audio files
    if (_recordedAudioPath != null) {
      try {
        File(_recordedAudioPath!).deleteSync();
      } catch (e) {
        // Ignore cleanup errors
      }
    }

    if (_lastTranscriptionAudioPath != null) {
      try {
        File(_lastTranscriptionAudioPath!).deleteSync();
      } catch (e) {
        // Ignore cleanup errors
      }
    }

    super.dispose();
  }
}
