import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_unity_widget/flutter_unity_widget.dart';

import '../../core/theme/app_theme.dart';
import '../../screens/chat/chat_screen.dart';
import '../../providers/auth_provider.dart';
import '../../providers/progress_provider.dart';
import '../../providers/unity_provider.dart';
import '../../models/shop/shop_preview_models.dart';
import '../../models/shop/shop_item_model.dart';
import '../common/glassmorphic_container.dart';
import '../shop/shop_screen_overlay.dart';
import '../shop/preview_mode_overlay.dart';
import '../profile/profile_screen_overlay.dart';
import '../collection/collection_detail_overlay.dart';
import '../collection/collection_preview_overlay.dart';
import '../../providers/shop_preview_provider.dart';
import '../../providers/collection_detail_provider.dart';
import '../../providers/collection_preview_provider.dart';

// Navigation state provider
final navigationIndexProvider = StateProvider<int>((ref) => 0);

// Global key for Unity widget to ensure it's preserved across rebuilds
final unityWidgetKey = GlobalKey(debugLabel: 'unity_widget_key');

class MainNavigation extends ConsumerStatefulWidget {
  const MainNavigation({super.key});

  @override
  ConsumerState<MainNavigation> createState() => _MainNavigationState();
}

class _MainNavigationState extends ConsumerState<MainNavigation>
    with TickerProviderStateMixin {
  late AnimationController _overlayAnimationController;
  late Animation<double> _overlayAnimation;
  @override
  void initState() {
    super.initState();

    _overlayAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _overlayAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _overlayAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // Record daily activity
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(progressProvider.notifier).recordDailyActivity();
    });
  }

  @override
  void dispose() {
    _overlayAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentIndex = ref.watch(navigationIndexProvider);
    final previewState = ref.watch(shopPreviewProvider);
    final collectionPreviewState = ref.watch(collectionPreviewProvider);
    ref.watch(currentUserProvider);

    // Listen for navigation changes and trigger animations
    ref.listen<int>(navigationIndexProvider, (previous, next) {
      if (previous != null && previous != next) {
        _handleNavigationChange(previous, next);
      }
    });

    return Scaffold(
      extendBody: true,
      body: Column(
        children: [
          // Main content area that gets pushed up when preview is active
          Expanded(
            child: Stack(
              children: [
                // Unity Background - Always visible
                _buildUnityBackground(),

                // Chat Screen Content (only visible when index is 0)
                if (currentIndex == 0) _buildChatContent(),

                // Overlay Screens (Shop and Profile)
                if (currentIndex != 0) _buildOverlayScreen(currentIndex),

                // Collection Detail Overlay - Full screen overlay for collection item details
                Consumer(
                  builder: (context, ref, child) {
                    final collectionDetailState = ref.watch(collectionDetailProvider);
                    if (collectionDetailState.isInDetailMode) {
                      return const CollectionDetailOverlay();
                    }
                    return const SizedBox.shrink();
                  },
                ),

                // Top controls for preview modes
                if (previewState.isInPreviewMode)
                  _buildPreviewTopControls(previewState),
                if (collectionPreviewState.isInPreviewMode)
                  _buildCollectionPreviewTopControls(collectionPreviewState),
              ],
            ),
          ),

          // Preview overlays that take up space like a keyboard
          if (previewState.isInPreviewMode)
            const PreviewModeKeyboardOverlay(),
          if (collectionPreviewState.isInPreviewMode)
            const CollectionPreviewKeyboardOverlay(),
        ],
      ),
    );
  }

  // Unity widget should be created only once
  Widget _buildUnityBackground() {
    return Consumer(
      builder: (context, ref, child) {
        final unityController = ref.watch(unityControllerProvider);

        return Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.black,
          child:
              unityController != null
                  ? const UnityWidgetContainer()
                  : _buildAvatarPlaceholder(),
        );
      },
    );
  }

  Widget _buildAvatarPlaceholder() {
    return Center(
      child: Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: AppTheme.primaryColor.withValues(alpha: 0.3),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: const Icon(
              Icons.psychology_rounded,
              size: 60,
              color: Colors.white,
            ),
          )
          .animate(onPlay: (controller) => controller.repeat())
          .shimmer(
            duration: 2000.ms,
            color: Colors.white.withValues(alpha: 0.3),
          ),
    );
  }

  Widget _buildChatContent() {
    return const ChatScreen();
  }

  Widget _buildOverlayScreen(int index) {
    return AnimatedBuilder(
      animation: _overlayAnimation,
      builder: (context, child) {
        return Container(
          color: Colors.black.withValues(alpha: 0.3 * _overlayAnimation.value),
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 1),
              end: Offset.zero,
            ).animate(_overlayAnimation),
            child: Container(
              margin: EdgeInsets.only(
                top: MediaQuery.of(context).size.height * 0.1,
              ),
              decoration: const BoxDecoration(
                color: Colors.transparent,
                borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
              ),
              child: _getOverlayContent(index),
            ),
          ),
        );
      },
    );
  }

  Widget _getOverlayContent(int index) {
    // Check if preview mode is active
    final previewState = ref.watch(shopPreviewProvider);

    switch (index) {
      case 1:
        return const ShopScreenOverlay();
      case 2:
        return const ProfileScreenOverlay();
      default:
        return const SizedBox.shrink();
    }
  }

  void _handleNavigationChange(int currentIndex, int newIndex) {
    // Check if preview modes are active
    final previewState = ref.read(shopPreviewProvider);
    final collectionPreviewState = ref.read(collectionPreviewProvider);
    final collectionDetailState = ref.read(collectionDetailProvider);

    // If shop preview mode is active and we're trying to navigate away from shop (index 1),
    // exit preview mode first
    if (previewState.isInPreviewMode && currentIndex == 1 && newIndex != 1) {
      print('Exiting shop preview mode before navigation');
      ref.read(shopPreviewProvider.notifier).exitPreviewMode();
    }

    // If collection preview mode is active and we're trying to navigate away from profile (index 2),
    // exit collection preview mode first
    if (collectionPreviewState.isInPreviewMode && currentIndex == 2 && newIndex != 2) {
      print('Exiting collection preview mode before navigation');
      ref.read(collectionPreviewProvider.notifier).exitPreviewMode();
    }

    // If collection detail mode is active and we're trying to navigate away from profile (index 2),
    // exit collection detail mode first
    if (collectionDetailState.isInDetailMode && currentIndex == 2 && newIndex != 2) {
      ref.read(collectionDetailProvider.notifier).exitDetailMode();
    }

    // Update navigation state
    ref.read(navigationIndexProvider.notifier).state = newIndex;

    // Handle overlay animation
    if (newIndex != 0 && currentIndex == 0) {
      // Opening overlay
      _overlayAnimationController.forward();
    } else if (newIndex == 0 && currentIndex != 0) {
      // Closing overlay
      _overlayAnimationController.reverse();
    } else if (newIndex != 0 && currentIndex != 0) {
      // Switching between overlays
      _overlayAnimationController.reset();
      _overlayAnimationController.forward();
    }
  }

  // Top controls for shop preview mode
  Widget _buildPreviewTopControls(ShopPreviewState previewState) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Exit button
              GlassmorphicButton(
                onPressed: () {
                  ref.read(shopPreviewProvider.notifier).exitPreviewMode();
                },
                padding: const EdgeInsets.all(12),
                child: const Icon(
                  Icons.close_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),

              const Spacer(),

              // Hearts display
              _buildHeartsDisplay(),
            ],
          ),
        ),
      ),
    );
  }

  // Top controls for collection preview mode
  Widget _buildCollectionPreviewTopControls(CollectionPreviewState previewState) {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 16,
      left: 16,
      right: 16,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Back button
          GlassmorphicContainer(
            padding: const EdgeInsets.all(8),
            borderRadius: BorderRadius.circular(20),
            child: IconButton(
              onPressed: () {
                ref.read(collectionPreviewProvider.notifier).exitPreviewMode();
              },
              icon: const Icon(
                Icons.arrow_back_rounded,
                color: Colors.white,
                size: 24,
              ),
            ),
          ).animate()
            .fadeIn(delay: 200.ms, duration: 300.ms)
            .scale(begin: const Offset(0.8, 0.8), end: const Offset(1.0, 1.0)),

          // Title
          // Text(
          //   'Collection Preview',
          //   style: Theme.of(context).textTheme.titleLarge?.copyWith(
          //     color: Colors.white,
          //     fontWeight: FontWeight.bold,
          //   ),
          // ).animate()
          //   .fadeIn(delay: 300.ms, duration: 300.ms)
          //   .slideY(begin: -0.3, end: 0),

          // Hearts display
          _buildHeartsDisplay().animate()
            .fadeIn(delay: 400.ms, duration: 300.ms)
            .scale(begin: const Offset(0.8, 0.8), end: const Offset(1.0, 1.0)),
        ],
      ),
    );
  }

  Widget _buildCategorySwitcher(ShopItemType category) {
    return GlassmorphicContainer(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getIconForCategory(category),
            color: AppTheme.primaryColor,
            size: 18,
          ),
          const SizedBox(width: 6),
          Text(
            _getCategoryDisplayName(category),
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeartsDisplay() {
    return Consumer(
      builder: (context, ref, child) {
        final user = ref.watch(currentUserProvider);
        final hearts = user?.progress.hearts ?? 0;

        return GlassmorphicContainer(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.favorite_rounded,
                color: AppTheme.accentColor,
                size: 18,
              ),
              const SizedBox(width: 6),
              Text(
                hearts.toString(),
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  IconData _getIconForCategory(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return Icons.landscape_rounded;
      case ShopItemType.outfit:
        return Icons.checkroom_rounded;
      case ShopItemType.accessory:
        return Icons.diamond_rounded;
      default:
        return Icons.shopping_bag_rounded;
    }
  }

  String _getCategoryDisplayName(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return 'Environments';
      case ShopItemType.outfit:
        return 'Outfits';
      case ShopItemType.accessory:
        return 'Accessories';
      default:
        return 'Items';
    }
  }
}

class NavigationItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;

  NavigationItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
  });
}

// Custom page route with slide transition
class SlidePageRoute<T> extends PageRouteBuilder<T> {
  final Widget child;
  final Offset begin;
  final Offset end;

  SlidePageRoute({
    required this.child,
    this.begin = const Offset(1.0, 0.0),
    this.end = Offset.zero,
  }) : super(
         pageBuilder: (context, animation, secondaryAnimation) => child,
         transitionsBuilder: (context, animation, secondaryAnimation, child) {
           return SlideTransition(
             position: Tween<Offset>(begin: begin, end: end).animate(
               CurvedAnimation(parent: animation, curve: Curves.easeInOut),
             ),
             child: child,
           );
         },
         transitionDuration: const Duration(milliseconds: 300),
       );
}

// Navigation helper
class NavigationHelper {
  static void pushSlideRoute(
    BuildContext context,
    Widget screen, {
    Offset? begin,
  }) {
    Navigator.of(context).push(
      SlidePageRoute(child: screen, begin: begin ?? const Offset(1.0, 0.0)),
    );
  }

  static void pushFadeRoute(BuildContext context, Widget screen) {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => screen,
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(opacity: animation, child: child);
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }

  static void pushScaleRoute(BuildContext context, Widget screen) {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => screen,
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return ScaleTransition(
            scale: Tween<double>(begin: 0.0, end: 1.0).animate(
              CurvedAnimation(parent: animation, curve: Curves.elasticOut),
            ),
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 500),
      ),
    );
  }
}

// This widget ensures Unity is only created once and maintained across rebuilds
class UnityWidgetContainer extends StatefulWidget {
  const UnityWidgetContainer({Key? key}) : super(key: key);

  @override
  State<UnityWidgetContainer> createState() => _UnityWidgetContainerState();
}

class _UnityWidgetContainerState extends State<UnityWidgetContainer> {
  @override
  Widget build(BuildContext context) {
    // Use Consumer to access the provider
    return Consumer(
      builder: (context, ref, child) {
        // Create the Unity widget only once using the global key
        return UnityWidget(
          key: unityWidgetKey,
          onUnityCreated: (controller) {
            // Initialize both Unity controller and camera controller
            ref.read(unityControllerProvider.notifier).setController(controller);
          },
          onUnityMessage: (message) {
            ref.read(unityControllerProvider.notifier).handleMessage(message);
          },
          fullscreen: false,
        );
      },
    );
  }
}
