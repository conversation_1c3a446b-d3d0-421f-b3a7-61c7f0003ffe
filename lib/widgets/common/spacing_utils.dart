import 'package:flutter/material.dart';

/// Utility class for consistent spacing throughout the app
class SpacingUtils {
  SpacingUtils._();

  // Base spacing values
  static const double _baseSpacing = 8.0;

  // Responsive spacing multipliers
  static double _getMultiplier(bool isTablet) => isTablet ? 1.5 : 1.0;

  // Spacing constants
  static double xs(bool isTablet) => _baseSpacing * 0.5 * _getMultiplier(isTablet); // 4/6
  static double sm(bool isTablet) => _baseSpacing * _getMultiplier(isTablet); // 8/12
  static double md(bool isTablet) => _baseSpacing * 2 * _getMultiplier(isTablet); // 16/24
  static double lg(bool isTablet) => _baseSpacing * 3 * _getMultiplier(isTablet); // 24/36
  static double xl(bool isTablet) => _baseSpacing * 4 * _getMultiplier(isTablet); // 32/48
  static double xxl(bool isTablet) => _baseSpacing * 6 * _getMultiplier(isTablet); // 48/72

  // Padding utilities
  static EdgeInsets paddingXS(bool isTablet) => EdgeInsets.all(xs(isTablet));
  static EdgeInsets paddingSM(bool isTablet) => EdgeInsets.all(sm(isTablet));
  static EdgeInsets paddingMD(bool isTablet) => EdgeInsets.all(md(isTablet));
  static EdgeInsets paddingLG(bool isTablet) => EdgeInsets.all(lg(isTablet));
  static EdgeInsets paddingXL(bool isTablet) => EdgeInsets.all(xl(isTablet));
  static EdgeInsets paddingXXL(bool isTablet) => EdgeInsets.all(xxl(isTablet));

  // Horizontal padding
  static EdgeInsets paddingHorizontalXS(bool isTablet) => 
      EdgeInsets.symmetric(horizontal: xs(isTablet));
  static EdgeInsets paddingHorizontalSM(bool isTablet) => 
      EdgeInsets.symmetric(horizontal: sm(isTablet));
  static EdgeInsets paddingHorizontalMD(bool isTablet) => 
      EdgeInsets.symmetric(horizontal: md(isTablet));
  static EdgeInsets paddingHorizontalLG(bool isTablet) => 
      EdgeInsets.symmetric(horizontal: lg(isTablet));
  static EdgeInsets paddingHorizontalXL(bool isTablet) => 
      EdgeInsets.symmetric(horizontal: xl(isTablet));

  // Vertical padding
  static EdgeInsets paddingVerticalXS(bool isTablet) => 
      EdgeInsets.symmetric(vertical: xs(isTablet));
  static EdgeInsets paddingVerticalSM(bool isTablet) => 
      EdgeInsets.symmetric(vertical: sm(isTablet));
  static EdgeInsets paddingVerticalMD(bool isTablet) => 
      EdgeInsets.symmetric(vertical: md(isTablet));
  static EdgeInsets paddingVerticalLG(bool isTablet) => 
      EdgeInsets.symmetric(vertical: lg(isTablet));
  static EdgeInsets paddingVerticalXL(bool isTablet) => 
      EdgeInsets.symmetric(vertical: xl(isTablet));

  // Margin utilities
  static EdgeInsets marginXS(bool isTablet) => EdgeInsets.all(xs(isTablet));
  static EdgeInsets marginSM(bool isTablet) => EdgeInsets.all(sm(isTablet));
  static EdgeInsets marginMD(bool isTablet) => EdgeInsets.all(md(isTablet));
  static EdgeInsets marginLG(bool isTablet) => EdgeInsets.all(lg(isTablet));
  static EdgeInsets marginXL(bool isTablet) => EdgeInsets.all(xl(isTablet));
  static EdgeInsets marginXXL(bool isTablet) => EdgeInsets.all(xxl(isTablet));

  // SizedBox utilities for spacing
  static Widget verticalSpaceXS(bool isTablet) => SizedBox(height: xs(isTablet));
  static Widget verticalSpaceSM(bool isTablet) => SizedBox(height: sm(isTablet));
  static Widget verticalSpaceMD(bool isTablet) => SizedBox(height: md(isTablet));
  static Widget verticalSpaceLG(bool isTablet) => SizedBox(height: lg(isTablet));
  static Widget verticalSpaceXL(bool isTablet) => SizedBox(height: xl(isTablet));
  static Widget verticalSpaceXXL(bool isTablet) => SizedBox(height: xxl(isTablet));

  static Widget horizontalSpaceXS(bool isTablet) => SizedBox(width: xs(isTablet));
  static Widget horizontalSpaceSM(bool isTablet) => SizedBox(width: sm(isTablet));
  static Widget horizontalSpaceMD(bool isTablet) => SizedBox(width: md(isTablet));
  static Widget horizontalSpaceLG(bool isTablet) => SizedBox(width: lg(isTablet));
  static Widget horizontalSpaceXL(bool isTablet) => SizedBox(width: xl(isTablet));
  static Widget horizontalSpaceXXL(bool isTablet) => SizedBox(width: xxl(isTablet));

  // Custom spacing
  static EdgeInsets customPadding({
    required bool isTablet,
    double? top,
    double? bottom,
    double? left,
    double? right,
  }) {
    final multiplier = _getMultiplier(isTablet);
    return EdgeInsets.only(
      top: (top ?? 0) * multiplier,
      bottom: (bottom ?? 0) * multiplier,
      left: (left ?? 0) * multiplier,
      right: (right ?? 0) * multiplier,
    );
  }

  static EdgeInsets customMargin({
    required bool isTablet,
    double? top,
    double? bottom,
    double? left,
    double? right,
  }) {
    final multiplier = _getMultiplier(isTablet);
    return EdgeInsets.only(
      top: (top ?? 0) * multiplier,
      bottom: (bottom ?? 0) * multiplier,
      left: (left ?? 0) * multiplier,
      right: (right ?? 0) * multiplier,
    );
  }
}

/// Extension on BuildContext for easier access to responsive spacing
extension SpacingExtension on BuildContext {
  bool get isTablet => MediaQuery.of(this).size.width > 600;
  
  // Quick access to spacing values
  double get spacingXS => SpacingUtils.xs(isTablet);
  double get spacingSM => SpacingUtils.sm(isTablet);
  double get spacingMD => SpacingUtils.md(isTablet);
  double get spacingLG => SpacingUtils.lg(isTablet);
  double get spacingXL => SpacingUtils.xl(isTablet);
  double get spacingXXL => SpacingUtils.xxl(isTablet);

  // Quick access to padding
  EdgeInsets get paddingXS => SpacingUtils.paddingXS(isTablet);
  EdgeInsets get paddingSM => SpacingUtils.paddingSM(isTablet);
  EdgeInsets get paddingMD => SpacingUtils.paddingMD(isTablet);
  EdgeInsets get paddingLG => SpacingUtils.paddingLG(isTablet);
  EdgeInsets get paddingXL => SpacingUtils.paddingXL(isTablet);
  EdgeInsets get paddingXXL => SpacingUtils.paddingXXL(isTablet);

  // Quick access to margin
  EdgeInsets get marginXS => SpacingUtils.marginXS(isTablet);
  EdgeInsets get marginSM => SpacingUtils.marginSM(isTablet);
  EdgeInsets get marginMD => SpacingUtils.marginMD(isTablet);
  EdgeInsets get marginLG => SpacingUtils.marginLG(isTablet);
  EdgeInsets get marginXL => SpacingUtils.marginXL(isTablet);
  EdgeInsets get marginXXL => SpacingUtils.marginXXL(isTablet);

  // Quick access to spacing widgets
  Widget get verticalSpaceXS => SpacingUtils.verticalSpaceXS(isTablet);
  Widget get verticalSpaceSM => SpacingUtils.verticalSpaceSM(isTablet);
  Widget get verticalSpaceMD => SpacingUtils.verticalSpaceMD(isTablet);
  Widget get verticalSpaceLG => SpacingUtils.verticalSpaceLG(isTablet);
  Widget get verticalSpaceXL => SpacingUtils.verticalSpaceXL(isTablet);
  Widget get verticalSpaceXXL => SpacingUtils.verticalSpaceXXL(isTablet);

  Widget get horizontalSpaceXS => SpacingUtils.horizontalSpaceXS(isTablet);
  Widget get horizontalSpaceSM => SpacingUtils.horizontalSpaceSM(isTablet);
  Widget get horizontalSpaceMD => SpacingUtils.horizontalSpaceMD(isTablet);
  Widget get horizontalSpaceLG => SpacingUtils.horizontalSpaceLG(isTablet);
  Widget get horizontalSpaceXL => SpacingUtils.horizontalSpaceXL(isTablet);
  Widget get horizontalSpaceXXL => SpacingUtils.horizontalSpaceXXL(isTablet);
}

/// Responsive typography utilities
class TypographyUtils {
  TypographyUtils._();

  // Font size multipliers
  static double _getFontMultiplier(bool isTablet) => isTablet ? 1.2 : 1.0;

  // Responsive font sizes
  static double caption(bool isTablet) => 10 * _getFontMultiplier(isTablet);
  static double bodySmall(bool isTablet) => 12 * _getFontMultiplier(isTablet);
  static double bodyMedium(bool isTablet) => 14 * _getFontMultiplier(isTablet);
  static double bodyLarge(bool isTablet) => 16 * _getFontMultiplier(isTablet);
  static double titleSmall(bool isTablet) => 18 * _getFontMultiplier(isTablet);
  static double titleMedium(bool isTablet) => 20 * _getFontMultiplier(isTablet);
  static double titleLarge(bool isTablet) => 22 * _getFontMultiplier(isTablet);
  static double headlineSmall(bool isTablet) => 24 * _getFontMultiplier(isTablet);
  static double headlineMedium(bool isTablet) => 28 * _getFontMultiplier(isTablet);
  static double headlineLarge(bool isTablet) => 32 * _getFontMultiplier(isTablet);

  // Icon sizes
  static double iconSmall(bool isTablet) => 16 * _getFontMultiplier(isTablet);
  static double iconMedium(bool isTablet) => 20 * _getFontMultiplier(isTablet);
  static double iconLarge(bool isTablet) => 24 * _getFontMultiplier(isTablet);
  static double iconXLarge(bool isTablet) => 32 * _getFontMultiplier(isTablet);
}

/// Extension for responsive typography
extension TypographyExtension on BuildContext {
  // Font sizes
  double get captionSize => TypographyUtils.caption(isTablet);
  double get bodySmallSize => TypographyUtils.bodySmall(isTablet);
  double get bodyMediumSize => TypographyUtils.bodyMedium(isTablet);
  double get bodyLargeSize => TypographyUtils.bodyLarge(isTablet);
  double get titleSmallSize => TypographyUtils.titleSmall(isTablet);
  double get titleMediumSize => TypographyUtils.titleMedium(isTablet);
  double get titleLargeSize => TypographyUtils.titleLarge(isTablet);
  double get headlineSmallSize => TypographyUtils.headlineSmall(isTablet);
  double get headlineMediumSize => TypographyUtils.headlineMedium(isTablet);
  double get headlineLargeSize => TypographyUtils.headlineLarge(isTablet);

  // Icon sizes
  double get iconSmallSize => TypographyUtils.iconSmall(isTablet);
  double get iconMediumSize => TypographyUtils.iconMedium(isTablet);
  double get iconLargeSize => TypographyUtils.iconLarge(isTablet);
  double get iconXLargeSize => TypographyUtils.iconXLarge(isTablet);
}
