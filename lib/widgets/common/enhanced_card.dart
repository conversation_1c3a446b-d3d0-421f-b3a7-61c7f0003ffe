import 'package:flutter/material.dart';
import 'dart:ui';
import '../../core/theme/app_theme.dart';

/// Enhanced card widget with glassmorphism effect and improved styling
class EnhancedCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;
  final List<Color>? gradientColors;
  final Color? borderColor;
  final double borderWidth;
  final List<BoxShadow>? boxShadow;
  final bool enableBlur;
  final double blurSigma;
  final VoidCallback? onTap;
  final bool isTablet;

  const EnhancedCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.borderRadius,
    this.gradientColors,
    this.borderColor,
    this.borderWidth = 1.0,
    this.boxShadow,
    this.enableBlur = true,
    this.blurSigma = 10.0,
    this.onTap,
    this.isTablet = false,
  });

  @override
  Widget build(BuildContext context) {
    final defaultPadding = EdgeInsets.all(isTablet ? 24 : 20);
    final defaultMargin = EdgeInsets.all(isTablet ? 12 : 8);
    final defaultBorderRadius = BorderRadius.circular(20);
    
    final defaultGradientColors = [
      Colors.white.withValues(alpha: 0.1),
      Colors.white.withValues(alpha: 0.05),
    ];
    
    final defaultBoxShadow = [
      BoxShadow(
        color: Colors.black.withValues(alpha: 0.1),
        offset: const Offset(0, 8),
        blurRadius: 24,
      ),
      BoxShadow(
        color: AppTheme.primaryColor.withValues(alpha: 0.1),
        offset: const Offset(0, 0),
        blurRadius: 16,
      ),
    ];

    Widget cardContent = Container(
      width: width,
      height: height,
      margin: margin ?? defaultMargin,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: gradientColors ?? defaultGradientColors,
        ),
        borderRadius: borderRadius ?? defaultBorderRadius,
        border: Border.all(
          color: borderColor ?? Colors.white.withValues(alpha: 0.2),
          width: borderWidth,
        ),
        boxShadow: boxShadow ?? defaultBoxShadow,
      ),
      child: ClipRRect(
        borderRadius: borderRadius ?? defaultBorderRadius,
        child: enableBlur
            ? BackdropFilter(
                filter: ImageFilter.blur(sigmaX: blurSigma, sigmaY: blurSigma),
                child: Padding(
                  padding: padding ?? defaultPadding,
                  child: child,
                ),
              )
            : Padding(
                padding: padding ?? defaultPadding,
                child: child,
              ),
      ),
    );

    if (onTap != null) {
      return GestureDetector(
        onTap: onTap,
        child: cardContent,
      );
    }

    return cardContent;
  }
}

/// Enhanced button with gradient and improved styling
class EnhancedButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final bool isLoading;
  final bool isTablet;
  final Color? backgroundColor;
  final List<Color>? gradientColors;
  final Color? textColor;
  final double? fontSize;
  final FontWeight? fontWeight;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final double? width;
  final double? height;
  final bool outlined;

  const EnhancedButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.isLoading = false,
    this.isTablet = false,
    this.backgroundColor,
    this.gradientColors,
    this.textColor,
    this.fontSize,
    this.fontWeight,
    this.padding,
    this.borderRadius,
    this.width,
    this.height,
    this.outlined = false,
  });

  @override
  State<EnhancedButton> createState() => _EnhancedButtonState();
}

class _EnhancedButtonState extends State<EnhancedButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final defaultPadding = EdgeInsets.symmetric(
      horizontal: widget.isTablet ? 24 : 20,
      vertical: widget.isTablet ? 16 : 12,
    );
    
    final defaultBorderRadius = BorderRadius.circular(12);
    final isEnabled = widget.onPressed != null && !widget.isLoading;

    return GestureDetector(
      onTapDown: isEnabled ? (_) => _scaleController.forward() : null,
      onTapUp: isEnabled ? (_) => _scaleController.reverse() : null,
      onTapCancel: isEnabled ? () => _scaleController.reverse() : null,
      onTap: isEnabled ? widget.onPressed : null,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: isEnabled ? _scaleAnimation.value : 1.0,
            child: Container(
              width: widget.width,
              height: widget.height,
              padding: widget.padding ?? defaultPadding,
              decoration: BoxDecoration(
                gradient: widget.outlined
                    ? null
                    : (widget.gradientColors != null
                        ? LinearGradient(colors: widget.gradientColors!)
                        : (isEnabled ? AppTheme.primaryGradient : null)),
                color: widget.outlined
                    ? Colors.transparent
                    : (widget.backgroundColor ??
                        (isEnabled ? null : Colors.grey.withValues(alpha: 0.3))),
                borderRadius: widget.borderRadius ?? defaultBorderRadius,
                border: widget.outlined
                    ? Border.all(
                        color: isEnabled
                            ? (widget.textColor ?? AppTheme.primaryColor)
                            : Colors.grey,
                        width: 2,
                      )
                    : null,
                boxShadow: !widget.outlined && isEnabled
                    ? [
                        BoxShadow(
                          color: (widget.gradientColors?.first ??
                                  widget.backgroundColor ??
                                  AppTheme.primaryColor)
                              .withValues(alpha: 0.3),
                          offset: const Offset(0, 4),
                          blurRadius: 12,
                        ),
                      ]
                    : null,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (widget.isLoading)
                    SizedBox(
                      width: widget.isTablet ? 20 : 16,
                      height: widget.isTablet ? 20 : 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          widget.textColor ?? Colors.white,
                        ),
                      ),
                    )
                  else if (widget.icon != null) ...[
                    Icon(
                      widget.icon,
                      color: widget.textColor ??
                          (widget.outlined
                              ? (isEnabled ? AppTheme.primaryColor : Colors.grey)
                              : Colors.white),
                      size: widget.isTablet ? 20 : 18,
                    ),
                    SizedBox(width: widget.isTablet ? 10 : 8),
                  ],
                  if (!widget.isLoading)
                    Text(
                      widget.text,
                      style: TextStyle(
                        color: widget.textColor ??
                            (widget.outlined
                                ? (isEnabled ? AppTheme.primaryColor : Colors.grey)
                                : Colors.white),
                        fontSize: widget.fontSize ?? (widget.isTablet ? 16 : 14),
                        fontWeight: widget.fontWeight ?? FontWeight.w600,
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

/// Enhanced progress bar with improved styling
class EnhancedProgressBar extends StatelessWidget {
  final double value;
  final Color? backgroundColor;
  final Color? valueColor;
  final double height;
  final BorderRadius? borderRadius;
  final bool showPercentage;
  final bool isTablet;
  final String? label;

  const EnhancedProgressBar({
    super.key,
    required this.value,
    this.backgroundColor,
    this.valueColor,
    this.height = 8,
    this.borderRadius,
    this.showPercentage = false,
    this.isTablet = false,
    this.label,
  });

  @override
  Widget build(BuildContext context) {
    final defaultBorderRadius = BorderRadius.circular(height / 2);
    final percentage = (value * 100).round();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null || showPercentage)
          Padding(
            padding: EdgeInsets.only(bottom: isTablet ? 8 : 6),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (label != null)
                  Text(
                    label!,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      fontSize: isTablet ? 14 : 12,
                    ),
                  ),
                if (showPercentage)
                  Text(
                    '$percentage%',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: valueColor ?? AppTheme.primaryColor,
                      fontSize: isTablet ? 12 : 10,
                    ),
                  ),
              ],
            ),
          ),
        Container(
          height: height,
          decoration: BoxDecoration(
            color: backgroundColor ?? Colors.white.withValues(alpha: 0.1),
            borderRadius: borderRadius ?? defaultBorderRadius,
          ),
          child: ClipRRect(
            borderRadius: borderRadius ?? defaultBorderRadius,
            child: LinearProgressIndicator(
              value: value.clamp(0.0, 1.0),
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(
                valueColor ?? AppTheme.primaryColor,
              ),
              minHeight: height,
            ),
          ),
        ),
      ],
    );
  }
}
