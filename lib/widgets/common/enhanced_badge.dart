import 'package:flutter/material.dart';
import '../../core/theme/app_theme.dart';

/// Enhanced badge widget with various styles and animations
class EnhancedBadge extends StatefulWidget {
  final String text;
  final IconData? icon;
  final Color? backgroundColor;
  final List<Color>? gradientColors;
  final Color? textColor;
  final Color? iconColor;
  final Color? borderColor;
  final double? borderWidth;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final bool isTablet;
  final BadgeStyle style;
  final BadgeSize size;
  final bool animated;
  final VoidCallback? onTap;

  const EnhancedBadge({
    super.key,
    required this.text,
    this.icon,
    this.backgroundColor,
    this.gradientColors,
    this.textColor,
    this.iconColor,
    this.borderColor,
    this.borderWidth,
    this.padding,
    this.borderRadius,
    this.isTablet = false,
    this.style = BadgeStyle.filled,
    this.size = BadgeSize.medium,
    this.animated = false,
    this.onTap,
  });

  @override
  State<EnhancedBadge> createState() => _EnhancedBadgeState();
}

class _EnhancedBadgeState extends State<EnhancedBadge>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.5, curve: Curves.elasticOut),
      ),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.1).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.5, 1.0, curve: Curves.easeInOut),
      ),
    );

    if (widget.animated) {
      _animationController.repeat(reverse: true);
    } else {
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final badgeConfig = _getBadgeConfig();
    
    Widget badge = Container(
      padding: widget.padding ?? badgeConfig.padding,
      decoration: BoxDecoration(
        color: widget.style == BadgeStyle.outlined ? Colors.transparent : 
               (widget.backgroundColor ?? badgeConfig.backgroundColor),
        gradient: widget.style == BadgeStyle.outlined ? null :
                 (widget.gradientColors != null 
                     ? LinearGradient(colors: widget.gradientColors!)
                     : badgeConfig.gradient),
        borderRadius: widget.borderRadius ?? badgeConfig.borderRadius,
        border: widget.style == BadgeStyle.outlined || widget.borderColor != null
            ? Border.all(
                color: widget.borderColor ?? badgeConfig.borderColor,
                width: widget.borderWidth ?? badgeConfig.borderWidth,
              )
            : null,
        boxShadow: widget.style == BadgeStyle.filled ? badgeConfig.boxShadow : null,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.icon != null) ...[
            Icon(
              widget.icon,
              color: widget.iconColor ?? badgeConfig.iconColor,
              size: badgeConfig.iconSize,
            ),
            SizedBox(width: badgeConfig.spacing),
          ],
          Text(
            widget.text,
            style: TextStyle(
              color: widget.textColor ?? badgeConfig.textColor,
              fontSize: badgeConfig.fontSize,
              fontWeight: badgeConfig.fontWeight,
            ),
          ),
        ],
      ),
    );

    if (widget.animated) {
      badge = AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value * _pulseAnimation.value,
            child: badge,
          );
        },
      );
    }

    if (widget.onTap != null) {
      badge = GestureDetector(
        onTap: widget.onTap,
        child: badge,
      );
    }

    return badge;
  }

  BadgeConfig _getBadgeConfig() {
    switch (widget.size) {
      case BadgeSize.small:
        return BadgeConfig.small(widget.isTablet, widget.style);
      case BadgeSize.medium:
        return BadgeConfig.medium(widget.isTablet, widget.style);
      case BadgeSize.large:
        return BadgeConfig.large(widget.isTablet, widget.style);
    }
  }
}

enum BadgeStyle { filled, outlined, soft }
enum BadgeSize { small, medium, large }

class BadgeConfig {
  final EdgeInsetsGeometry padding;
  final BorderRadius borderRadius;
  final Color backgroundColor;
  final Gradient? gradient;
  final Color textColor;
  final Color iconColor;
  final Color borderColor;
  final double borderWidth;
  final double fontSize;
  final FontWeight fontWeight;
  final double iconSize;
  final double spacing;
  final List<BoxShadow>? boxShadow;

  const BadgeConfig({
    required this.padding,
    required this.borderRadius,
    required this.backgroundColor,
    this.gradient,
    required this.textColor,
    required this.iconColor,
    required this.borderColor,
    required this.borderWidth,
    required this.fontSize,
    required this.fontWeight,
    required this.iconSize,
    required this.spacing,
    this.boxShadow,
  });

  factory BadgeConfig.small(bool isTablet, BadgeStyle style) {
    final multiplier = isTablet ? 1.2 : 1.0;
    
    return BadgeConfig(
      padding: EdgeInsets.symmetric(
        horizontal: 8 * multiplier,
        vertical: 4 * multiplier,
      ),
      borderRadius: BorderRadius.circular(8),
      backgroundColor: _getBackgroundColor(style),
      gradient: _getGradient(style),
      textColor: _getTextColor(style),
      iconColor: _getIconColor(style),
      borderColor: _getBorderColor(style),
      borderWidth: 1,
      fontSize: 10 * multiplier,
      fontWeight: FontWeight.w600,
      iconSize: 12 * multiplier,
      spacing: 4 * multiplier,
      boxShadow: _getBoxShadow(style),
    );
  }

  factory BadgeConfig.medium(bool isTablet, BadgeStyle style) {
    final multiplier = isTablet ? 1.2 : 1.0;
    
    return BadgeConfig(
      padding: EdgeInsets.symmetric(
        horizontal: 12 * multiplier,
        vertical: 6 * multiplier,
      ),
      borderRadius: BorderRadius.circular(12),
      backgroundColor: _getBackgroundColor(style),
      gradient: _getGradient(style),
      textColor: _getTextColor(style),
      iconColor: _getIconColor(style),
      borderColor: _getBorderColor(style),
      borderWidth: 1,
      fontSize: 12 * multiplier,
      fontWeight: FontWeight.w600,
      iconSize: 16 * multiplier,
      spacing: 6 * multiplier,
      boxShadow: _getBoxShadow(style),
    );
  }

  factory BadgeConfig.large(bool isTablet, BadgeStyle style) {
    final multiplier = isTablet ? 1.2 : 1.0;
    
    return BadgeConfig(
      padding: EdgeInsets.symmetric(
        horizontal: 16 * multiplier,
        vertical: 8 * multiplier,
      ),
      borderRadius: BorderRadius.circular(16),
      backgroundColor: _getBackgroundColor(style),
      gradient: _getGradient(style),
      textColor: _getTextColor(style),
      iconColor: _getIconColor(style),
      borderColor: _getBorderColor(style),
      borderWidth: 1.5,
      fontSize: 14 * multiplier,
      fontWeight: FontWeight.w600,
      iconSize: 18 * multiplier,
      spacing: 8 * multiplier,
      boxShadow: _getBoxShadow(style),
    );
  }

  static Color _getBackgroundColor(BadgeStyle style) {
    switch (style) {
      case BadgeStyle.filled:
        return AppTheme.primaryColor;
      case BadgeStyle.outlined:
        return Colors.transparent;
      case BadgeStyle.soft:
        return AppTheme.primaryColor.withValues(alpha: 0.1);
    }
  }

  static Gradient? _getGradient(BadgeStyle style) {
    switch (style) {
      case BadgeStyle.filled:
        return AppTheme.primaryGradient;
      case BadgeStyle.outlined:
      case BadgeStyle.soft:
        return null;
    }
  }

  static Color _getTextColor(BadgeStyle style) {
    switch (style) {
      case BadgeStyle.filled:
        return Colors.white;
      case BadgeStyle.outlined:
      case BadgeStyle.soft:
        return AppTheme.primaryColor;
    }
  }

  static Color _getIconColor(BadgeStyle style) {
    switch (style) {
      case BadgeStyle.filled:
        return Colors.white;
      case BadgeStyle.outlined:
      case BadgeStyle.soft:
        return AppTheme.primaryColor;
    }
  }

  static Color _getBorderColor(BadgeStyle style) {
    switch (style) {
      case BadgeStyle.filled:
        return Colors.transparent;
      case BadgeStyle.outlined:
        return AppTheme.primaryColor;
      case BadgeStyle.soft:
        return AppTheme.primaryColor.withValues(alpha: 0.3);
    }
  }

  static List<BoxShadow>? _getBoxShadow(BadgeStyle style) {
    switch (style) {
      case BadgeStyle.filled:
        return [
          BoxShadow(
            color: AppTheme.primaryColor.withValues(alpha: 0.3),
            offset: const Offset(0, 2),
            blurRadius: 8,
          ),
        ];
      case BadgeStyle.outlined:
      case BadgeStyle.soft:
        return null;
    }
  }
}

/// Predefined badge variants for common use cases
class BadgeVariants {
  static Widget success({
    required String text,
    IconData? icon,
    bool isTablet = false,
    BadgeSize size = BadgeSize.medium,
  }) {
    return EnhancedBadge(
      text: text,
      icon: icon,
      backgroundColor: AppTheme.successColor,
      textColor: Colors.white,
      iconColor: Colors.white,
      isTablet: isTablet,
      size: size,
    );
  }

  static Widget warning({
    required String text,
    IconData? icon,
    bool isTablet = false,
    BadgeSize size = BadgeSize.medium,
  }) {
    return EnhancedBadge(
      text: text,
      icon: icon,
      backgroundColor: AppTheme.warningColor,
      textColor: Colors.white,
      iconColor: Colors.white,
      isTablet: isTablet,
      size: size,
    );
  }

  static Widget error({
    required String text,
    IconData? icon,
    bool isTablet = false,
    BadgeSize size = BadgeSize.medium,
  }) {
    return EnhancedBadge(
      text: text,
      icon: icon,
      backgroundColor: AppTheme.errorColor,
      textColor: Colors.white,
      iconColor: Colors.white,
      isTablet: isTablet,
      size: size,
    );
  }

  static Widget info({
    required String text,
    IconData? icon,
    bool isTablet = false,
    BadgeSize size = BadgeSize.medium,
  }) {
    return EnhancedBadge(
      text: text,
      icon: icon,
      backgroundColor: AppTheme.accentColor,
      textColor: Colors.white,
      iconColor: Colors.white,
      isTablet: isTablet,
      size: size,
    );
  }
}
