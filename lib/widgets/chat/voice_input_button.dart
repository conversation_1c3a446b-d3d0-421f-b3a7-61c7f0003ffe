import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:typed_data';
import '../../core/theme/app_theme.dart';
import '../../providers/voice_provider.dart';
import '../../providers/chat_provider.dart';

class VoiceInputButton extends ConsumerStatefulWidget {
  final Function(String audioPath, String transcription) onVoiceMessage;
  final double size;

  const VoiceInputButton({
    super.key,
    required this.onVoiceMessage,
    this.size = 48,
  });

  @override
  ConsumerState<VoiceInputButton> createState() => _VoiceInputButtonState();
}

class _VoiceInputButtonState extends ConsumerState<VoiceInputButton>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _scaleController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final voiceState = ref.watch(voiceProvider);
    final isListening = voiceState.isListening;
    final soundLevel = voiceState.soundLevel;

    // Start/stop pulse animation based on listening state
    if (isListening && !_pulseController.isAnimating) {
      _pulseController.repeat(reverse: true);
    } else if (!isListening && _pulseController.isAnimating) {
      _pulseController.stop();
      _pulseController.reset();
    }

    return GestureDetector(
      onTapDown: (_) => _scaleController.forward(),
      onTapUp: (_) => _scaleController.reverse(),
      onTapCancel: () => _scaleController.reverse(),
      onTap: _handleTap,
      onLongPress: _handleLongPress,
      child: AnimatedBuilder(
        animation: Listenable.merge([_pulseAnimation, _scaleAnimation]),
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                gradient: isListening 
                    ? LinearGradient(
                        colors: [
                          AppTheme.errorColor,
                          AppTheme.errorColor.withOpacity(0.8),
                        ],
                      )
                    : AppTheme.primaryGradient,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: (isListening ? AppTheme.errorColor : AppTheme.primaryColor)
                        .withOpacity(0.3),
                    blurRadius: isListening ? 20 * _pulseAnimation.value : 10,
                    spreadRadius: isListening ? 5 * _pulseAnimation.value : 2,
                  ),
                ],
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Sound level indicator
                  if (isListening)
                    Container(
                      width: widget.size * 0.6 * (1 + soundLevel * 0.5),
                      height: widget.size * 0.6 * (1 + soundLevel * 0.5),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.3),
                        shape: BoxShape.circle,
                      ),
                    ).animate(onPlay: (controller) => controller.repeat())
                        .fadeIn(duration: 300.ms)
                        .then()
                        .fadeOut(duration: 300.ms),
                  
                  // Main icon
                  Icon(
                    isListening ? Icons.stop_rounded : Icons.mic_rounded,
                    color: Colors.white,
                    size: widget.size * 0.5,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  void _handleTap() {
    final voiceState = ref.read(voiceProvider);
    
    if (!voiceState.hasPermission) {
      _showPermissionDialog();
      return;
    }

    if (voiceState.isListening) {
      _stopListening();
    } else {
      _startListening();
    }
  }

  void _handleLongPress() {
    HapticFeedback.mediumImpact();
    _startListening();
  }

  void _startListening() {
    HapticFeedback.lightImpact();

    // Start typing indicator for real-time feedback
    ref.read(chatProvider.notifier).startTyping();

    ref.read(voiceProvider.notifier).startListening();
  }

  void _stopListening() {
    HapticFeedback.lightImpact();

    // Stop typing indicator
    ref.read(chatProvider.notifier).stopTyping();

    ref.read(voiceProvider.notifier).stopListening();

    // Get the recognized text and process it
    final recognizedText = ref.read(voiceProvider).recognizedText;
    if (recognizedText.isNotEmpty) {
      // For now, we'll use a placeholder audio path
      // In a real implementation, you'd save the recorded audio
      widget.onVoiceMessage('', recognizedText);
      ref.read(voiceProvider.notifier).clearRecognizedText();
    }
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.surfaceColor,
        title: const Text('Microphone Permission Required'),
        content: const Text(
          'To use voice messages, we need access to your microphone. Would you like to grant permission?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              // Request permission by trying to start listening
              // This will trigger permission request if needed
              await ref.read(voiceProvider.notifier).startListening();
              // Stop immediately if permission was granted
              final voiceState = ref.read(voiceProvider);
              if (voiceState.hasPermission && voiceState.isListening) {
                await ref.read(voiceProvider.notifier).stopListening();
              }
            },
            child: const Text('Grant Permission'),
          ),
        ],
      ),
    );
  }
}

// Voice Recording Overlay
class VoiceRecordingOverlay extends ConsumerWidget {
  final VoidCallback onCancel;
  final VoidCallback onSend;

  const VoiceRecordingOverlay({
    super.key,
    required this.onCancel,
    required this.onSend,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final voiceState = ref.watch(voiceProvider);
    final recognizedText = voiceState.recognizedText;
    final soundLevel = voiceState.soundLevel;

    return Container(
      color: Colors.black.withOpacity(0.8),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Sound visualization
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.primaryColor.withOpacity(0.3),
                    blurRadius: 30 * (1 + soundLevel),
                    spreadRadius: 10 * (1 + soundLevel),
                  ),
                ],
              ),
              child: Center(
                child: Icon(
                  Icons.mic_rounded,
                  size: 80,
                  color: Colors.white,
                ),
              ),
            ).animate(onPlay: (controller) => controller.repeat())
                .scale(
                  begin: const Offset(1.0, 1.0),
                  end: Offset(1.1 + soundLevel * 0.2, 1.1 + soundLevel * 0.2),
                  duration: 100.ms,
                ),
            
            const SizedBox(height: 40),
            
            // Recognized text
            if (recognizedText.isNotEmpty)
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 32),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.2),
                  ),
                ),
                child: Text(
                  recognizedText,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
              ).animate().fadeIn().slideY(begin: 0.3),
            
            const SizedBox(height: 40),
            
            // Control buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Cancel button
                FloatingActionButton(
                  onPressed: onCancel,
                  backgroundColor: AppTheme.errorColor,
                  child: const Icon(Icons.close_rounded),
                ),
                
                // Send button
                FloatingActionButton(
                  onPressed: recognizedText.isNotEmpty ? onSend : null,
                  backgroundColor: recognizedText.isNotEmpty 
                      ? AppTheme.successColor 
                      : Colors.grey,
                  child: const Icon(Icons.send_rounded),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Instructions
            Text(
              'Speak now...',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white.withOpacity(0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Voice Waveform Widget
class VoiceWaveform extends StatelessWidget {
  final double soundLevel;
  final bool isActive;
  final int barCount;

  const VoiceWaveform({
    super.key,
    required this.soundLevel,
    this.isActive = false,
    this.barCount = 5,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(barCount, (index) {
        final height = isActive 
            ? 20.0 * (0.5 + soundLevel * (index + 1) / barCount)
            : 4.0;
        
        return Container(
          width: 3,
          height: height,
          margin: const EdgeInsets.symmetric(horizontal: 1),
          decoration: BoxDecoration(
            color: isActive ? AppTheme.primaryColor : Colors.grey,
            borderRadius: BorderRadius.circular(2),
          ),
        ).animate(onPlay: (controller) => controller.repeat())
            .shimmer(
              duration: Duration(milliseconds: 800 + index * 100),
              color: Colors.white.withOpacity(0.3),
            );
      }),
    );
  }
}
