import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../core/theme/app_theme.dart';
import '../../core/constants/app_constants.dart';
import '../../models/task/task_model.dart';
import '../../providers/task_provider.dart';
import '../common/glassmorphic_container.dart';

class TaskOverlay extends ConsumerStatefulWidget {
  final VoidCallback onClose;

  const TaskOverlay({
    super.key,
    required this.onClose,
  });

  @override
  ConsumerState<TaskOverlay> createState() => _TaskOverlayState();
}

class _TaskOverlayState extends ConsumerState<TaskOverlay>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppConstants.defaultAnimationDuration,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final tasks = ref.watch(taskProvider);
    final activeTasks = tasks.where((task) => 
      task.status == TaskStatus.running || 
      task.status == TaskStatus.pending ||
      task.status == TaskStatus.needsIntervention
    ).toList();
    
    final completedTasks = tasks.where((task) => 
      task.status == TaskStatus.completed ||
      task.status == TaskStatus.failed ||
      task.status == TaskStatus.cancelled
    ).toList();

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Opacity(
          opacity: _opacityAnimation.value,
          child: Container(
            color: Colors.black.withOpacity(0.5),
            child: Center(
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: Container(
                  margin: const EdgeInsets.all(24),
                  constraints: const BoxConstraints(
                    maxHeight: 600,
                    maxWidth: 400,
                  ),
                  child: GlassmorphicContainer(
                    padding: EdgeInsets.zero,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildHeader(),
                        Flexible(
                          child: DefaultTabController(
                            length: 2,
                            child: Column(
                              children: [
                                _buildTabBar(),
                                Flexible(
                                  child: TabBarView(
                                    children: [
                                      _buildTaskList(activeTasks, isActive: true),
                                      _buildTaskList(completedTasks, isActive: false),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.task_alt_rounded,
            color: Colors.white,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'AI Agent Tasks',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          IconButton(
            onPressed: widget.onClose,
            icon: const Icon(
              Icons.close_rounded,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        indicator: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(12),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.textSecondaryColor,
        tabs: const [
          Tab(text: 'Active'),
          Tab(text: 'History'),
        ],
      ),
    );
  }

  Widget _buildTaskList(List<TaskModel> tasks, {required bool isActive}) {
    if (tasks.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isActive ? Icons.task_alt_rounded : Icons.history_rounded,
              size: 48,
              color: AppTheme.textSecondaryColor,
            ),
            const SizedBox(height: 16),
            Text(
              isActive ? 'No active tasks' : 'No completed tasks',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: tasks.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        return TaskCard(
          task: tasks[index],
          isActive: isActive,
        );
      },
    );
  }
}

class TaskCard extends ConsumerStatefulWidget {
  final TaskModel task;
  final bool isActive;

  const TaskCard({
    super.key,
    required this.task,
    required this.isActive,
  });

  @override
  ConsumerState<TaskCard> createState() => _TaskCardState();
}

class _TaskCardState extends ConsumerState<TaskCard> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return GlassmorphicContainer(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTaskHeader(),
          const SizedBox(height: 12),
          _buildProgressBar(),
          if (widget.task.status == TaskStatus.needsIntervention) ...[
            const SizedBox(height: 12),
            _buildInterventionAlert(),
          ],
          if (widget.isActive) ...[
            const SizedBox(height: 12),
            _buildActionButtons(),
          ],
          if (_isExpanded) ...[
            const SizedBox(height: 16),
            _buildPhaseDetails(),
          ],
        ],
      ),
    );
  }

  Widget _buildTaskHeader() {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: widget.task.statusColor.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: Icon(
            widget.task.statusIcon,
            color: widget.task.statusColor,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.task.title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                widget.task.description,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.textSecondaryColor,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          icon: AnimatedRotation(
            turns: _isExpanded ? 0.5 : 0,
            duration: AppConstants.defaultAnimationDuration,
            child: const Icon(Icons.expand_more_rounded),
          ),
        ),
      ],
    );
  }

  Widget _buildProgressBar() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              widget.task.statusText,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: widget.task.statusColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              '${(widget.task.progress * 100).toInt()}%',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        LinearProgressIndicator(
          value: widget.task.progress,
          backgroundColor: Colors.white.withOpacity(0.1),
          valueColor: AlwaysStoppedAnimation<Color>(widget.task.statusColor),
        ),
      ],
    );
  }

  Widget _buildInterventionAlert() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.amber.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.amber.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.warning_rounded,
            color: Colors.amber,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              widget.task.interventionMessage ?? 'User intervention required',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.amber,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () {
              ref.read(taskProvider.notifier).requestProgressUpdate(widget.task.id);
            },
            icon: const Icon(Icons.refresh_rounded, size: 16),
            label: const Text('Update'),
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: AppTheme.primaryColor),
              foregroundColor: AppTheme.primaryColor,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () {
              ref.read(taskProvider.notifier).cancelTask(widget.task.id);
            },
            icon: const Icon(Icons.cancel_rounded, size: 16),
            label: const Text('Cancel'),
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: Colors.red),
              foregroundColor: Colors.red,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPhaseDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Task Phases',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        ...widget.task.phases.map((phase) => _buildPhaseItem(phase)),
      ],
    );
  }

  Widget _buildPhaseItem(TaskPhaseDetail phase) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: phase.isCurrent 
            ? AppTheme.primaryColor.withOpacity(0.1)
            : Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: phase.isCurrent 
            ? Border.all(color: AppTheme.primaryColor.withOpacity(0.3))
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                phase.isCompleted 
                    ? Icons.check_circle_rounded
                    : phase.isCurrent
                        ? Icons.play_circle_rounded
                        : Icons.radio_button_unchecked_rounded,
                size: 16,
                color: phase.isCompleted 
                    ? Colors.green
                    : phase.isCurrent
                        ? AppTheme.primaryColor
                        : AppTheme.textSecondaryColor,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  phase.title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: phase.isCurrent ? FontWeight.w600 : FontWeight.w500,
                    color: phase.isCurrent ? AppTheme.primaryColor : null,
                  ),
                ),
              ),
            ],
          ),
          if (phase.description.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              phase.description,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
          if (phase.subTasks.isNotEmpty) ...[
            const SizedBox(height: 8),
            ...phase.subTasks.map((subTask) => Padding(
              padding: const EdgeInsets.only(left: 24, bottom: 4),
              child: Row(
                children: [
                  Icon(
                    Icons.fiber_manual_record,
                    size: 6,
                    color: AppTheme.textSecondaryColor,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      subTask,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ],
      ),
    );
  }
}