import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_theme.dart';
import '../../services/error/preview_error_handler.dart';
import '../common/glassmorphic_container.dart';

class PreviewErrorWidget extends ConsumerWidget {
  final PreviewError error;
  final List<ErrorRecoverySuggestion> suggestions;
  final VoidCallback? onDismiss;

  const PreviewErrorWidget({
    super.key,
    required this.error,
    required this.suggestions,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: GlassmorphicContainer(
        padding: const EdgeInsets.all(20),
        borderColor: _getErrorColor(error.type),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Error header
            Row(
              children: [
                Icon(
                  _getErrorIcon(error.type),
                  color: _getErrorColor(error.type),
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getErrorTitle(error.type),
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        error.message,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                if (onDismiss != null)
                  IconButton(
                    onPressed: onDismiss,
                    icon: const Icon(
                      Icons.close_rounded,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
              ],
            ),
            
            // Recovery suggestions
            if (suggestions.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                'Try these solutions:',
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              ...suggestions.map((suggestion) => _buildSuggestionTile(suggestion)),
            ],
          ],
        ),
      ).animate()
        .fadeIn(duration: 300.ms)
        .slideY(begin: -0.2, end: 0),
    );
  }

  Widget _buildSuggestionTile(ErrorRecoverySuggestion suggestion) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: GestureDetector(
        onTap: suggestion.action,
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.white.withValues(alpha: 0.1),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.lightbulb_outline_rounded,
                color: AppTheme.warningColor,
                size: 18,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      suggestion.title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      suggestion.description,
                      style: TextStyle(
                        color: AppTheme.textSecondaryColor,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios_rounded,
                color: AppTheme.textSecondaryColor,
                size: 14,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getErrorColor(PreviewErrorType type) {
    switch (type) {
      case PreviewErrorType.unityConnection:
        return AppTheme.errorColor;
      case PreviewErrorType.assetLoading:
        return AppTheme.warningColor;
      case PreviewErrorType.cameraTransition:
        return AppTheme.warningColor;
      case PreviewErrorType.purchase:
        return AppTheme.errorColor;
      case PreviewErrorType.relationshipSync:
        return AppTheme.warningColor;
      case PreviewErrorType.network:
        return AppTheme.errorColor;
    }
  }

  IconData _getErrorIcon(PreviewErrorType type) {
    switch (type) {
      case PreviewErrorType.unityConnection:
        return Icons.link_off_rounded;
      case PreviewErrorType.assetLoading:
        return Icons.image_not_supported_rounded;
      case PreviewErrorType.cameraTransition:
        return Icons.videocam_off_rounded;
      case PreviewErrorType.purchase:
        return Icons.payment_rounded;
      case PreviewErrorType.relationshipSync:
        return Icons.sync_problem_rounded;
      case PreviewErrorType.network:
        return Icons.wifi_off_rounded;
    }
  }

  String _getErrorTitle(PreviewErrorType type) {
    switch (type) {
      case PreviewErrorType.unityConnection:
        return 'Connection Issue';
      case PreviewErrorType.assetLoading:
        return 'Loading Problem';
      case PreviewErrorType.cameraTransition:
        return 'Camera Error';
      case PreviewErrorType.purchase:
        return 'Purchase Failed';
      case PreviewErrorType.relationshipSync:
        return 'Sync Issue';
      case PreviewErrorType.network:
        return 'Network Error';
    }
  }
}

/// System health indicator widget
class SystemHealthIndicator extends ConsumerWidget {
  const SystemHealthIndicator({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final health = ref.watch(previewSystemHealthProvider);
    
    if (health == PreviewSystemHealth.healthy) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.all(8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getHealthColor(health).withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getHealthColor(health),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getHealthIcon(health),
            color: _getHealthColor(health),
            size: 16,
          ),
          const SizedBox(width: 6),
          Text(
            _getHealthText(health),
            style: TextStyle(
              color: _getHealthColor(health),
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Color _getHealthColor(PreviewSystemHealth health) {
    switch (health) {
      case PreviewSystemHealth.healthy:
        return AppTheme.successColor;
      case PreviewSystemHealth.degraded:
        return AppTheme.warningColor;
      case PreviewSystemHealth.critical:
        return AppTheme.errorColor;
    }
  }

  IconData _getHealthIcon(PreviewSystemHealth health) {
    switch (health) {
      case PreviewSystemHealth.healthy:
        return Icons.check_circle_rounded;
      case PreviewSystemHealth.degraded:
        return Icons.warning_rounded;
      case PreviewSystemHealth.critical:
        return Icons.error_rounded;
    }
  }

  String _getHealthText(PreviewSystemHealth health) {
    switch (health) {
      case PreviewSystemHealth.healthy:
        return 'System OK';
      case PreviewSystemHealth.degraded:
        return 'Limited Features';
      case PreviewSystemHealth.critical:
        return 'System Issues';
    }
  }
}

/// Error boundary widget for preview components
class PreviewErrorBoundary extends ConsumerWidget {
  final Widget child;
  final String componentName;

  const PreviewErrorBoundary({
    super.key,
    required this.child,
    required this.componentName,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final hasError = ref.watch(hasPreviewErrorProvider);
    final errorState = ref.watch(previewErrorProvider);

    return Stack(
      children: [
        child,
        
        // Error overlay
        if (hasError && errorState.currentError != null)
          Positioned.fill(
            child: Container(
              color: Colors.black.withValues(alpha: 0.8),
              child: Center(
                child: PreviewErrorWidget(
                  error: errorState.currentError!,
                  suggestions: errorState.recoverySuggestions,
                  onDismiss: () {
                    ref.read(previewErrorProvider.notifier).clearError();
                  },
                ),
              ),
            ),
          ),
      ],
    );
  }
}