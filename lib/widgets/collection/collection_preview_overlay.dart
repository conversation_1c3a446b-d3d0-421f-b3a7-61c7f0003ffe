import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_theme.dart';
import '../../models/shop/shop_item_model.dart';
import '../../providers/collection_preview_provider.dart';
import '../../widgets/navigation/main_navigation.dart';
import '../../providers/auth_provider.dart';
import '../../models/user/user_model.dart';
import '../common/glassmorphic_container.dart';

class CollectionPreviewOverlay extends ConsumerStatefulWidget {
  const CollectionPreviewOverlay({super.key});

  @override
  ConsumerState<CollectionPreviewOverlay> createState() => _CollectionPreviewOverlayState();
}

// Keyboard-style overlay that takes up space instead of overlaying
class CollectionPreviewKeyboardOverlay extends ConsumerStatefulWidget {
  const CollectionPreviewKeyboardOverlay({super.key});

  @override
  ConsumerState<CollectionPreviewKeyboardOverlay> createState() => _CollectionPreviewKeyboardOverlayState();
}

class _CollectionPreviewOverlayState extends ConsumerState<CollectionPreviewOverlay>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // Start animations
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final previewState = ref.watch(collectionPreviewProvider);
    print(
      'Collection preview overlay build: isInPreviewMode = ${previewState.isInPreviewMode}',
    );

    if (!previewState.isInPreviewMode) {
      print('Not in collection preview mode, returning empty widget');
      return const SizedBox.shrink();
    }

    print('Building collection preview overlay UI');

    return WillPopScope(
      onWillPop: () async {
        print('🔙 Back button pressed in collection preview mode');
        _exitPreviewMode();
        return false; // Prevent default back navigation
      },
      child: Positioned.fill(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: GestureDetector(
            onTap: () {
              // Tap outside to close (optional)
              print('🔙 Tapped outside collection preview overlay');
              // Uncomment if you want tap-outside-to-close behavior
              // _exitPreviewMode();
            },
            child: Container(
              width: double.infinity,
              height: double.infinity,
              child: Stack(
                children: [
                  // Top controls
                  _buildTopControls(previewState),

                  // Bottom preview panel
                  SlideTransition(
                    position: _slideAnimation,
                    child: _buildBottomPanel(previewState),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTopControls(CollectionPreviewState previewState) {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 16,
      left: 16,
      right: 16,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Back button
          GlassmorphicContainer(
            padding: const EdgeInsets.all(8),
            borderRadius: BorderRadius.circular(20),
            child: IconButton(
              onPressed: _exitPreviewMode,
              icon: const Icon(
                Icons.arrow_back_rounded,
                color: Colors.white,
                size: 24,
              ),
            ),
          ).animate()
            .fadeIn(delay: 200.ms, duration: 300.ms)
            .scale(begin: const Offset(0.8, 0.8), end: const Offset(1.0, 1.0)),

          // Preview mode indicator
          GlassmorphicContainer(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            borderRadius: BorderRadius.circular(20),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.visibility_rounded,
                  color: AppTheme.primaryColor,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Text(
                  'Preview Mode',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ).animate()
            .fadeIn(delay: 300.ms, duration: 300.ms)
            .slideX(begin: 0.3, end: 0),
        ],
      ),
    );
  }

  Widget _buildBottomPanel(CollectionPreviewState previewState) {
    final selectedItem = previewState.selectedItem;
    if (selectedItem == null) return const SizedBox.shrink();

    return Align(
      alignment: Alignment.bottomCenter,
      child: GlassmorphicBottomSheet(
        height: 320,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Selected item info
            _buildSelectedItemInfo(selectedItem),
            
            const SizedBox(height: 16),

            // Owned items horizontal selector
            if (previewState.ownedItems.isNotEmpty) ...[
              Text(
                'Your Collection',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              _buildOwnedItemsSelector(previewState),
              const SizedBox(height: 20),
            ],

            // Action buttons
            _buildActionButtons(previewState),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedItemInfo(ShopItemModel item) {
    final user = ref.watch(currentUserProvider);
    final isCurrentlyEquipped = _isItemCurrentlyEquipped(item, user);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Item image/icon
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isCurrentlyEquipped 
                    ? AppTheme.primaryColor 
                    : Colors.white.withValues(alpha: 0.2),
                width: isCurrentlyEquipped ? 2 : 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: item.imageUrl != null
                  ? Image.network(
                      item.imageUrl!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => _buildItemIcon(item),
                    )
                  : _buildItemIcon(item),
            ),
          ),

          const SizedBox(width: 16),

          // Item details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        item.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    if (isCurrentlyEquipped)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: AppTheme.primaryColor),
                        ),
                        child: Text(
                          'Equipped',
                          style: TextStyle(
                            color: AppTheme.primaryColor,
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  item.description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                _buildItemStats(item),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemIcon(ShopItemModel item) {
    IconData iconData;
    Color iconColor;

    switch (item.type) {
      case ShopItemType.environment:
        iconData = Icons.landscape_rounded;
        iconColor = AppTheme.primaryColor;
        break;
      case ShopItemType.outfit:
        iconData = Icons.checkroom_rounded;
        iconColor = AppTheme.accentColor;
        break;
      case ShopItemType.accessory:
        iconData = Icons.diamond_rounded;
        iconColor = Colors.purple;
        break;
      case ShopItemType.companion:
        iconData = Icons.person_rounded;
        iconColor = Colors.blue;
        break;
      case ShopItemType.pet:
        iconData = Icons.pets_rounded;
        iconColor = Colors.green;
        break;
      default:
        iconData = Icons.inventory_rounded;
        iconColor = Colors.grey;
    }

    return Container(
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 30,
      ),
    );
  }

  Widget _buildItemStats(ShopItemModel item) {
    return Row(
      children: [
        _buildStatChip(
          item.rarity.name.toUpperCase(),
          _getRarityColor(item.rarity),
        ),
        const SizedBox(width: 8),
        _buildStatChip(
          item.typeDisplayName,
          AppTheme.textSecondaryColor,
        ),
      ],
    );
  }

  Widget _buildStatChip(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Color _getRarityColor(ShopItemRarity rarity) {
    switch (rarity) {
      case ShopItemRarity.common:
        return Colors.grey;
      case ShopItemRarity.rare:
        return Colors.blue;
      case ShopItemRarity.epic:
        return Colors.purple;
      case ShopItemRarity.legendary:
        return Colors.orange;
    }
  }

  Widget _buildOwnedItemsSelector(CollectionPreviewState previewState) {
    return SizedBox(
      height: 80,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 4),
        itemCount: previewState.ownedItems.length,
        itemBuilder: (context, index) {
          final item = previewState.ownedItems[index];
          final isSelected = item.id == previewState.selectedItem?.id;

          return Padding(
            padding: const EdgeInsets.only(right: 12),
            child: GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                ref.read(collectionPreviewProvider.notifier).selectPreviewItem(item);
              },
              child: Container(
                width: 70,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected
                        ? AppTheme.primaryColor
                        : Colors.white.withValues(alpha: 0.2),
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: item.imageUrl != null
                      ? Image.network(
                          item.imageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => _buildItemIcon(item),
                        )
                      : _buildItemIcon(item),
                ),
              ),
            ).animate(delay: Duration(milliseconds: index * 50))
              .fadeIn(duration: 300.ms)
              .slideX(begin: 0.3, end: 0),
          );
        },
      ),
    );
  }

  Widget _buildActionButtons(CollectionPreviewState previewState) {
    final selectedItem = previewState.selectedItem;
    if (selectedItem == null) return const SizedBox.shrink();

    final user = ref.watch(currentUserProvider);
    final isCurrentlyEquipped = _isItemCurrentlyEquipped(selectedItem, user);
    final isTransitioning = previewState.isTransitioning;

    return Row(
      children: [
        // Equip button
        Expanded(
          child: GlassmorphicButton(
            onPressed: isCurrentlyEquipped || isTransitioning
                ? null
                : () => _equipItem(selectedItem),
            backgroundColor: isCurrentlyEquipped
                ? AppTheme.successColor
                : AppTheme.primaryColor,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (isTransitioning) ...[
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  const SizedBox(width: 8),
                ] else ...[
                  Icon(
                    isCurrentlyEquipped
                        ? Icons.check_circle_rounded
                        : Icons.inventory_rounded,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                ],
                Text(
                  isTransitioning
                      ? 'Equipping...'
                      : isCurrentlyEquipped
                          ? 'Equipped'
                          : 'Equip',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  bool _isItemCurrentlyEquipped(ShopItemModel item, UserModel? user) {
    if (user == null) return false;

    switch (item.type) {
      case ShopItemType.environment:
        return user.selectedEnvironment == item.id;
      case ShopItemType.outfit:
      case ShopItemType.companion:
      case ShopItemType.accessory:
      case ShopItemType.pet:
        // For now, check against the equipped cosmetics in the preview state
        final previewState = ref.read(collectionPreviewProvider);
        final equippedItem = previewState.cosmeticState.equippedCosmetics[item.type];
        return equippedItem == item.id;
      default:
        return false;
    }
  }

  void _equipItem(ShopItemModel item) {
    HapticFeedback.mediumImpact();
    ref.read(collectionPreviewProvider.notifier).equipPreviewedItem();
  }

  void _exitPreviewMode() async {
    try {
      print('🔙 Exiting collection preview mode');
      await _slideController.reverse();
      await _fadeController.reverse();

      if (mounted) {
        await ref.read(collectionPreviewProvider.notifier).exitPreviewMode();

        // Ensure we're on the profile tab to show the profile overlay
        final currentIndex = ref.read(navigationIndexProvider);
        if (currentIndex != 2) {
          ref.read(navigationIndexProvider.notifier).state = 2;
        }

        print('🔙 Profile overlay should now be visible');
      }
    } catch (e) {
      print('🔴 Error exiting collection preview mode: $e');
      // Force exit even if there's an error
      if (mounted) {
        ref.read(collectionPreviewProvider.notifier).clearError();
        // Still ensure we're on profile tab
        ref.read(navigationIndexProvider.notifier).state = 2;
      }
    }
  }
}

// Keyboard-style overlay implementation
class _CollectionPreviewKeyboardOverlayState extends ConsumerState<CollectionPreviewKeyboardOverlay>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // Start animation when widget is created
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final previewState = ref.watch(collectionPreviewProvider);

    if (!previewState.isInPreviewMode) {
      return const SizedBox.shrink();
    }

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          _exitPreviewMode();
        }
      },
      child: SlideTransition(
        position: _slideAnimation,
        child: SizedBox(
          height: 380, // Same height as the original bottom panel
          width: double.infinity,
          child: Column(
            children: [

              // Bottom panel content
              Expanded(
                child: _buildBottomPanel(previewState),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Reuse the same bottom panel from the original overlay
  Widget _buildBottomPanel(CollectionPreviewState previewState) {
    final selectedItem = previewState.selectedItem;
    if (selectedItem == null) return const SizedBox.shrink();

    return GlassmorphicBottomSheet(
      height: null, // Let it expand to fill available space
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Selected item info
          _buildSelectedItemInfo(selectedItem),

          const SizedBox(height: 16),

          // Owned items horizontal selector
          if (previewState.ownedItems.isNotEmpty) ...[
            Text(
              'Your Collection',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            _buildOwnedItemsSelector(previewState),
            const SizedBox(height: 20),
          ],

          // Action buttons
          _buildActionButtons(previewState),
        ],
      ),
    );
  }

  // Helper methods (simplified versions of the original overlay methods)
  void _exitPreviewMode() async {
    await _slideController.reverse();
    if (mounted) {
      await ref.read(collectionPreviewProvider.notifier).exitPreviewMode();
      final currentIndex = ref.read(navigationIndexProvider);
      if (currentIndex != 2) {
        ref.read(navigationIndexProvider.notifier).state = 2;
      }
    }
  }

  Widget _buildSelectedItemInfo(ShopItemModel item) {
    // Simplified version - you can expand this
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(Icons.checkroom, color: Colors.white),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'Owned',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.7),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOwnedItemsSelector(CollectionPreviewState previewState) {
    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: previewState.ownedItems.length,
        itemBuilder: (context, index) {
          final item = previewState.ownedItems[index];
          final isSelected = item.id == previewState.selectedItem?.id;

          return Padding(
            padding: const EdgeInsets.only(right: 12),
            child: GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                ref.read(collectionPreviewProvider.notifier).selectPreviewItem(item);
              },
              child: Container(
                width: 70,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected
                        ? AppTheme.primaryColor
                        : Colors.white.withValues(alpha: 0.2),
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: item.imageUrl != null
                      ? Image.network(
                          item.imageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => _buildItemIcon(item),
                        )
                      : _buildItemIcon(item),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildItemIcon(ShopItemModel item) {
    IconData iconData;
    switch (item.type) {
      case ShopItemType.environment:
        iconData = Icons.landscape_rounded;
        break;
      case ShopItemType.outfit:
        iconData = Icons.checkroom_rounded;
        break;
      case ShopItemType.accessory:
        iconData = Icons.diamond_rounded;
        break;
      default:
        iconData = Icons.shopping_bag_rounded;
    }

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.primaryColor.withValues(alpha: 0.3),
            AppTheme.accentColor.withValues(alpha: 0.3),
          ],
        ),
      ),
      child: Icon(
        iconData,
        color: Colors.white,
        size: 32,
      ),
    );
  }

  Widget _buildActionButtons(CollectionPreviewState previewState) {
    final selectedItem = previewState.selectedItem;
    if (selectedItem == null) return const SizedBox.shrink();

    final isCurrentlyEquipped = _isItemCurrentlyEquipped(selectedItem);
    final isTransitioning = previewState.isTransitioning;

    return Row(
      children: [
        // Equip button
        Expanded(
          child: GlassmorphicButton(
            onPressed: isCurrentlyEquipped || isTransitioning
                ? null
                : () => _equipItem(selectedItem),
            backgroundColor: isCurrentlyEquipped
                ? AppTheme.successColor
                : AppTheme.primaryColor,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (isTransitioning) ...[
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  const SizedBox(width: 8),
                ] else ...[
                  Icon(
                    isCurrentlyEquipped
                        ? Icons.check_circle_rounded
                        : Icons.inventory_rounded,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                ],
                Text(
                  isCurrentlyEquipped ? 'Equipped' : 'Equip',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  bool _isItemCurrentlyEquipped(ShopItemModel item) {
    final user = ref.read(currentUserProvider);
    if (user == null) return false;

    switch (item.type) {
      case ShopItemType.environment:
        return user.selectedEnvironment == item.id;
      case ShopItemType.outfit:
        // For outfits, we can check if it's the first owned outfit (default behavior)
        // or implement a more sophisticated selection system
        return user.ownedOutfits.isNotEmpty && user.ownedOutfits.first == item.id;
      default:
        return false;
    }
  }

  void _equipItem(ShopItemModel item) {
    // For now, just select the item for preview
    ref.read(collectionPreviewProvider.notifier).selectPreviewItem(item);
  }
}
