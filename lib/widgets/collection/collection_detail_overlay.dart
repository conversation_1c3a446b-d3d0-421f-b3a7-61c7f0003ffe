import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../core/theme/app_theme.dart';
import '../../models/shop/shop_item_model.dart';
import '../../providers/collection_detail_provider.dart';
import '../../providers/auth_provider.dart';
import '../../services/storage/storage_service.dart';
import '../common/glassmorphic_container.dart';
import '../../models/user/user_model.dart';

class CollectionDetailOverlay extends ConsumerStatefulWidget {
  const CollectionDetailOverlay({super.key});

  @override
  ConsumerState<CollectionDetailOverlay> createState() => _CollectionDetailOverlayState();
}

class _CollectionDetailOverlayState extends ConsumerState<CollectionDetailOverlay>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // Start animations
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  void _exitDetailMode() async {
    await _fadeController.reverse();
    await _slideController.reverse();
    if (mounted) {
      ref.read(collectionDetailProvider.notifier).exitDetailMode();
    }
  }

  @override
  Widget build(BuildContext context) {
    final detailState = ref.watch(collectionDetailProvider);
    
    if (!detailState.isInDetailMode) {
      return const SizedBox.shrink();
    }

    return WillPopScope(
      onWillPop: () async {
        _exitDetailMode();
        return false;
      },
      child: Positioned.fill(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: GestureDetector(
            onTap: () {
              // Optional: tap outside to close
              // _exitDetailMode();
            },
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.black.withValues(alpha: 0.7),
              child: Stack(
                children: [
                  // Top controls
                  _buildTopControls(),

                  // Bottom detail panel
                  SlideTransition(
                    position: _slideAnimation,
                    child: _buildDetailPanel(detailState),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTopControls() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 16,
      right: 16,
      child: GlassmorphicContainer(
        padding: const EdgeInsets.all(8),
        borderRadius: BorderRadius.circular(20),
        child: IconButton(
          onPressed: _exitDetailMode,
          icon: const Icon(
            Icons.close_rounded,
            color: Colors.white,
            size: 24,
          ),
        ),
      ).animate()
        .fadeIn(delay: 200.ms, duration: 300.ms)
        .scale(begin: const Offset(0.8, 0.8), end: const Offset(1.0, 1.0)),
    );
  }

  Widget _buildDetailPanel(CollectionDetailState detailState) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: GlassmorphicBottomSheet(
        height: 420,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Selected item info
            if (detailState.selectedItem != null) ...[
              _buildSelectedItemInfo(detailState.selectedItem!),
              const SizedBox(height: 20),
            ],

            // Category title
            Text(
              'Your ${_getCategoryDisplayName(detailState.currentCategory ?? ShopItemType.outfit)} Collection',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),

            const SizedBox(height: 12),

            // Owned items horizontal selector
            if (detailState.ownedItems.isNotEmpty) ...[
              _buildOwnedItemsSelector(detailState),
              const SizedBox(height: 20),
            ],

            // Action buttons
            _buildActionButtons(detailState),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedItemInfo(ShopItemModel item) {
    final user = ref.watch(currentUserProvider);
    final isCurrentlyEquipped = _isItemCurrentlyEquipped(item, user);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.white.withValues(alpha: 0.1)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Item image/icon
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isCurrentlyEquipped 
                    ? AppTheme.primaryColor 
                    : Colors.white.withValues(alpha: 0.2),
                width: isCurrentlyEquipped ? 2 : 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: item.imageUrl != null
                  ? Image.network(
                      item.imageUrl!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => _buildItemIcon(item),
                    )
                  : _buildItemIcon(item),
            ),
          ),

          const SizedBox(width: 16),

          // Item details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        item.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    if (isCurrentlyEquipped)
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: AppTheme.primaryColor),
                        ),
                        child: Text(
                          'Equipped',
                          style: TextStyle(
                            color: AppTheme.primaryColor,
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  item.description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.textSecondaryColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                _buildItemStats(item),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemIcon(ShopItemModel item) {
    IconData iconData;
    Color iconColor;

    switch (item.type) {
      case ShopItemType.environment:
        iconData = Icons.landscape_rounded;
        iconColor = AppTheme.primaryColor;
        break;
      case ShopItemType.outfit:
        iconData = Icons.checkroom_rounded;
        iconColor = AppTheme.accentColor;
        break;
      case ShopItemType.accessory:
        iconData = Icons.diamond_rounded;
        iconColor = Colors.purple;
        break;
      case ShopItemType.companion:
        iconData = Icons.person_rounded;
        iconColor = Colors.blue;
        break;
      case ShopItemType.pet:
        iconData = Icons.pets_rounded;
        iconColor = Colors.green;
        break;
      default:
        iconData = Icons.inventory_rounded;
        iconColor = Colors.grey;
    }

    return Container(
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 30,
      ),
    );
  }

  Widget _buildItemStats(ShopItemModel item) {
    return Row(
      children: [
        _buildStatChip(
          item.rarity.name.toUpperCase(),
          _getRarityColor(item.rarity),
        ),
        const SizedBox(width: 8),
        _buildStatChip(
          item.typeDisplayName,
          AppTheme.textSecondaryColor,
        ),
      ],
    );
  }

  Widget _buildStatChip(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Color _getRarityColor(ShopItemRarity rarity) {
    switch (rarity) {
      case ShopItemRarity.common:
        return Colors.grey;
      case ShopItemRarity.rare:
        return Colors.blue;
      case ShopItemRarity.epic:
        return Colors.purple;
      case ShopItemRarity.legendary:
        return Colors.orange;
    }
  }

  Widget _buildOwnedItemsSelector(CollectionDetailState detailState) {
    return SizedBox(
      height: 80,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 4),
        itemCount: detailState.ownedItems.length,
        itemBuilder: (context, index) {
          final item = detailState.ownedItems[index];
          final isSelected = item.id == detailState.selectedItem?.id;
          
          return Padding(
            padding: const EdgeInsets.only(right: 12),
            child: GestureDetector(
              onTap: () {
                ref.read(collectionDetailProvider.notifier).enterDetailMode(item);
              },
              child: Container(
                width: 70,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected 
                        ? AppTheme.primaryColor 
                        : Colors.white.withValues(alpha: 0.2),
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: item.imageUrl != null
                      ? Image.network(
                          item.imageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => _buildItemIcon(item),
                        )
                      : _buildItemIcon(item),
                ),
              ),
            ).animate(delay: Duration(milliseconds: index * 50))
              .fadeIn(duration: 300.ms)
              .slideX(begin: 0.3, end: 0),
          );
        },
      ),
    );
  }

  Widget _buildActionButtons(CollectionDetailState detailState) {
    final selectedItem = detailState.selectedItem;
    if (selectedItem == null) return const SizedBox.shrink();

    final user = ref.watch(currentUserProvider);
    final isCurrentlyEquipped = _isItemCurrentlyEquipped(selectedItem, user);
    final isEquipping = detailState.isEquipping;

    return Row(
      children: [
        // Equip button
        Expanded(
          child: GlassmorphicButton(
            onPressed: isCurrentlyEquipped || isEquipping 
                ? null 
                : () => _equipItem(selectedItem),
            backgroundColor: isCurrentlyEquipped 
                ? AppTheme.successColor 
                : AppTheme.primaryColor,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (isEquipping) ...[
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  const SizedBox(width: 8),
                ] else ...[
                  Icon(
                    isCurrentlyEquipped 
                        ? Icons.check_circle_rounded 
                        : Icons.inventory_rounded,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                ],
                Text(
                  isEquipping 
                      ? 'Equipping...' 
                      : isCurrentlyEquipped 
                          ? 'Equipped' 
                          : 'Equip',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  bool _isItemCurrentlyEquipped(ShopItemModel item, UserModel? user) {
    if (user == null) return false;

    // Use mock equipment state from storage for now
    switch (item.type) {
      case ShopItemType.environment:
        return user.selectedEnvironment == item.id;
      case ShopItemType.outfit:
        final selectedOutfit = StorageService.getProgress<String>('selected_outfit');
        return selectedOutfit == item.id;
      case ShopItemType.companion:
        final selectedCompanion = StorageService.getProgress<String>('selected_companion');
        return selectedCompanion == item.id;
      case ShopItemType.accessory:
        final selectedAccessory = StorageService.getProgress<String>('selected_accessory');
        return selectedAccessory == item.id;
      case ShopItemType.pet:
        final selectedPet = StorageService.getProgress<String>('selected_pet');
        return selectedPet == item.id;
      default:
        return false;
    }
  }

  void _equipItem(ShopItemModel item) {
    HapticFeedback.mediumImpact();
    ref.read(collectionDetailProvider.notifier).equipItem(item);
  }

  String _getCategoryDisplayName(ShopItemType type) {
    switch (type) {
      case ShopItemType.environment:
        return 'Environment';
      case ShopItemType.outfit:
        return 'Outfit';
      case ShopItemType.accessory:
        return 'Accessory';
      case ShopItemType.companion:
        return 'Companion';
      case ShopItemType.pet:
        return 'Pet';
      default:
        return 'Item';
    }
  }
}
