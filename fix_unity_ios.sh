#!/bin/bash

# Unity iOS Integration Fix Script
# This script helps resolve the "_SendMessageToFlutter" undefined symbol error

echo "🔧 Unity iOS Integration Fix Script"
echo "=================================="

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ Error: Please run this script from your Flutter project root directory"
    exit 1
fi

echo "✅ Found Flutter project"

# Check if Unity export exists
if [ ! -d "ios/UnityLibrary" ]; then
    echo "❌ Error: UnityLibrary not found. Please export your Unity project first."
    echo "   1. Open Unity"
    echo "   2. Go to Flutter > Export iOS"
    exit 1
fi

echo "✅ Found UnityLibrary"

# Clean build folders
echo "🧹 Cleaning build folders..."
rm -rf ios/build
rm -rf build
flutter clean

# Update pods
echo "📦 Updating CocoaPods..."
cd ios
pod deintegrate
pod install --repo-update
cd ..

echo "🔧 Applying iOS fixes..."

# Check if AppDelegate is properly configured
if grep -q "InitUnityIntegrationWithOptions" ios/Runner/AppDelegate.swift; then
    echo "✅ AppDelegate Unity integration found"
else
    echo "⚠️  AppDelegate needs Unity integration"
    echo "   Please add the following to your AppDelegate.swift:"
    echo "   import flutter_unity_widget"
    echo "   InitUnityIntegrationWithOptions(argc: CommandLine.argc, argv: CommandLine.unsafeArgv, launchOptions)"
fi

# Create a helper script for Xcode configuration
cat > ios/configure_xcode.md << 'EOF'
# Manual Xcode Configuration Steps

After running this script, you need to manually configure Xcode:

## 1. Open Xcode Workspace
```bash
open ios/Runner.xcworkspace
```

## 2. Add Unity-iPhone Project
- Right-click in the Navigator (not on an item)
- Select "Add Files to Runner"
- Navigate to `ios/UnityLibrary/Unity-iPhone.xcodeproj`
- Click "Add"

## 3. Add UnityFramework to Runner Target
- Select Runner project in navigator
- Select Runner target
- Go to "General" tab
- In "Frameworks, Libraries, and Embedded Content":
  - Click "+" button
  - Select "UnityFramework.framework" from Unity-iPhone
  - Set to "Embed & Sign"

## 4. Configure Build Settings
- Select Runner target
- Go to "Build Settings" tab
- Search for "Other Linker Flags"
- Add: `-framework UnityFramework`
- Search for "Framework Search Paths"
- Add: `$(PROJECT_DIR)/UnityLibrary`

## 5. Architecture Settings
- In Build Settings, set:
  - `ONLY_ACTIVE_ARCH` = NO
  - `VALID_ARCHS` = arm64

## 6. Clean and Build
- Product > Clean Build Folder
- Product > Build

## Common Issues:
- If you get "mUnityPlayer" errors, see Unity setup step 3 in the documentation
- Make sure your Unity project has the flutter-unity integration package imported
- Verify that UnityMessageManager is added to your Unity scene
EOF

echo "📝 Created Xcode configuration guide at ios/configure_xcode.md"

# Check Unity project structure
if [ -d "unity" ]; then
    echo "✅ Found Unity project folder"
    
    # Check if Unity integration package is imported
    if [ -d "unity/*/Assets/FlutterUnityIntegration" ]; then
        echo "✅ Flutter Unity Integration package found in Unity project"
    else
        echo "⚠️  Flutter Unity Integration package not found in Unity project"
        echo "   Please import the fuw-2022.2.1.unitypackage in Unity"
    fi
else
    echo "⚠️  Unity project folder not found"
fi

echo ""
echo "🎉 Script completed!"
echo ""
echo "Next steps:"
echo "1. Follow the instructions in ios/configure_xcode.md"
echo "2. Make sure your Unity scene has UnityMessageManager component"
echo "3. Re-export your Unity project if needed"
echo "4. Try building your iOS app again"
echo ""
echo "If you still get errors, check:"
echo "- Unity Player Settings > Configuration > Scripting Backend = IL2CPP"
echo "- Unity Player Settings > Configuration > Target Device = iPhone + iPad"
echo "- Your Unity scene has at least one GameObject with UnityMessageManager"