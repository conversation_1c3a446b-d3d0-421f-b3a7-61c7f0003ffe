# Design Document

## Overview

The Enhanced Shop Preview System creates an immersive shopping experience by integrating Flutter UI with Unity 3D real-time preview capabilities. The system allows users to preview cosmetic items on their AI companion with smooth camera transitions, personality-based reactions, and relationship-gated content access.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Shop Screen] --> B[Preview Mode Controller]
    B --> C[Unity Camera Controller]
    B --> D[Cosmetic Preview Manager]
    B --> E[Companion Reaction System]
    
    D --> F[Item Selector Widget]
    D --> G[Unity Cosmetic Applier]
    
    E --> H[Personality Engine]
    E --> I[Animation Controller]
    E --> J[Voice/Text Generator]
    
    K[Relationship Manager] --> L[Content Gate Controller]
    L --> D
    
    M[Purchase System] --> N[Transaction Handler]
    N --> O[Inventory Manager]
    N --> P[Currency Manager]
```

### State Management Architecture

The system uses Riverpod for state management with the following provider hierarchy:

- `shopPreviewProvider` - Main preview state controller
- `previewModeProvider` - Boolean state for preview mode
- `selectedPreviewItemProvider` - Currently selected item for preview
- `previewCameraStateProvider` - Unity camera position and zoom state
- `companionReactionProvider` - Companion reaction and animation state
- `relationshipGateProvider` - Relationship-based content filtering

## Components and Interfaces

### 1. Shop Preview Controller

**Purpose**: Orchestrates the preview experience and manages state transitions.

**Key Methods**:
- `enterPreviewMode(ShopItemModel item, ShopItemType category)`
- `exitPreviewMode()`
- `selectPreviewItem(ShopItemModel item)`
- `purchaseCurrentItem()`

**State Properties**:
- `isInPreviewMode: bool`
- `currentCategory: ShopItemType?`
- `selectedItem: ShopItemModel?`
- `availableItems: List<ShopItemModel>`
- `cameraState: PreviewCameraState`

### 2. Unity Camera Controller

**Purpose**: Manages Unity camera transitions and positioning for preview mode.

**Camera States**:
- `default` - Normal chat view (close-up, upper body focus)
- `preview` - Zoomed out view (full body, centered)
- `transitioning` - Animation state between positions

**Key Methods**:
- `transitionToPreviewMode(duration: Duration)`
- `transitionToDefaultMode(duration: Duration)`
- `setCameraPosition(position: Vector3, rotation: Vector3)`

### 3. Cosmetic Preview Manager

**Purpose**: Handles real-time cosmetic application and item management.

**Key Methods**:
- `applyCosmetic(itemId: String, type: ShopItemType)`
- `revertToEquipped()`
- `preloadCosmeticAssets(items: List<ShopItemModel>)`

**Integration Points**:
- Unity cosmetic system via UnityWidgetController
- Local cosmetic cache for performance
- Asset preloading for smooth transitions

### 4. Companion Reaction System

**Purpose**: Generates personality-based reactions to cosmetic previews and purchases.

**Reaction Types**:
- `preview` - Initial reaction when item is selected
- `purchase_success` - Positive reaction after successful purchase
- `purchase_decline` - Reaction when user doesn't purchase
- `locked_item` - Reaction to relationship-locked items

**Personality-Based Reactions**:
```dart
class CompanionReaction {
  final String text;
  final String animation;
  final String emotion;
  final Duration duration;
}
```

### 5. Relationship Gate Controller

**Purpose**: Manages access to cosmetics based on user-companion relationship level.

**Relationship Levels** (based on provided model):
1. **Acquaintance** - Basic cosmetics, casual outfits
2. **Friend** - More personal items, stylish outfits
3. **Close Friend** - Intimate clothing, special accessories
4. **Intimate** - Adult-oriented cosmetics (with consent)

**Key Methods**:
- `filterItemsByRelationship(items: List<ShopItemModel>, level: int)`
- `isItemUnlocked(item: ShopItemModel, relationshipLevel: int)`
- `getUnlockRequirement(item: ShopItemModel)`

### 6. Preview UI Components

#### Horizontal Item Selector
- Scrollable list of category items
- Smooth scroll animations
- Lock indicators for restricted items
- Rarity-based visual styling

#### Preview Controls
- Purchase button with dynamic pricing
- "Try On" vs "Equipped" states
- Exit preview button
- Category switcher

#### Relationship Lock Overlay
- Clear indication of required relationship level
- Progress indicator toward next level
- Contextual companion dialogue

## Data Models

### Preview State Model
```dart
class ShopPreviewState {
  final bool isInPreviewMode;
  final ShopItemType? currentCategory;
  final ShopItemModel? selectedItem;
  final List<ShopItemModel> categoryItems;
  final PreviewCameraState cameraState;
  final CompanionReaction? currentReaction;
  final bool isTransitioning;
}
```

### Preview Camera State
```dart
class PreviewCameraState {
  final CameraMode mode;
  final Vector3 position;
  final Vector3 rotation;
  final float zoom;
  final bool isTransitioning;
}

enum CameraMode { default, preview }
```

### Cosmetic Application State
```dart
class CosmeticApplicationState {
  final Map<ShopItemType, String> appliedCosmetics;
  final Map<ShopItemType, String> equippedCosmetics;
  final bool isApplying;
}
```

## Error Handling

### Preview Mode Errors
- **Unity Connection Lost**: Graceful fallback to static preview images
- **Asset Loading Failed**: Show placeholder with retry option
- **Camera Transition Failed**: Reset to default position

### Purchase Errors
- **Insufficient Currency**: Clear error message with earning suggestions
- **Network Failure**: Queue purchase for retry when connection restored
- **Item No Longer Available**: Refresh shop and notify user

### Relationship Gate Errors
- **Invalid Relationship Data**: Default to most restrictive level
- **Sync Issues**: Use local relationship cache as fallback

## Testing Strategy

### Unit Tests
- Preview state management logic
- Relationship filtering algorithms
- Cosmetic application state transitions
- Purchase validation logic

### Integration Tests
- Flutter-Unity communication for cosmetic changes
- Camera transition smoothness
- Companion reaction triggering
- Purchase flow end-to-end

### UI Tests
- Preview mode entry/exit flows
- Horizontal selector interactions
- Purchase confirmation dialogs
- Relationship lock displays

### Performance Tests
- Cosmetic switching response time (<100ms)
- Camera transition smoothness (60fps)
- Memory usage during extended preview sessions
- Asset loading and caching efficiency

## Performance Considerations

### Optimization Strategies
1. **Asset Preloading**: Load category assets when entering preview mode
2. **Cosmetic Caching**: Cache applied cosmetics to avoid redundant Unity calls
3. **Animation Pooling**: Reuse companion reaction animations
4. **Lazy Loading**: Load relationship data only when needed

### Memory Management
- Dispose preview resources when exiting mode
- Limit concurrent cosmetic asset loading
- Clear reaction animation cache periodically

### Network Optimization
- Batch cosmetic metadata requests
- Cache relationship requirements locally
- Offline mode for owned cosmetics preview

## Security Considerations

### Content Gating
- Server-side validation of relationship levels
- Encrypted relationship progression data
- Audit logging for adult content access

### Purchase Security
- Server-side purchase validation
- Transaction idempotency
- Currency manipulation prevention

### Privacy Protection
- Anonymized usage analytics
- Secure storage of relationship data
- User consent for intimate content access

## Accessibility Features

### Visual Accessibility
- High contrast mode for UI elements
- Scalable text for item descriptions
- Color-blind friendly rarity indicators

### Motor Accessibility
- Large touch targets for item selection
- Voice commands for preview navigation
- Gesture alternatives for camera control

### Cognitive Accessibility
- Clear visual hierarchy in preview mode
- Simple, consistent interaction patterns
- Progress indicators for all transitions

## Future Enhancements

### Advanced Features
- **AR Preview Mode**: Use device camera for real-world cosmetic preview
- **Social Sharing**: Share cosmetic combinations with friends
- **Custom Cosmetics**: User-generated content with approval system
- **Seasonal Events**: Time-limited cosmetics with special unlock conditions

### Technical Improvements
- **ML-Powered Recommendations**: Suggest cosmetics based on user preferences
- **Advanced Physics**: Realistic cloth and hair simulation in Unity
- **Cross-Platform Sync**: Seamless cosmetic sync across devices
- **Voice-Controlled Shopping**: Natural language cosmetic selection