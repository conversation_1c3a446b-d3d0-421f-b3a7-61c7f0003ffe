# Implementation Plan

- [x] 1. Create core data models and state management
  - Create preview state models for managing preview mode, camera states, and cosmetic applications
  - Implement Riverpod providers for shop preview state management
  - Add relationship-based filtering logic to existing shop provider
  - _Requirements: 1.1, 1.2, 3.1, 3.2_

- [x] 2. Implement Unity camera controller integration
  - Extend existing Unity provider with camera control methods for preview mode transitions
  - Add camera state management for default and preview positions
  - Implement smooth camera transition animations with configurable duration
  - Create Unity command constants for camera operations
  - _Requirements: 1.2, 1.5, 4.1, 4.2_

- [x] 3. Build cosmetic preview manager
  - Create cosmetic application state management for real-time preview
  - Implement methods to apply and revert cosmetic changes in Unity
  - Add cosmetic asset preloading for smooth item switching
  - Create cosmetic cache system for performance optimization
  - _Requirements: 1.3, 1.4, 4.3_

- [x] 4. Develop companion reaction system
  - Create companion reaction data models with text, animation, and emotion properties
  - Implement personality-based reaction generation logic for different cosmetic types
  - Add reaction queuing system to handle rapid item switching
  - Create animation trigger methods for companion reactions
  - _Requirements: 1.4, 2.4, 5.1, 5.2, 5.5_

- [x] 5. Implement relationship-based content gating
  - Create relationship gate controller with filtering methods
  - Add relationship level validation for cosmetic access
  - Implement locked item display logic with unlock requirements
  - Create companion dialogue for relationship-locked items
  - _Requirements: 3.1, 3.2, 3.3, 3.4_

- [x] 6. Build horizontal item selector widget
  - Create scrollable horizontal list widget for category items
  - Implement smooth scroll animations and item selection feedback
  - Add lock indicators and rarity-based visual styling
  - Create item preview thumbnails with loading states
  - _Requirements: 1.1, 4.1, 4.4_

- [x] 7. Create preview mode UI components
  - Build preview mode overlay with controls and item selector
  - Implement purchase button with dynamic pricing and confirmation dialog
  - Add exit preview button with smooth transition back to normal mode
  - Create category switcher for different cosmetic types
  - _Requirements: 1.1, 1.5, 2.1, 2.2_

- [x] 8. Integrate preview system with existing shop screen
  - Modify existing shop screen to support preview mode entry
  - Add preview mode trigger on item tap with category detection
  - Implement state management integration between shop and preview modes
  - Create smooth UI transitions between normal and preview modes
  - _Requirements: 1.1, 1.5, 4.1_

- [x] 9. Implement purchase system integration
  - Extend existing purchase logic to work within preview mode
  - Add purchase confirmation with companion reaction integration
  - Implement purchase success/failure handling with appropriate animations
  - Create inventory update logic for newly purchased items
  - _Requirements: 2.1, 2.2, 2.3, 2.4_

- [x] 10. Add error handling and fallback systems
  - Implement Unity connection error handling with graceful fallbacks
  - Add asset loading error recovery with retry mechanisms
  - Create network error handling for purchase operations
  - Implement relationship data sync error handling
  - _Requirements: 1.1, 2.3, 4.1_

- [x] 11. Create comprehensive test suite
  - Write unit tests for preview state management and relationship filtering
  - Create integration tests for Unity communication and cosmetic application
  - Implement UI tests for preview mode interactions and purchase flows
  - Add performance tests for cosmetic switching and camera transitions
  - _Requirements: 1.3, 1.4, 2.1, 4.2, 4.3_

- [x] 12. Optimize performance and memory usage
  - Implement asset preloading and caching strategies
  - Add memory management for preview resources
  - Optimize cosmetic switching performance for sub-100ms response times
  - Create resource cleanup logic for preview mode exit
  - _Requirements: 4.2, 4.3, 4.4_