from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone
from django.utils.timezone import now
import uuid


class User(AbstractUser):
    """Extended user model for the AI companion app."""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    email = models.EmailField(unique=True)
    first_name = models.Char<PERSON>ield(max_length=30, blank=True)
    last_name = models.Char<PERSON>ield(max_length=30, blank=True)
    
    # Profile information
    date_of_birth = models.DateField(null=True, blank=True)
    user_timezone = models.CharField(max_length=50, default='UTC')
    timezone = property(lambda self: self.user_timezone, 
                       lambda self, value: setattr(self, 'user_timezone', value))
    preferred_language = models.CharField(max_length=10, default='en')
    ai_companion_name = models.CharField(max_length=50, default='Ella')
    bio = models.TextField(blank=True, default='')
    
    # Notification preferences
    enable_email_notifications = models.<PERSON><PERSON>anField(default=True)
    enable_push_notifications = models.BooleanField(default=True)
    enable_voice_responses = models.BooleanField(default=True)
    
    # AI companion preferences
    companion_personality = models.CharField(
        max_length=50, 
        choices=[
            ('supportive', 'Sweet & Supportive'),
            ('confident', 'Confident & Assertive'),
            ('playful', 'Playful & Fun'),
            ('intellectual', 'Intellectual & Thoughtful'),
            ('caring', 'Caring & Empathetic'),
        ],
        default='supportive'
    )
    personality_traits = models.JSONField(default=dict, blank=True, help_text="Key-value pairs of personality traits")
    
    # Voice preferences
    preferred_voice = models.CharField(
        max_length=50,
        choices=[
            ('alloy', 'Alloy'),
            ('echo', 'Echo'),
            ('fable', 'Fable'),
            ('onyx', 'Onyx'),
            ('nova', 'Nova'),
            ('shimmer', 'Shimmer'),
        ],
        default='nova'
    )
    
    # Privacy settings
    allow_data_collection = models.BooleanField(default=True)
    allow_personalization = models.BooleanField(default=True)
    allow_proactive_messages = models.BooleanField(default=True)
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_active = models.DateTimeField(default=now)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']
    
    class Meta:
        db_table = 'auth_user'
        verbose_name = 'User'
        verbose_name_plural = 'Users'
    
    def __str__(self):
        return self.email
    
    def get_full_name(self):
        return f"{self.first_name} {self.last_name}".strip() or self.username
    
    def update_last_active(self):
        """Update the last active timestamp."""
        self.last_active = timezone.now()
        self.save(update_fields=['last_active'])


class UserSession(models.Model):
    """Track user sessions for analytics and security."""
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='sessions')
    session_key = models.CharField(max_length=40, unique=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    device_type = models.CharField(
        max_length=20,
        choices=[
            ('mobile', 'Mobile'),
            ('tablet', 'Tablet'),
            ('desktop', 'Desktop'),
            ('unknown', 'Unknown'),
        ],
        default='unknown'
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    last_activity = models.DateTimeField(auto_now=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'user_sessions'
        ordering = ['-last_activity']
    
    def __str__(self):
        return f"{self.user.email} - {self.device_type} - {self.created_at}"



        from django.db import models
        from django.contrib.auth import get_user_model
        from django.core.validators import MinValueValidator, MaxValueValidator
        from django.utils import timezone
        from decimal import Decimal
        import uuid
        
        User = get_user_model()
        
        
        class UserLevel(models.Model):
            """User level and XP tracking"""
            user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='level')
            current_level = models.PositiveIntegerField(default=1)
            current_xp = models.PositiveIntegerField(default=0)
            total_xp = models.PositiveIntegerField(default=0)
            xp_to_next_level = models.PositiveIntegerField(default=100)
            created_at = models.DateTimeField(auto_now_add=True)
            updated_at = models.DateTimeField(auto_now=True)
            
            class Meta:
                db_table = 'gamification_user_level'
                verbose_name = 'User Level'
                verbose_name_plural = 'User Levels'
            
            def __str__(self):
                return f"{self.user.username} - Level {self.current_level}"
            
            def add_xp(self, amount):
                """Add XP and handle level ups"""
                self.current_xp += amount
                self.total_xp += amount
                
                # Check for level up
                while self.current_xp >= self.xp_to_next_level:
                    self.current_xp -= self.xp_to_next_level
                    self.current_level += 1
                    self.xp_to_next_level = self._calculate_xp_for_level(self.current_level + 1)
                
                self.save()
                return self.current_level
            
            def _calculate_xp_for_level(self, level):
                """Calculate XP required for a specific level"""
                return 100 * (level ** 1.5)  # Exponential scaling
        
        
        class Achievement(models.Model):
            """Achievement definitions"""
            CATEGORY_CHOICES = [
                ('chat', 'Chat'),
                ('memory', 'Memory'),
                ('social', 'Social'),
                ('milestone', 'Milestone'),
                ('special', 'Special'),
            ]
            
            RARITY_CHOICES = [
                ('common', 'Common'),
                ('rare', 'Rare'),
                ('epic', 'Epic'),
                ('legendary', 'Legendary'),
            ]
            
            id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
            name = models.CharField(max_length=100)
            description = models.TextField()
            category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
            rarity = models.CharField(max_length=20, choices=RARITY_CHOICES, default='common')
            icon = models.CharField(max_length=100, blank=True)  # Icon identifier
            xp_reward = models.PositiveIntegerField(default=0)
            currency_reward = models.PositiveIntegerField(default=0)
            requirement_type = models.CharField(max_length=50)  # e.g., 'message_count', 'days_active'
            requirement_value = models.PositiveIntegerField()  # Target value
            is_hidden = models.BooleanField(default=False)  # Hidden until unlocked
            is_active = models.BooleanField(default=True)
            created_at = models.DateTimeField(auto_now_add=True)
            
            class Meta:
                db_table = 'gamification_achievement'
                verbose_name = 'Achievement'
                verbose_name_plural = 'Achievements'
            
            def __str__(self):
                return self.name
        
        
        class UserAchievement(models.Model):
            """User's unlocked achievements"""
            user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='achievements')
            achievement = models.ForeignKey(Achievement, on_delete=models.CASCADE)
            unlocked_at = models.DateTimeField(auto_now_add=True)
            progress = models.PositiveIntegerField(default=0)  # Current progress towards achievement
            is_completed = models.BooleanField(default=False)
            
            class Meta:
                db_table = 'gamification_user_achievement'
                verbose_name = 'User Achievement'
                verbose_name_plural = 'User Achievements'
                unique_together = ['user', 'achievement']
            
            def __str__(self):
                return f"{self.user.username} - {self.achievement.name}"
            
            def update_progress(self, value):
                """Update achievement progress and check completion"""
                self.progress = value
                if self.progress >= self.achievement.requirement_value and not self.is_completed:
                    self.is_completed = True
                    self.unlocked_at = timezone.now()
                    # Award XP and currency
                    if hasattr(self.user, 'level'):
                        self.user.level.add_xp(self.achievement.xp_reward)
                    if hasattr(self.user, 'wallet'):
                        self.user.wallet.add_currency(self.achievement.currency_reward)
                self.save()
        
        
        class UserWallet(models.Model):
            """User's virtual currency wallet"""
            user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='wallet')
            balance = models.PositiveIntegerField(default=0)  # Virtual currency balance
            total_earned = models.PositiveIntegerField(default=0)
            total_spent = models.PositiveIntegerField(default=0)
            created_at = models.DateTimeField(auto_now_add=True)
            updated_at = models.DateTimeField(auto_now=True)
            
            class Meta:
                db_table = 'gamification_user_wallet'
                verbose_name = 'User Wallet'
                verbose_name_plural = 'User Wallets'
            
            def __str__(self):
                return f"{self.user.username} - {self.balance} coins"
            
            def add_currency(self, amount):
                """Add currency to wallet"""
                self.balance += amount
                self.total_earned += amount
                self.save()
            
            def spend_currency(self, amount):
                """Spend currency from wallet"""
                if self.balance >= amount:
                    self.balance -= amount
                    self.total_spent += amount
                    self.save()
                    return True
                return False
        
        
        class ShopItem(models.Model):
            """Shop items that users can purchase"""
            ITEM_TYPE_CHOICES = [
                ('avatar_customization', 'Avatar Customization'),
                ('chat_theme', 'Chat Theme'),
                ('voice_pack', 'Voice Pack'),
                ('emote', 'Emote'),
                ('badge', 'Badge'),
                ('boost', 'XP Boost'),
                ('special', 'Special Item'),
            ]
            
            RARITY_CHOICES = [
                ('common', 'Common'),
                ('rare', 'Rare'),
                ('epic', 'Epic'),
                ('legendary', 'Legendary'),
            ]
            
            id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
            name = models.CharField(max_length=100)
            description = models.TextField()
            item_type = models.CharField(max_length=30, choices=ITEM_TYPE_CHOICES)
            rarity = models.CharField(max_length=20, choices=RARITY_CHOICES, default='common')
            price = models.PositiveIntegerField()  # Price in virtual currency
            icon = models.CharField(max_length=100, blank=True)
            preview_image = models.CharField(max_length=200, blank=True)
            is_limited = models.BooleanField(default=False)
            stock_quantity = models.PositiveIntegerField(null=True, blank=True)  # For limited items
            level_requirement = models.PositiveIntegerField(default=1)
            is_active = models.BooleanField(default=True)
            created_at = models.DateTimeField(auto_now_add=True)
            
            class Meta:
                db_table = 'gamification_shop_item'
                verbose_name = 'Shop Item'
                verbose_name_plural = 'Shop Items'
            
            def __str__(self):
                return self.name
            
            def can_purchase(self, user):
                """Check if user can purchase this item"""
                if not self.is_active:
                    return False, "Item is not available"
                
                if hasattr(user, 'level') and user.level.current_level < self.level_requirement:
                    return False, f"Requires level {self.level_requirement}"
                
                if hasattr(user, 'wallet') and user.wallet.balance < self.price:
                    return False, "Insufficient funds"
                
                if self.is_limited and self.stock_quantity is not None and self.stock_quantity <= 0:
                    return False, "Out of stock"
                
                # Check if user already owns this item
                if self.user_inventories.filter(user=user).exists():
                    return False, "Already owned"
                
                return True, "Can purchase"
        
        
        class UserInventory(models.Model):
            """User's purchased items inventory"""
            user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='inventory')
            item = models.ForeignKey(ShopItem, on_delete=models.CASCADE, related_name='user_inventories')
            purchased_at = models.DateTimeField(auto_now_add=True)
            is_equipped = models.BooleanField(default=False)  # For customization items
            quantity = models.PositiveIntegerField(default=1)  # For stackable items
            
            class Meta:
                db_table = 'gamification_user_inventory'
                verbose_name = 'User Inventory'
                verbose_name_plural = 'User Inventories'
                unique_together = ['user', 'item']
            
            def __str__(self):
                return f"{self.user.username} - {self.item.name}"
        
        
        class DailyReward(models.Model):
            """Daily login rewards"""
            day = models.PositiveIntegerField(validators=[MinValueValidator(1), MaxValueValidator(30)])
            xp_reward = models.PositiveIntegerField(default=0)
            currency_reward = models.PositiveIntegerField(default=0)
            item_reward = models.ForeignKey(ShopItem, on_delete=models.SET_NULL, null=True, blank=True)
            is_bonus_day = models.BooleanField(default=False)  # Special rewards on certain days
            
            class Meta:
                db_table = 'gamification_daily_reward'
                verbose_name = 'Daily Reward'
                verbose_name_plural = 'Daily Rewards'
                unique_together = ['day']
            
            def __str__(self):
                return f"Day {self.day} Reward"
        
        
        class UserDailyStreak(models.Model):
            """User's daily login streak tracking"""
            user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='daily_streak')
            current_streak = models.PositiveIntegerField(default=0)
            longest_streak = models.PositiveIntegerField(default=0)
            last_login_date = models.DateField(null=True, blank=True)
            last_reward_claimed = models.DateField(null=True, blank=True)
            total_logins = models.PositiveIntegerField(default=0)
            created_at = models.DateTimeField(auto_now_add=True)
            updated_at = models.DateTimeField(auto_now=True)
            
            class Meta:
                db_table = 'gamification_user_daily_streak'
                verbose_name = 'User Daily Streak'
                verbose_name_plural = 'User Daily Streaks'
            
            def __str__(self):
                return f"{self.user.username} - {self.current_streak} day streak"
            
            def update_streak(self):
                """Update streak based on current date"""
                today = timezone.now().date()
                
                if self.last_login_date is None:
                    # First login
                    self.current_streak = 1
                    self.total_logins = 1
                elif self.last_login_date == today:
                    # Already logged in today
                    return self.current_streak
                elif self.last_login_date == today - timezone.timedelta(days=1):
                    # Consecutive day
                    self.current_streak += 1
                    self.total_logins += 1
                else:
                    # Streak broken
                    self.current_streak = 1
                    self.total_logins += 1
                
                if self.current_streak > self.longest_streak:
                    self.longest_streak = self.current_streak
                
                self.last_login_date = today
                self.save()
                return self.current_streak

                
                from django.db import models
                from django.conf import settings
                from django.utils import timezone
                import uuid
                import json
                
                
                class MemoryType(models.TextChoices):
                    """Types of memories that can be stored."""
                    SEMANTIC_PROFILE = 'semantic_profile', 'User-specific stable preferences and characteristics'
                    EPISODIC_SUMMARY = 'episodic_summary', 'Summaries of conversations or interactions'
                    GENERAL_KNOWLEDGE = 'general_knowledge', 'General facts or domain knowledge'
                    EXPLICIT_MEMORY = 'explicit_memory', 'Specific facts or notes explicitly saved'
                    PERSONAL_FACT = 'personal_fact', 'Personal information about the user'
                    PREFERENCE = 'preference', 'User preferences and choices'
                    GOAL = 'goal', 'User goals and aspirations'
                    RELATIONSHIP = 'relationship', 'Information about user relationships'
                    SKILL = 'skill', 'User skills and abilities'
                    INTEREST = 'interest', 'User interests and hobbies'
                
                
                class Memory(models.Model):
                    """Represents a memory stored in the vector database."""
                    
                    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
                    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='memories')
                    
                    # Memory content
                    content = models.TextField(help_text="The actual memory content/text")
                    memory_type = models.CharField(max_length=50, choices=MemoryType.choices)
                    
                    # Salience scores (0.0 to 1.0)
                    importance_score = models.FloatField(
                        default=0.5,
                        help_text="How crucial this memory is for future help (0.0-1.0)"
                    )
                    personalness_score = models.FloatField(
                        default=0.5,
                        help_text="How personal/specific to the user this memory is (0.0-1.0)"
                    )
                    actionability_score = models.FloatField(
                        default=0.5,
                        help_text="How actionable/task-related this memory is (0.0-1.0)"
                    )
                    
                    # Vector store reference
                    vector_id = models.CharField(
                        max_length=255,
                        unique=True,
                        help_text="ID of this memory in the vector store"
                    )
                    
                    # Metadata
                    source_conversation = models.ForeignKey(
                        'chat.Conversation',
                        on_delete=models.SET_NULL,
                        null=True,
                        blank=True,
                        help_text="Conversation this memory originated from"
                    )
                    source_message = models.ForeignKey(
                        'chat.Message',
                        on_delete=models.SET_NULL,
                        null=True,
                        blank=True,
                        help_text="Message this memory originated from"
                    )
                    
                    # Additional metadata as JSON
                    metadata = models.JSONField(
                        default=dict,
                        help_text="Additional metadata about this memory"
                    )
                    
                    # Timestamps
                    created_at = models.DateTimeField(auto_now_add=True)
                    updated_at = models.DateTimeField(auto_now=True)
                    last_accessed = models.DateTimeField(default=timezone.now)
                    
                    # Status
                    is_active = models.BooleanField(default=True)
                    is_verified = models.BooleanField(
                        default=False,
                        help_text="Whether this memory has been verified as accurate"
                    )
                    
                    class Meta:
                        db_table = 'memory_memories'
                        ordering = ['-created_at']
                        indexes = [
                            models.Index(fields=['user', 'memory_type']),
                            models.Index(fields=['user', 'importance_score']),
                            models.Index(fields=['user', 'is_active']),
                            models.Index(fields=['vector_id']),
                        ]
                    
                    def __str__(self):
                        return f"{self.user.email} - {self.memory_type} - {self.content[:50]}..."
                    
                    def update_access_time(self):
                        """Update the last accessed timestamp."""
                        self.last_accessed = timezone.now()
                        self.save(update_fields=['last_accessed'])
                    
                    def get_salience_score(self, weights=None):
                        """Calculate weighted salience score."""
                        if weights is None:
                            weights = {'importance': 0.5, 'personalness': 0.3, 'actionability': 0.2}
                        
                        return (
                            weights['importance'] * self.importance_score +
                            weights['personalness'] * self.personalness_score +
                            weights['actionability'] * self.actionability_score
                        )
                
                
                class MemoryRetrieval(models.Model):
                    """Track memory retrievals for analytics and optimization."""
                    
                    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
                    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
                    memory = models.ForeignKey(Memory, on_delete=models.CASCADE, related_name='retrievals')
                    
                    # Retrieval context
                    query = models.TextField(help_text="The query that retrieved this memory")
                    similarity_score = models.FloatField(
                        help_text="Similarity score from vector search (0.0-1.0)"
                    )
                    rank = models.IntegerField(
                        help_text="Rank of this memory in the retrieval results"
                    )
                    
                    # Context information
                    conversation = models.ForeignKey(
                        'chat.Conversation',
                        on_delete=models.SET_NULL,
                        null=True,
                        blank=True
                    )
                    
                    created_at = models.DateTimeField(auto_now_add=True)
                    
                    class Meta:
                        db_table = 'memory_retrievals'
                        ordering = ['-created_at']
                        indexes = [
                            models.Index(fields=['user', 'created_at']),
                            models.Index(fields=['memory', 'created_at']),
                        ]
                    
                    def __str__(self):
                        return f"{self.user.email} - {self.memory.memory_type} - Score: {self.similarity_score}"
                
                
                class MemoryCluster(models.Model):
                    """Groups related memories together for better organization."""
                    
                    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
                    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='memory_clusters')
                    
                    name = models.CharField(max_length=200)
                    description = models.TextField(blank=True)
                    
                    # Cluster metadata
                    cluster_type = models.CharField(
                        max_length=50,
                        choices=[
                            ('topic', 'Topic-based cluster'),
                            ('temporal', 'Time-based cluster'),
                            ('relationship', 'Relationship-based cluster'),
                            ('project', 'Project-based cluster'),
                        ],
                        default='topic'
                    )
                    
                    memories = models.ManyToManyField(Memory, related_name='clusters', blank=True)
                    
                    created_at = models.DateTimeField(auto_now_add=True)
                    updated_at = models.DateTimeField(auto_now=True)
                    
                    class Meta:
                        db_table = 'memory_clusters'
                        ordering = ['-updated_at']
                        unique_together = ['user', 'name']
                    
                    def __str__(self):
                        return f"{self.user.email} - {self.name}"
                    
                    def get_memory_count(self):
                        """Get count of memories in this cluster."""
                        return self.memories.count()
                
                
                class MemoryConfiguration(models.Model):
                    """User-specific memory system configuration."""
                    
                    user = models.OneToOneField(
                        settings.AUTH_USER_MODEL,
                        on_delete=models.CASCADE,
                        related_name='memory_config'
                    )
                    
                    # Storage thresholds
                    min_importance_threshold = models.FloatField(
                        default=0.3,
                        help_text="Minimum importance score to store a memory"
                    )
                    min_personalness_threshold = models.FloatField(
                        default=0.2,
                        help_text="Minimum personalness score to store a memory"
                    )
                    min_actionability_threshold = models.FloatField(
                        default=0.2,
                        help_text="Minimum actionability score to store a memory"
                    )
                    
                    # Retrieval settings
                    max_memories_per_retrieval = models.IntegerField(
                        default=5,
                        help_text="Maximum number of memories to retrieve per query"
                    )
                    similarity_threshold = models.FloatField(
                        default=0.7,
                        help_text="Minimum similarity score for memory retrieval"
                    )
                    
                    # Privacy settings
                    allow_memory_storage = models.BooleanField(
                        default=True,
                        help_text="Whether to store memories for this user"
                    )
                    allow_cross_conversation_memory = models.BooleanField(
                        default=True,
                        help_text="Whether to use memories from other conversations"
                    )
                    
                    # Automatic cleanup
                    auto_cleanup_enabled = models.BooleanField(
                        default=True,
                        help_text="Whether to automatically clean up old/irrelevant memories"
                    )
                    memory_retention_days = models.IntegerField(
                        default=365,
                        help_text="How long to keep memories (in days)"
                    )
                    
                    created_at = models.DateTimeField(auto_now_add=True)
                    updated_at = models.DateTimeField(auto_now=True)
                    
                    class Meta:
                        db_table = 'memory_configurations'
                    
                    def __str__(self):
                        return f"{self.user.email} - Memory Config"
                    
                    def should_store_memory(self, importance, personalness, actionability):
                        """Check if a memory meets the storage thresholds."""
                        return (
                            importance >= self.min_importance_threshold and
                            personalness >= self.min_personalness_threshold and
                            actionability >= self.min_actionability_threshold
                        )
                
                
                # Alias for backward compatibility
                UserMemoryConfig = MemoryConfiguration

                


                """
                Memory Manager for Django Real-time AI Companion.
                Adapted from kd_assistant_langgraph implementation for Django integration.
                
                High-level interface for saving and retrieving long-term memory (RAG).
                """
                import logging
                import uuid
                import datetime
                import os
                from typing import List, Dict, Any, Optional
                from pathlib import Path
                from django.conf import settings
                
                from langchain_chroma import Chroma
                from langchain_openai import OpenAIEmbeddings
                
                logger = logging.getLogger(__name__)
                
                
                class MemoryManager:
                    """
                    Manages persistent memory storage and retrieval using a Vector Store.
                    Handles storage and retrieval of various forms of persistent knowledge.
                    Django-integrated version of the kd_assistant_langgraph MemoryManager.
                    """
                
                    def __init__(
                        self,
                        persist_directory: Optional[str] = None,
                        collection_name: str = "kd_memory",
                        embedding_model: str = "text-embedding-3-small",
                    ):
                        """
                        Initialize the memory manager with vector store.
                        """
                        if not persist_directory:
                            # Use Django's MEDIA_ROOT or a subdirectory for vector store
                            if hasattr(settings, 'MEDIA_ROOT'):
                                persist_directory = os.path.join(settings.MEDIA_ROOT, "vector_store_db")
                            else:
                                # Fallback to project root
                                persist_directory = os.path.join(settings.BASE_DIR, "media", "vector_store_db")
                
                        self.persist_directory = persist_directory
                        self.collection_name = collection_name
                
                        # Create a custom embedding function with caching
                        self.embedding_model = embedding_model
                        self.embeddings = OpenAIEmbeddings(
                            model=embedding_model,
                            timeout=7,  # Shorter timeout for faster failure
                            max_retries=1,  # Fewer retries for speed
                            openai_api_key=settings.OPENAI_API_KEY
                        )
                
                        # Initialize the vector store
                        try:
                            # Ensure directory exists
                            os.makedirs(persist_directory, exist_ok=True)
                            
                            # First try to load existing collection
                            self.vectorstore = Chroma(
                                collection_name=collection_name,
                                embedding_function=self.embeddings,
                                persist_directory=persist_directory,
                                collection_metadata={
                                    "hnsw:space": "cosine"
                                },  # Ensure cosine distance for OpenAI embeddings
                            )
                            collection_count = self.vectorstore._collection.count()
                            logger.info(
                                f"Memory Manager initialized with {collection_count} existing documents in collection '{collection_name}' at '{persist_directory}'"
                            )
                        except Exception as e:
                            logger.error(
                                f"Error initializing ChromaDB: {e}. Creating a new collection."
                            )
                            # If loading fails, create a new collection
                            os.makedirs(persist_directory, exist_ok=True)
                            self.vectorstore = Chroma(
                                collection_name=collection_name,
                                embedding_function=self.embeddings,
                                persist_directory=persist_directory,
                                collection_metadata={
                                    "hnsw:space": "cosine"
                                },  # Ensure cosine distance for OpenAI embeddings
                            )
                            logger.info(
                                f"Created new ChromaDB collection '{collection_name}' at '{persist_directory}' with cosine distance."
                            )
                
                        # Refined memory types
                        self.memory_types = {
                            "semantic_profile": "User-specific stable preferences, biographical details, and core characteristics.",
                            "episodic_summary": "Summaries of conversations or significant interaction chunks.",
                            "general_knowledge": "General facts or domain knowledge learned or taught, not specific to a user.",
                            "explicit_memory": "Specific facts or notes explicitly saved by the user or agent.",
                            "task_related": "Information directly relevant to an ongoing or future task."
                        }
                
                        # Short-term memory for conversation context (primarily managed by LangGraph state)
                        self.short_term_memory = []
                        self.max_short_term_items = 20
                
                    def store_memory(
                        self,
                        text: str,
                        memory_type: str,
                        metadata: Dict[str, Any] = None,
                        importance_score: Optional[float] = None,
                        personalness_score: Optional[float] = None,
                        actionability_score: Optional[float] = None,
                        user_id: Optional[str] = None,
                    ) -> str:
                        """
                        Store a memory in the vector database with salience scores.
                        """
                        if not text or not text.strip():
                            logger.warning("Attempted to store empty memory text")
                            return ""
                
                        if memory_type not in self.memory_types:
                            logger.warning(
                                f"Unknown memory type: {memory_type}, defaulting to 'explicit_memory'"
                            )
                            memory_type = "explicit_memory"
                
                        memory_id = str(uuid.uuid4())
                        timestamp = datetime.datetime.now().isoformat()
                
                        full_metadata = {
                            "memory_id": memory_id,
                            "memory_type": memory_type,
                            "timestamp": timestamp,
                        }
                
                        if user_id:
                            full_metadata["user_id"] = user_id
                
                        if importance_score is not None:
                            full_metadata["importance_score"] = importance_score
                        if personalness_score is not None:
                            full_metadata["personalness_score"] = personalness_score
                        if actionability_score is not None:
                            full_metadata["actionability_score"] = actionability_score
                
                        if metadata:
                            # Ensure provided metadata doesn't overwrite critical fields unless intended
                            for key, value in metadata.items():
                                if key not in ["memory_id", "timestamp"]:
                                    full_metadata[key] = value
                                elif key in full_metadata and full_metadata[key] != value:
                                    logger.warning(
                                        f"Metadata key '{key}' conflicts with auto-generated value. Keeping auto-generated."
                                    )
                
                        try:
                            ids = self.vectorstore.add_texts(
                                texts=[text], metadatas=[full_metadata], ids=[memory_id]
                            )
                
                            if ids:
                                logger.info(
                                    f"Stored {memory_type} memory with ID: {memory_id}. Scores: Imp={importance_score}, Per={personalness_score}, Act={actionability_score}"
                                )
                                return memory_id
                            else:
                                logger.error(
                                    "Failed to store memory, no ID returned from vector store."
                                )
                                return ""
                        except Exception as e:
                            logger.error(f"Error storing memory: {e}", exc_info=True)
                            return ""
                
                    def search_memories(
                        self,
                        query: str,
                        user_id: Optional[str] = None,
                        memory_type: Optional[str] = None,
                        k: int = 3,
                        min_importance: float = 0.3,
                        min_personalness: Optional[float] = None,
                        min_actionability: Optional[float] = None,
                        timeout: int = 5,
                    ) -> List[Dict[str, Any]]:
                        """
                        Search for memories similar to the query.
                        """
                        try:
                            # ChromaDB filter
                            chroma_filter = {}
                            if user_id:
                                chroma_filter["user_id"] = user_id
                            if memory_type:
                                chroma_filter["memory_type"] = memory_type
                
                            # Get results with relevance scores
                            results = self.vectorstore.similarity_search_with_relevance_scores(
                                query=query,
                                k=k * 2,  # Get more results initially to filter
                                filter=chroma_filter if chroma_filter else None,
                            )
                
                            # Process results and apply additional filters
                            processed_results = []
                            for doc, score in results:
                                doc_importance = float(
                                    doc.metadata.get("importance_score", 0)
                                )
                                doc_personalness = float(
                                    doc.metadata.get("personalness_score", 0)
                                )
                                doc_actionability = float(
                                    doc.metadata.get("actionability_score", 0)
                                )
                
                                # Apply filters
                                if min_importance > 0 and doc_importance < min_importance:
                                    continue
                                if min_personalness and doc_personalness < min_personalness:
                                    continue
                                if min_actionability and doc_actionability < min_actionability:
                                    continue
                
                                processed_results.append({
                                    "text": doc.page_content,
                                    "metadata": doc.metadata,
                                    "relevance": score,
                                })
                
                            # Sort by relevance (higher scores are better for cosine similarity)
                            processed_results.sort(key=lambda x: x["relevance"], reverse=True)
                            logger.info(f"Found {len(processed_results)} memories matching query")
                
                            return processed_results[:k]
                        except Exception as e:
                            logger.error(f"Error searching memories: {e}")
                            return []
                
                    def get_all_user_memories(
                        self, user_id: str, limit: int = 10
                    ) -> List[Dict[str, Any]]:
                        """
                        Get all memories for a specific user, sorted by timestamp.
                        """
                        try:
                            # Use a broad query to get all user memories
                            results = self.vectorstore.similarity_search(
                                query="user memories profile interests hobbies",
                                k=limit * 2,  # Get more than needed to filter
                                filter={"user_id": user_id}
                            )
                            
                            # Convert to our standard format
                            processed_results = []
                            for doc in results:
                                processed_results.append({
                                    "text": doc.page_content,
                                    "metadata": doc.metadata,
                                })
                            
                            # Sort by timestamp if available
                            processed_results.sort(
                                key=lambda x: x["metadata"].get("timestamp", ""), 
                                reverse=True
                            )
                            
                            return processed_results[:limit]
                            
                        except Exception as e:
                            logger.error(f"Error getting user memories: {e}")
                            return []
                
                    def get_memory_stats(self) -> Dict[str, Any]:
                        """
                        Get statistics about the memories stored.
                        """
                        try:
                            stats = {
                                "total_count": self.vectorstore._collection.count(),
                                "by_type": {mem_type: 0 for mem_type in self.memory_types.keys()},
                                "by_user": {},
                            }
                            return stats
                        except Exception as e:
                            logger.error(f"Error getting memory stats: {e}")
                            return {"total_count": 0, "by_type": {}, "by_user": {}}


                            from django.db import models
                            from django.conf import settings
                            from django.utils import timezone
                            import uuid
                            import json
                            
                            # Import real-time models
                            from .models_realtime import (
                                UserRelationship,
                                EmotionContext,
                                StreamingSession,
                                PerformanceMetrics
                            )
                            
                            
                            class Conversation(models.Model):
                                """Represents a conversation thread between user and AI companion."""
                                
                                id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
                                user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='conversations')
                                title = models.CharField(max_length=200, blank=True)
                                
                                # Conversation metadata
                                is_active = models.BooleanField(default=True)
                                is_archived = models.BooleanField(default=False)
                                
                                # Timestamps
                                created_at = models.DateTimeField(auto_now_add=True)
                                updated_at = models.DateTimeField(auto_now=True)
                                last_message_at = models.DateTimeField(default=timezone.now)
                                
                                class Meta:
                                    db_table = 'chat_conversations'
                                    ordering = ['-last_message_at']
                                    indexes = [
                                        models.Index(fields=['user', '-last_message_at']),
                                        models.Index(fields=['user', 'is_active']),
                                    ]
                                
                                def __str__(self):
                                    return f"{self.user.email} - {self.title or 'Untitled'}"
                                
                                def update_last_message_time(self):
                                    """Update the last message timestamp."""
                                    self.last_message_at = timezone.now()
                                    self.save(update_fields=['last_message_at'])
                                
                                def get_message_count(self):
                                    """Get total message count in this conversation."""
                                    return self.messages.count()
                                
                                def get_recent_messages(self, limit=10):
                                    """Get recent messages from this conversation."""
                                    return self.messages.order_by('-created_at')[:limit]
                            
                            
                            class Message(models.Model):
                                """Represents a single message in a conversation."""
                                
                                MESSAGE_TYPES = [
                                    ('text', 'Text'),
                                    ('audio', 'Audio'),
                                    ('image', 'Image'),
                                    ('system', 'System'),
                                ]
                                
                                SENDER_TYPES = [
                                    ('user', 'User'),
                                    ('assistant', 'AI Assistant'),
                                    ('system', 'System'),
                                ]
                                
                                id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
                                conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages')
                                
                                # Message content
                                sender_type = models.CharField(max_length=20, choices=SENDER_TYPES)
                                message_type = models.CharField(max_length=20, choices=MESSAGE_TYPES, default='text')
                                content = models.TextField()
                                
                                # Audio-specific fields
                                audio_file = models.FileField(upload_to='audio/messages/', null=True, blank=True)
                                audio_duration = models.FloatField(null=True, blank=True)  # in seconds
                                transcription = models.TextField(blank=True)  # For audio messages
                                
                                # AI processing metadata
                                processing_time = models.FloatField(null=True, blank=True)  # in seconds
                                model_used = models.CharField(max_length=100, blank=True)  # e.g., 'gpt-4', 'whisper-1'
                                tokens_used = models.IntegerField(null=True, blank=True)
                                
                                # Message status
                                is_edited = models.BooleanField(default=False)
                                is_deleted = models.BooleanField(default=False)
                                
                                # Timestamps
                                created_at = models.DateTimeField(auto_now_add=True)
                                updated_at = models.DateTimeField(auto_now=True)
                                
                                class Meta:
                                    db_table = 'chat_messages'
                                    ordering = ['created_at']
                                    indexes = [
                                        models.Index(fields=['conversation', 'created_at']),
                                        models.Index(fields=['conversation', 'sender_type']),
                                    ]
                                
                                def __str__(self):
                                    return f"{self.sender_type}: {self.content[:50]}..."
                                
                                def save(self, *args, **kwargs):
                                    super().save(*args, **kwargs)
                                    # Update conversation's last message time
                                    self.conversation.update_last_message_time()
                            
                            
                            class MessageReaction(models.Model):
                                """User reactions to AI messages (like, dislike, etc.)."""
                                
                                REACTION_TYPES = [
                                    ('like', 'Like'),
                                    ('dislike', 'Dislike'),
                                    ('love', 'Love'),
                                    ('helpful', 'Helpful'),
                                    ('not_helpful', 'Not Helpful'),
                                ]
                                
                                id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
                                message = models.ForeignKey(Message, on_delete=models.CASCADE, related_name='reactions')
                                user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
                                reaction_type = models.CharField(max_length=20, choices=REACTION_TYPES)
                                
                                created_at = models.DateTimeField(auto_now_add=True)
                                
                                class Meta:
                                    db_table = 'chat_message_reactions'
                                    unique_together = ['message', 'user']  # One reaction per user per message
                                
                                def __str__(self):
                                    return f"{self.user.email} - {self.reaction_type} - {self.message.id}"
                            
                            
                            class ChatSession(models.Model):
                                """Represents an active chat session (WebSocket connection)."""
                                
                                id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
                                user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='chat_sessions')
                                conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='sessions', null=True, blank=True)
                                
                                # Session metadata
                                channel_name = models.CharField(max_length=255, unique=True)  # WebSocket channel name
                                is_active = models.BooleanField(default=True)
                                
                                # Connection info
                                ip_address = models.GenericIPAddressField(null=True, blank=True)
                                user_agent = models.TextField(blank=True)
                                
                                # Timestamps
                                connected_at = models.DateTimeField(auto_now_add=True)
                                last_activity = models.DateTimeField(auto_now=True)
                                disconnected_at = models.DateTimeField(null=True, blank=True)
                                
                                class Meta:
                                    db_table = 'chat_sessions'
                                    ordering = ['-connected_at']
                                    indexes = [
                                        models.Index(fields=['user', 'is_active']),
                                        models.Index(fields=['channel_name']),
                                    ]
                                
                                def __str__(self):
                                    return f"{self.user.email} - {self.channel_name}"
                                
                                def disconnect(self):
                                    """Mark session as disconnected."""
                                    self.is_active = False
                                    self.disconnected_at = timezone.now()
                                    self.save(update_fields=['is_active', 'disconnected_at'])
                            
                            
                            class VoiceSettings(models.Model):
                                """User-specific voice settings for TTS."""
                                
                                user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='voice_settings')
                                
                                # TTS Settings
                                voice_model = models.CharField(max_length=50, default='tts-1')
                                voice_name = models.CharField(max_length=50, default='nova')
                                speech_speed = models.FloatField(default=1.0)  # 0.25 to 4.0
                                
                                # STT Settings
                                language = models.CharField(max_length=10, default='en')
                                auto_transcribe = models.BooleanField(default=True)
                                
                                created_at = models.DateTimeField(auto_now_add=True)
                                updated_at = models.DateTimeField(auto_now=True)
                                
                                class Meta:
                                    db_table = 'chat_voice_settings'
                                
                                def __str__(self):
                                    return f"{self.user.email} - {self.voice_name}"

                                    """
                                    Real-time AI companion models for emotion tracking, relationship management, and streaming sessions.
                                    """
                                    from django.db import models
                                    from django.conf import settings
                                    from django.utils import timezone
                                    import uuid
                                    import json
                                    
                                    
                                    class UserRelationship(models.Model):
                                        """Tracks the relationship development between user and AI companion."""
                                        
                                        RELATIONSHIP_LEVELS = [
                                            (1, 'Acquaintance - Basic conversation, general topics'),
                                            (2, 'Friend - Personal topics, mild flirtation allowed'),
                                            (3, 'Close Friend - Intimate conversation, emotional support'),
                                            (4, 'Intimate - Adult content with explicit consent'),
                                        ]
                                        
                                        user = models.OneToOneField(
                                            settings.AUTH_USER_MODEL, 
                                            on_delete=models.CASCADE, 
                                            related_name='ai_relationship'
                                        )
                                        
                                        # Relationship progression
                                        relationship_level = models.IntegerField(
                                            choices=RELATIONSHIP_LEVELS,
                                            default=1,
                                            help_text="Current relationship level (1-4)"
                                        )
                                        
                                        # Interaction metrics
                                        total_interactions = models.IntegerField(
                                            default=0,
                                            help_text="Total number of interactions with AI companion"
                                        )
                                        total_conversation_time = models.DurationField(
                                            default=timezone.timedelta(0),
                                            help_text="Total time spent in conversations"
                                        )
                                        
                                        # Emotional metrics
                                        emotional_intimacy_score = models.FloatField(
                                            default=0.0,
                                            help_text="Calculated emotional intimacy score (0.0-1.0)"
                                        )
                                        average_emotion_intensity = models.FloatField(
                                            default=0.0,
                                            help_text="Average intensity of emotions expressed (0.0-1.0)"
                                        )
                                        
                                        # Progression tracking
                                        progression_milestones = models.JSONField(
                                            default=dict,
                                            help_text="Milestones achieved in relationship progression"
                                        )
                                        last_milestone_date = models.DateTimeField(
                                            null=True,
                                            blank=True,
                                            help_text="Date of last milestone achievement"
                                        )
                                        
                                        # User consent and preferences
                                        user_consent_flags = models.JSONField(
                                            default=dict,
                                            help_text="User consent for different types of content"
                                        )
                                        explicit_progression_request = models.BooleanField(
                                            default=False,
                                            help_text="User has explicitly requested relationship progression"
                                        )
                                        
                                        # Timestamps
                                        relationship_started = models.DateTimeField(auto_now_add=True)
                                        last_interaction = models.DateTimeField(auto_now=True)
                                        last_level_change = models.DateTimeField(auto_now_add=True)
                                        
                                        class Meta:
                                            db_table = 'chat_user_relationships'
                                            verbose_name = 'User AI Relationship'
                                            verbose_name_plural = 'User AI Relationships'
                                        
                                        def __str__(self):
                                            return f"{self.user.email} - Level {self.relationship_level}"
                                        
                                        def can_access_content_level(self, content_level):
                                            """Check if user can access content at given level."""
                                            return self.relationship_level >= content_level
                                        
                                        def update_interaction_metrics(self, conversation_duration=None, emotion_intensity=None):
                                            """Update interaction metrics after a conversation."""
                                            self.total_interactions += 1
                                            
                                            if conversation_duration:
                                                self.total_conversation_time += conversation_duration
                                            
                                            if emotion_intensity is not None:
                                                # Update running average of emotion intensity
                                                current_avg = self.average_emotion_intensity
                                                total = self.total_interactions
                                                self.average_emotion_intensity = ((current_avg * (total - 1)) + emotion_intensity) / total
                                                
                                                # Update emotional intimacy score based on emotion intensity
                                                intimacy_boost = 0.0
                                                if emotion_intensity > 0.7:
                                                    intimacy_boost = 0.05
                                                elif emotion_intensity > 0.5:
                                                    intimacy_boost = 0.03
                                                elif emotion_intensity > 0.3:
                                                    intimacy_boost = 0.02
                                                
                                                self.emotional_intimacy_score = min(1.0, self.emotional_intimacy_score + intimacy_boost)
                                            
                                            self.save(update_fields=[
                                                'total_interactions', 
                                                'total_conversation_time', 
                                                'average_emotion_intensity',
                                                'emotional_intimacy_score',
                                                'last_interaction'
                                            ])
                                        
                                        def check_progression_eligibility(self):
                                            """Check if user is eligible for relationship level progression."""
                                            if self.relationship_level >= 4:
                                                return False, "Already at maximum relationship level"
                                            
                                            # Define progression criteria
                                            criteria = {
                                                2: {  # Friend level
                                                    'min_interactions': 10,
                                                    'min_conversation_time': timezone.timedelta(hours=2),
                                                    'min_emotional_intimacy': 0.3,
                                                },
                                                3: {  # Close friend level
                                                    'min_interactions': 50,
                                                    'min_conversation_time': timezone.timedelta(hours=10),
                                                    'min_emotional_intimacy': 0.6,
                                                },
                                                4: {  # Intimate level
                                                    'min_interactions': 100,
                                                    'min_conversation_time': timezone.timedelta(hours=25),
                                                    'min_emotional_intimacy': 0.8,
                                                    'explicit_consent_required': True,
                                                }
                                            }
                                            
                                            next_level = self.relationship_level + 1
                                            if next_level not in criteria:
                                                return False, "Invalid progression level"
                                            
                                            reqs = criteria[next_level]
                                            
                                            # Check each requirement
                                            if self.total_interactions < reqs['min_interactions']:
                                                return False, f"Need {reqs['min_interactions']} interactions (have {self.total_interactions})"
                                            
                                            if self.total_conversation_time < reqs['min_conversation_time']:
                                                return False, f"Need {reqs['min_conversation_time']} conversation time"
                                            
                                            if self.emotional_intimacy_score < reqs['min_emotional_intimacy']:
                                                return False, f"Need emotional intimacy score of {reqs['min_emotional_intimacy']}"
                                            
                                            if reqs.get('explicit_consent_required'):
                                                if not self.explicit_progression_request:
                                                    return False, "Explicit user consent required for this level"
                                                if not self.user_consent_flags.get('adult_content_consent', False):
                                                    return False, "Adult content consent required for this level"
                                            
                                            return True, "Eligible for progression"
                                        
                                        def progress_relationship(self, force=False):
                                            """Progress to next relationship level if eligible."""
                                            if not force:
                                                eligible, reason = self.check_progression_eligibility()
                                                if not eligible:
                                                    return False, reason
                                            
                                            if self.relationship_level >= 4:
                                                return False, "Already at maximum level"
                                            
                                            old_level = self.relationship_level
                                            self.relationship_level += 1
                                            self.last_level_change = timezone.now()
                                            
                                            # Record milestone
                                            milestone_key = f"level_{self.relationship_level}_achieved"
                                            self.progression_milestones[milestone_key] = timezone.now().isoformat()
                                            self.last_milestone_date = timezone.now()
                                            
                                            self.save(update_fields=[
                                                'relationship_level', 
                                                'last_level_change', 
                                                'progression_milestones',
                                                'last_milestone_date'
                                            ])
                                            
                                            return True, f"Progressed from level {old_level} to {self.relationship_level}"
                                    
                                    
                                    class EmotionContext(models.Model):
                                        """Stores emotion analysis results for user interactions."""
                                        
                                        EMOTION_SOURCES = [
                                            ('audio', 'Audio emotion detection'),
                                            ('text', 'Text sentiment analysis'),
                                            ('combined', 'Combined audio and text analysis'),
                                            ('historical', 'Historical emotion pattern'),
                                        ]
                                        
                                        id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
                                        user = models.ForeignKey(
                                            settings.AUTH_USER_MODEL, 
                                            on_delete=models.CASCADE, 
                                            related_name='emotion_contexts'
                                        )
                                        
                                        # Session and conversation context
                                        session_id = models.CharField(
                                            max_length=255,
                                            help_text="WebSocket session ID"
                                        )
                                        conversation = models.ForeignKey(
                                            'chat.Conversation',
                                            on_delete=models.CASCADE,
                                            related_name='emotion_contexts',
                                            null=True,
                                            blank=True
                                        )
                                        message = models.ForeignKey(
                                            'chat.Message',
                                            on_delete=models.CASCADE,
                                            related_name='emotion_contexts',
                                            null=True,
                                            blank=True
                                        )
                                        
                                        # Emotion data
                                        emotion_source = models.CharField(
                                            max_length=20,
                                            choices=EMOTION_SOURCES,
                                            default='combined'
                                        )
                                        
                                        # Raw emotion scores from Hume API
                                        audio_emotions = models.JSONField(
                                            default=dict,
                                            help_text="Raw emotion scores from audio analysis"
                                        )
                                        text_emotions = models.JSONField(
                                            default=dict,
                                            help_text="Raw emotion scores from text analysis"
                                        )
                                        
                                        # Processed emotion context
                                        primary_emotion = models.CharField(
                                            max_length=50,
                                            help_text="Primary detected emotion"
                                        )
                                        emotion_intensity = models.FloatField(
                                            default=0.0,
                                            help_text="Overall emotion intensity (0.0-1.0)"
                                        )
                                        emotion_valence = models.FloatField(
                                            default=0.0,
                                            help_text="Emotion valence: negative (-1.0) to positive (1.0)"
                                        )
                                        emotion_arousal = models.FloatField(
                                            default=0.0,
                                            help_text="Emotion arousal: calm (0.0) to excited (1.0)"
                                        )
                                        
                                        # Context-aware emotions (processed for AI response)
                                        context_emotions = models.JSONField(
                                            default=dict,
                                            help_text="Processed emotions for AI context"
                                        )
                                        
                                        # Confidence and quality metrics
                                        confidence_score = models.FloatField(
                                            default=0.0,
                                            help_text="Confidence in emotion detection (0.0-1.0)"
                                        )
                                        audio_quality_score = models.FloatField(
                                            null=True,
                                            blank=True,
                                            help_text="Audio quality score for emotion detection"
                                        )
                                        
                                        # Processing metadata
                                        processing_time_ms = models.IntegerField(
                                            default=0,
                                            help_text="Time taken to process emotions (milliseconds)"
                                        )
                                        hume_request_id = models.CharField(
                                            max_length=255,
                                            blank=True,
                                            help_text="Hume API request ID for debugging"
                                        )
                                        
                                        created_at = models.DateTimeField(auto_now_add=True)
                                        
                                        class Meta:
                                            db_table = 'chat_emotion_contexts'
                                            ordering = ['-created_at']
                                            indexes = [
                                                models.Index(fields=['user', 'session_id']),
                                                models.Index(fields=['user', 'created_at']),
                                                models.Index(fields=['session_id', 'created_at']),
                                                models.Index(fields=['primary_emotion']),
                                            ]
                                        
                                        def __str__(self):
                                            return f"{self.user.email} - {self.primary_emotion} ({self.emotion_intensity:.2f})"
                                        
                                        def get_top_emotions(self, n=3, source='combined'):
                                            """Get top N emotions from specified source."""
                                            if source == 'audio' and self.audio_emotions:
                                                emotions = self.audio_emotions
                                            elif source == 'text' and self.text_emotions:
                                                emotions = self.text_emotions
                                            elif source == 'combined' and self.context_emotions:
                                                emotions = self.context_emotions
                                            else:
                                                return []
                                            
                                            # Sort emotions by score and return top N
                                            if isinstance(emotions, dict):
                                                sorted_emotions = sorted(emotions.items(), key=lambda x: x[1], reverse=True)
                                                return sorted_emotions[:n]
                                            
                                            return []
                                        
                                        def update_relationship_metrics(self):
                                            """Update user relationship metrics based on this emotion context."""
                                            try:
                                                relationship = self.user.ai_relationship
                                                
                                                # Update emotional intimacy score based on emotion intensity and type
                                                intimacy_boost = 0.0
                                                
                                                # Higher intensity emotions contribute more to intimacy
                                                if self.emotion_intensity > 0.7:
                                                    intimacy_boost += 0.02
                                                elif self.emotion_intensity > 0.5:
                                                    intimacy_boost += 0.01
                                                
                                                # Certain emotions contribute more to intimacy
                                                intimate_emotions = ['love', 'affection', 'trust', 'vulnerability', 'sadness', 'joy']
                                                if self.primary_emotion.lower() in intimate_emotions:
                                                    intimacy_boost += 0.01
                                                
                                                # Update the relationship's emotional intimacy score
                                                current_score = relationship.emotional_intimacy_score
                                                new_score = min(1.0, current_score + intimacy_boost)
                                                relationship.emotional_intimacy_score = new_score
                                                relationship.save(update_fields=['emotional_intimacy_score'])
                                                
                                            except UserRelationship.DoesNotExist:
                                                # Create relationship if it doesn't exist
                                                UserRelationship.objects.create(user=self.user)
                                    
                                    
                                    class StreamingSession(models.Model):
                                        """Tracks active WebSocket streaming sessions for real-time AI companion."""
                                        
                                        SESSION_TYPES = [
                                            ('voice', 'Voice conversation'),
                                            ('text', 'Text conversation'),
                                            ('mixed', 'Mixed voice and text'),
                                        ]
                                        
                                        SESSION_STATUS = [
                                            ('connecting', 'Connecting'),
                                            ('active', 'Active'),
                                            ('idle', 'Idle'),
                                            ('disconnected', 'Disconnected'),
                                            ('error', 'Error'),
                                        ]
                                        
                                        id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
                                        user = models.ForeignKey(
                                            settings.AUTH_USER_MODEL, 
                                            on_delete=models.CASCADE, 
                                            related_name='streaming_sessions'
                                        )
                                        
                                        # Session identification
                                        session_id = models.CharField(
                                            max_length=255, 
                                            unique=True,
                                            help_text="Unique session identifier"
                                        )
                                        websocket_id = models.CharField(
                                            max_length=255,
                                            help_text="WebSocket connection identifier"
                                        )
                                        
                                        # Session metadata
                                        session_type = models.CharField(
                                            max_length=20,
                                            choices=SESSION_TYPES,
                                            default='mixed'
                                        )
                                        status = models.CharField(
                                            max_length=20,
                                            choices=SESSION_STATUS,
                                            default='connecting'
                                        )
                                        
                                        # Connection information
                                        ip_address = models.GenericIPAddressField(null=True, blank=True)
                                        user_agent = models.TextField(blank=True)
                                        device_info = models.JSONField(
                                            default=dict,
                                            help_text="Device and browser information"
                                        )
                                        
                                        # Session activity
                                        started_at = models.DateTimeField(auto_now_add=True)
                                        last_activity = models.DateTimeField(auto_now=True)
                                        ended_at = models.DateTimeField(null=True, blank=True)
                                        is_active = models.BooleanField(default=True)
                                        
                                        # Performance metrics
                                        performance_metrics = models.JSONField(
                                            default=dict,
                                            help_text="Real-time performance metrics for this session"
                                        )
                                        
                                        # Audio/voice specific
                                        audio_enabled = models.BooleanField(default=False)
                                        voice_settings = models.JSONField(
                                            default=dict,
                                            help_text="Voice settings for this session"
                                        )
                                        
                                        # Error tracking
                                        error_count = models.IntegerField(default=0)
                                        last_error = models.TextField(blank=True)
                                        last_error_at = models.DateTimeField(null=True, blank=True)
                                        
                                        class Meta:
                                            db_table = 'chat_streaming_sessions'
                                            ordering = ['-started_at']
                                            indexes = [
                                                models.Index(fields=['user', 'is_active']),
                                                models.Index(fields=['session_id']),
                                                models.Index(fields=['websocket_id']),
                                                models.Index(fields=['status', 'started_at']),
                                            ]
                                        
                                        def __str__(self):
                                            return f"{self.user.email} - {self.session_type} - {self.status}"
                                        
                                        def update_activity(self):
                                            """Update last activity timestamp."""
                                            self.last_activity = timezone.now()
                                            self.save(update_fields=['last_activity'])
                                        
                                        def add_performance_metric(self, metric_name, value, timestamp=None):
                                            """Add a performance metric to this session."""
                                            if timestamp is None:
                                                timestamp = timezone.now().isoformat()
                                            
                                            if 'metrics' not in self.performance_metrics:
                                                self.performance_metrics['metrics'] = []
                                            
                                            self.performance_metrics['metrics'].append({
                                                'name': metric_name,
                                                'value': value,
                                                'timestamp': timestamp
                                            })
                                            
                                            # Keep only last 100 metrics to prevent unbounded growth
                                            if len(self.performance_metrics['metrics']) > 100:
                                                self.performance_metrics['metrics'] = self.performance_metrics['metrics'][-100:]
                                            
                                            # Save with async context handling
                                            try:
                                                from asgiref.sync import sync_to_async
                                                import asyncio
                                                
                                                def _save_metrics():
                                                    self.save(update_fields=['performance_metrics'])
                                                
                                                # Check if we're in an async context
                                                try:
                                                    loop = asyncio.get_running_loop()
                                                    # We're in an async context, schedule the sync operation
                                                    asyncio.create_task(sync_to_async(_save_metrics)())
                                                except RuntimeError:
                                                    # We're not in an async context, run directly
                                                    _save_metrics()
                                            except Exception as e:
                                                import logging
                                                logger = logging.getLogger(__name__)
                                                logger.error(f"Error saving performance metrics: {e}")
                                        
                                        def get_average_response_time(self):
                                            """Calculate average response time for this session."""
                                            metrics = self.performance_metrics.get('metrics', [])
                                            response_times = [m['value'] for m in metrics if m['name'] == 'total_response_time']
                                            
                                            if not response_times:
                                                return None
                                            
                                            return sum(response_times) / len(response_times)
                                        
                                        def record_error(self, error_message):
                                            """Record an error for this session."""
                                            self.error_count += 1
                                            self.last_error = error_message
                                            self.last_error_at = timezone.now()
                                            self.save(update_fields=['error_count', 'last_error', 'last_error_at'])
                                        
                                        def end_session(self, reason='user_disconnect'):
                                            """End the streaming session."""
                                            self.is_active = False
                                            self.status = 'disconnected'
                                            self.ended_at = timezone.now()
                                            
                                            # Record session duration in performance metrics
                                            if self.started_at:
                                                duration = (self.ended_at - self.started_at).total_seconds()
                                                self.performance_metrics['session_duration'] = duration
                                                self.performance_metrics['end_reason'] = reason
                                            
                                            self.save(update_fields=['is_active', 'status', 'ended_at', 'performance_metrics'])
                                    
                                    
                                    class PerformanceMetrics(models.Model):
                                        """Detailed performance metrics for real-time AI companion interactions."""
                                        
                                        METRIC_TYPES = [
                                            ('response_time', 'Total response time'),
                                            ('audio_processing', 'Audio processing time'),
                                            ('emotion_detection', 'Emotion detection time'),
                                            ('llm_first_token', 'LLM first token time'),
                                            ('tts_first_chunk', 'TTS first chunk time'),
                                            ('transcription', 'Speech-to-text time'),
                                            ('memory_retrieval', 'Memory retrieval time'),
                                        ]
                                        
                                        id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
                                        
                                        # Session context
                                        session = models.ForeignKey(
                                            StreamingSession,
                                            on_delete=models.CASCADE,
                                            related_name='detailed_metrics'
                                        )
                                        user = models.ForeignKey(
                                            settings.AUTH_USER_MODEL,
                                            on_delete=models.CASCADE,
                                            related_name='performance_metrics'
                                        )
                                        
                                        # Request identification
                                        request_id = models.CharField(
                                            max_length=255,
                                            help_text="Unique identifier for this request"
                                        )
                                        
                                        # Timing metrics (all in milliseconds)
                                        audio_processing_time = models.FloatField(
                                            null=True,
                                            blank=True,
                                            help_text="Time to process audio input (ms)"
                                        )
                                        emotion_detection_time = models.FloatField(
                                            null=True,
                                            blank=True,
                                            help_text="Time for emotion detection (ms)"
                                        )
                                        transcription_time = models.FloatField(
                                            null=True,
                                            blank=True,
                                            help_text="Time for speech-to-text (ms)"
                                        )
                                        memory_retrieval_time = models.FloatField(
                                            null=True,
                                            blank=True,
                                            help_text="Time to retrieve relevant memories (ms)"
                                        )
                                        llm_first_token_time = models.FloatField(
                                            null=True,
                                            blank=True,
                                            help_text="Time to first LLM token (ms)"
                                        )
                                        llm_total_time = models.FloatField(
                                            null=True,
                                            blank=True,
                                            help_text="Total LLM processing time (ms)"
                                        )
                                        tts_first_chunk_time = models.FloatField(
                                            null=True,
                                            blank=True,
                                            help_text="Time to first TTS audio chunk (ms)"
                                        )
                                        tts_total_time = models.FloatField(
                                            null=True,
                                            blank=True,
                                            help_text="Total TTS processing time (ms)"
                                        )
                                        total_response_time = models.FloatField(
                                            help_text="Total end-to-end response time (ms)"
                                        )
                                        
                                        # Quality metrics
                                        audio_quality_score = models.FloatField(
                                            null=True,
                                            blank=True,
                                            help_text="Audio input quality score (0.0-1.0)"
                                        )
                                        transcription_confidence = models.FloatField(
                                            null=True,
                                            blank=True,
                                            help_text="Transcription confidence score (0.0-1.0)"
                                        )
                                        emotion_confidence = models.FloatField(
                                            null=True,
                                            blank=True,
                                            help_text="Emotion detection confidence (0.0-1.0)"
                                        )
                                        
                                        # User satisfaction
                                        user_satisfaction_score = models.FloatField(
                                            null=True,
                                            blank=True,
                                            help_text="User satisfaction rating (1.0-5.0)"
                                        )
                                        user_feedback = models.TextField(
                                            blank=True,
                                            help_text="Optional user feedback"
                                        )
                                        
                                        # System information
                                        server_load = models.FloatField(
                                            null=True,
                                            blank=True,
                                            help_text="Server load at time of request"
                                        )
                                        concurrent_users = models.IntegerField(
                                            null=True,
                                            blank=True,
                                            help_text="Number of concurrent users"
                                        )
                                        
                                        # API usage
                                        groq_tokens_used = models.IntegerField(
                                            null=True,
                                            blank=True,
                                            help_text="Tokens used in Groq API call"
                                        )
                                        hume_api_calls = models.IntegerField(
                                            default=0,
                                            help_text="Number of Hume API calls made"
                                        )
                                        
                                        # Error information
                                        had_errors = models.BooleanField(default=False)
                                        error_details = models.JSONField(
                                            default=dict,
                                            help_text="Details of any errors encountered"
                                        )
                                        
                                        timestamp = models.DateTimeField(auto_now_add=True)
                                        
                                        class Meta:
                                            db_table = 'chat_performance_metrics'
                                            ordering = ['-timestamp']
                                            indexes = [
                                                models.Index(fields=['user', 'timestamp']),
                                                models.Index(fields=['session', 'timestamp']),
                                                models.Index(fields=['total_response_time']),
                                                models.Index(fields=['had_errors', 'timestamp']),
                                            ]
                                        
                                        def __str__(self):
                                            return f"{self.user.email} - {self.total_response_time}ms - {self.timestamp}"
                                        
                                        def meets_performance_target(self, target_ms=450):
                                            """Check if this interaction met the performance target."""
                                            return self.total_response_time <= target_ms
                                        
                                        def get_performance_breakdown(self):
                                            """Get a breakdown of where time was spent."""
                                            breakdown = {}
                                            
                                            if self.audio_processing_time:
                                                breakdown['audio_processing'] = self.audio_processing_time
                                            if self.emotion_detection_time:
                                                breakdown['emotion_detection'] = self.emotion_detection_time
                                            if self.transcription_time:
                                                breakdown['transcription'] = self.transcription_time
                                            if self.memory_retrieval_time:
                                                breakdown['memory_retrieval'] = self.memory_retrieval_time
                                            if self.llm_first_token_time:
                                                breakdown['llm_first_token'] = self.llm_first_token_time
                                            if self.tts_first_chunk_time:
                                                breakdown['tts_first_chunk'] = self.tts_first_chunk_time
                                            
                                            return breakdown
                                        
                                        @classmethod
                                        def get_average_metrics(cls, user=None, days=7):
                                            """Get average performance metrics for a user or system-wide."""
                                            from django.utils import timezone
                                            from django.db.models import Avg
                                            
                                            since = timezone.now() - timezone.timedelta(days=days)
                                            queryset = cls.objects.filter(timestamp__gte=since)
                                            
                                            if user:
                                                queryset = queryset.filter(user=user)
                                            
                                            return queryset.aggregate(
                                                avg_total_response_time=Avg('total_response_time'),
                                                avg_audio_processing_time=Avg('audio_processing_time'),
                                                avg_emotion_detection_time=Avg('emotion_detection_time'),
                                                avg_llm_first_token_time=Avg('llm_first_token_time'),
                                                avg_tts_first_chunk_time=Avg('tts_first_chunk_time'),
                                                avg_user_satisfaction=Avg('user_satisfaction_score'),
                                            )



---------