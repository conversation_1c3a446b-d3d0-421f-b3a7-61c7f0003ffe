# Audio Transcription Debug Guide

## Issue Analysis

The system is correctly sending audio to the backend and receiving responses from Groq Whisper, but the transcription result is just a period "." with low confidence (0.300). This indicates potential issues with audio quality, duration, or VAD sensitivity.

## Root Causes Identified

1. **VAD Over-Sensitivity**: The Voice Activity Detection (VAD) system was triggering on background noise or very short sounds
2. **Short Audio Duration**: Audio chunks being sent were too short for meaningful transcription
3. **Low Audio Quality**: Audio amplitude was too low or contained mostly silence
4. **Missing Audio Validation**: No quality checks before sending audio to the backend

## Improvements Made

### 1. VAD Configuration Adjustments

**File**: `lib/services/voice/realtime_voice_service.dart`

```dart
// Old configuration
static const double _vadPositiveThreshold = 0.6;
static const double _vadNegativeThreshold = 0.35;
static const int _vadMinSpeechFrames = 5;
static const int _vadRedemptionFrames = 8;

// New configuration - More conservative
static const double _vadPositiveThreshold = 0.7;  // Higher threshold to reduce false positives
static const double _vadNegativeThreshold = 0.4;  // Higher threshold to reduce noise triggering
static const int _vadMinSpeechFrames = 10;        // Require more frames before considering speech
static const int _vadRedemptionFrames = 15;       // More frames to confirm end of speech
```

### 2. Audio Quality Validation

Added comprehensive audio validation before sending to backend:

- **Minimum Amplitude Check**: Rejects audio with max amplitude < 0.02
- **Duration Check**: Rejects audio shorter than 0.5 seconds
- **Average Amplitude Check**: Rejects audio with average amplitude < 0.005
- **Enhanced Logging**: Better debugging information for audio quality

### 3. Audio Configuration Constants

**File**: `lib/core/constants/app_constants.dart`

```dart
// Audio Recording Settings
static const int audioSampleRate = 48000;
static const int audioChannels = 1;
static const int audioBitRate = 768000;
static const double minAudioAmplitude = 0.02;
static const double minAudioDuration = 0.5; // seconds
static const double minAvgAmplitude = 0.005;
```

### 4. Debug Tools

Created comprehensive debugging tools:

- **Audio Debug Widget**: `lib/widgets/debug/audio_debug_widget.dart`
- **Voice Settings Integration**: Added debug section to voice settings page
- **Test Recording Function**: Added `testAudioRecording()` method for debugging
- **Audio Playback Feature**: Record audio and listen back before sending to backend
- **WAV File Creation**: Converts PCM audio to playable WAV format for preview

## How to Debug Audio Issues

### Step 1: Access Debug Tools

1. Open the app
2. Navigate to Settings → Voice Settings
3. Scroll down to "Debug & Testing" section
4. Tap "Open Audio Debug Tool"

### Step 2: Test Audio Recording with Playback

1. In the debug tool, tap "Record & Preview"
2. Speak clearly for a few seconds
3. Tap "Stop Recording" when finished
4. **NEW**: Tap "Play Recording" to listen back to your audio
5. If the audio sounds good, tap "Send to Backend"
6. Check the debug logs for:
   - Audio quality metrics
   - VAD events
   - Transcription results

### Step 3: Monitor Audio Quality

Look for these key metrics in the logs:

```
🔄 PROCESSING: Audio quality analysis:
🔄   Max amplitude: 0.1234
🔄   Avg amplitude: 0.0567
🔄   RMS amplitude: 0.0789
🔄   Duration: 2.50s
```

**Good Audio Indicators**:
- Max amplitude: 0.1 - 0.8
- Avg amplitude: > 0.01
- Duration: > 1.0 seconds
- RMS amplitude: > 0.02

**Poor Audio Indicators**:
- Max amplitude: < 0.02 (too quiet)
- Avg amplitude: < 0.005 (mostly silence)
- Duration: < 0.5 seconds (too short)

### Step 4: Check VAD Events

Monitor VAD events for proper speech detection:

```
Speech start detected
Speech end detected
```

If you see too many false triggers, the environment might be too noisy.

## Troubleshooting Common Issues

### Issue: Getting "." transcriptions

**Possible Causes**:
- Speaking too quietly
- Background noise interference
- Very short speech segments
- Microphone issues
- **Sample rate mismatch** (FIXED)

**Solutions**:
1. Speak louder and clearer
2. Move to a quieter environment
3. Hold the microphone closer
4. Speak for at least 1-2 seconds

### Issue: Audio plays back too fast/slow

**Root Cause**: Sample rate mismatch between recording (16kHz) and WAV file header (48kHz)

**Fix Applied**:
- WAV files now correctly use 16kHz sample rate
- Audio should play back at normal speed
- This was causing transcription failures due to speed distortion

**How to Verify**:
1. Record audio using "Record & Preview"
2. Play back the recording
3. Audio should sound normal speed, not sped up

### Issue: No VAD events triggered

**Possible Causes**:
- Microphone permission denied
- Audio input not working
- VAD thresholds too high

**Solutions**:
1. Check microphone permissions
2. Test with other audio apps
3. Restart the app
4. Check device audio settings

### Issue: Frequent false VAD triggers

**Possible Causes**:
- Noisy environment
- VAD thresholds too low
- Audio interference

**Solutions**:
1. Move to quieter location
2. Adjust VAD sensitivity (if needed)
3. Check for audio feedback

## Backend Considerations

The backend expects:
- **Sample Rate**: 16kHz (FIXED: was incorrectly set to 48kHz causing 3x speed playback)
- **Format**: PCM16 (16-bit)
- **Channels**: Mono (1 channel)
- **Encoding**: Base64 encoded audio data

**IMPORTANT FIX**: The audio was being recorded at 16kHz but WAV files were created with 48kHz headers, causing audio to play back at 3x speed and fail transcription. This has been corrected.

## Performance Monitoring

The logs show performance metrics:
- **Transcription Time**: Should be < 1000ms
- **Audio Processing Time**: Should be < 100ms
- **Emotion Detection Time**: Should be < 100ms

If these exceed targets, it may indicate backend performance issues.

## New Playback Feature

The debug tool now includes audio playback functionality:

### Recording Controls:
- **Record & Preview**: Start recording with playback capability
- **Stop Recording**: End the recording session
- **Quick Test**: Original test recording function

### Playback Controls:
- **Play Recording**: Listen to your recorded audio
- **Stop Playback**: Stop audio playback
- **Send to Backend**: Send the audio for transcription after preview
- **Mark as Transcription**: Manually mark current recording as transcription audio

### Transcription Audio Playback:
- **Play Transcription Audio**: Listen to the exact audio that was sent to backend for transcription
- Automatically available when backend transcription is received
- Shows in blue section under "Transcription Results"

### Benefits:
- **Quality Verification**: Hear exactly what will be sent to the backend
- **Audio Debugging**: Identify issues like low volume, noise, or distortion
- **Confidence Building**: Ensure your speech is clear before transcription
- **Transcription Audio Playback**: Listen to the exact audio that produced a specific transcription result
- **Real-time Feedback**: See transcription results and immediately play back the corresponding audio

## Next Steps

1. Test the improved configuration with real speech
2. Use the new playback feature to verify audio quality
3. Monitor the debug logs for audio quality metrics
4. Adjust VAD thresholds if needed based on environment
5. Consider adding noise reduction if background noise is persistent
6. Implement audio preprocessing filters if quality issues persist

## Additional Debug Commands

For advanced debugging, you can add these to the debug widget:

```dart
// Test specific audio scenarios
await _voiceService.testAudioRecording();

// Monitor real-time audio levels
_voiceService.vadEventsStream.listen((event) {
  print('VAD Event: $event');
});

// Check transcription confidence
_voiceService.transcriptionStream.listen((result) {
  print('Transcription: "${result.transcription}" (confidence: ${result.confidence})');
});
```
