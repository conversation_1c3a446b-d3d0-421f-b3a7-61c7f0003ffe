# Authentication Setup Guide

## Required API Keys and Configuration

### 1. Google Sign-In Setup

#### Google Cloud Console Setup:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google Sign-In API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"

#### Create OAuth Credentials:
**For Android:**
- Application type: Android
- Package name: `com.example.ellahai`
- SHA-1 certificate fingerprint: Get this by running:
  ```bash
  keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
  ```

**For iOS:**
- Application type: iOS
- Bundle ID: `com.example.ellahai`

#### Download Configuration Files:
- **Android**: Download `google-services.json` and place in `android/app/`
- **iOS**: Download `GoogleService-Info.plist` and place in `ios/Runner/`

#### Update iOS Configuration:
1. Open `ios/Runner/Info.plist`
2. Replace `YOUR_REVERSED_CLIENT_ID_HERE` with the `REVERSED_CLIENT_ID` from your `GoogleService-Info.plist`

### 2. Apple Sign-In Setup

#### Apple Developer Console:
1. Go to [Apple Developer Console](https://developer.apple.com/)
2. Go to "Certificates, Identifiers & Profiles"
3. Select your App ID
4. Enable "Sign In with Apple" capability
5. Configure your app's domain and return URLs

#### Backend Configuration:
Your current Apple Sign-In backend implementation has security issues. Here's what needs to be fixed:

```python
# SECURITY ISSUE: Current code decodes JWT without verification
decoded_token = jwt.decode(identity_token, options={"verify_signature": False})

# PROPER IMPLEMENTATION: Verify with Apple's public keys
import requests
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import rsa
import jwt

def verify_apple_token(identity_token):
    # Get Apple's public keys
    apple_keys_url = "https://appleid.apple.com/auth/keys"
    response = requests.get(apple_keys_url)
    apple_keys = response.json()
    
    # Decode header to get key ID
    header = jwt.get_unverified_header(identity_token)
    key_id = header['kid']
    
    # Find the matching key
    apple_key = None
    for key in apple_keys['keys']:
        if key['kid'] == key_id:
            apple_key = key
            break
    
    if not apple_key:
        raise ValueError("Unable to find matching key")
    
    # Convert JWK to PEM format and verify token
    # ... (implementation details for JWK to PEM conversion)
    
    # Verify the token
    decoded_token = jwt.decode(
        identity_token,
        key=public_key,
        algorithms=['RS256'],
        audience='your.app.bundle.id',
        issuer='https://appleid.apple.com'
    )
    
    return decoded_token
```

### 3. Backend Environment Variables

Add these to your Django settings or environment variables:

```python
# settings.py or .env file

# Google OAuth
GOOGLE_OAUTH_CLIENT_ID = "your-google-client-id"
GOOGLE_OAUTH_CLIENT_SECRET = "your-google-client-secret"

# Apple Sign-In
APPLE_TEAM_ID = "your-apple-team-id"
APPLE_KEY_ID = "your-apple-key-id"
APPLE_PRIVATE_KEY = "your-apple-private-key"
APPLE_BUNDLE_ID = "com.example.ellahai"

# JWT Settings
JWT_SECRET_KEY = "your-jwt-secret-key"
JWT_ALGORITHM = "HS256"
JWT_ACCESS_TOKEN_LIFETIME = 3600  # 1 hour
JWT_REFRESH_TOKEN_LIFETIME = 604800  # 7 days
```

### 4. Required Python Packages

Add these to your Django requirements:

```txt
PyJWT==2.8.0
cryptography==41.0.7
requests==2.31.0
google-auth==2.23.4
google-auth-oauthlib==1.1.0
```

### 5. Flutter Configuration Files Needed

#### Create these files with your actual credentials:

**android/app/google-services.json** (from Google Cloud Console)
**ios/Runner/GoogleService-Info.plist** (from Google Cloud Console)

### 6. Security Recommendations

1. **Never commit API keys to version control**
2. **Use environment variables for sensitive data**
3. **Implement proper JWT token verification**
4. **Add rate limiting to authentication endpoints**
5. **Use HTTPS in production**
6. **Implement proper error handling without exposing sensitive information**

### 7. Testing Authentication

1. **Google Sign-In**: Test on both Android and iOS devices
2. **Apple Sign-In**: Test only on iOS devices (Apple requirement)
3. **Email/Password**: Test registration and login flows
4. **Backend Integration**: Verify tokens are properly validated

### 8. Production Checklist

- [ ] Google OAuth credentials configured for production domains
- [ ] Apple Sign-In configured with production bundle ID
- [ ] Backend JWT verification implemented properly
- [ ] Environment variables set in production
- [ ] HTTPS enabled
- [ ] Error logging configured
- [ ] Rate limiting implemented
- [ ] Security headers configured

## Current Status

✅ **Flutter Frontend**: Properly configured with backend integration
✅ **State Management**: Riverpod-based authentication provider
✅ **UI Components**: Authentication screens implemented
❌ **Google Credentials**: Need google-services.json and GoogleService-Info.plist
❌ **Apple Verification**: Backend needs proper JWT verification
❌ **Environment Variables**: Need to be configured
❌ **Production Security**: Needs implementation

## Next Steps

1. Set up Google OAuth credentials and download configuration files
2. Fix Apple Sign-In JWT verification in backend
3. Configure environment variables
4. Test authentication flows
5. Implement production security measures
