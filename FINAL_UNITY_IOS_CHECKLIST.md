# Final Unity iOS Integration Checklist

Since you've completed the iOS setup, here's a final checklist to ensure everything works:

## ✅ Unity Scene Setup (Completed)
- [x] UnityMessageManager component is added to your GameObject (visible in your screenshot)
- [x] EllahAIManager script is attached to the same GameObject
- [x] FlutterMessageHandler.cs has been removed (no longer needed)

## ✅ Unity Project Verification

### 1. Check Unity Build Settings
In Unity Editor:
- File > Build Settings
- Platform: iOS
- Player Settings > Other Settings:
  - Scripting Backend: IL2CPP
  - Target Device: iPhone + iPad
  - Architecture: ARM64

### 2. Verify Scene in Build
- Make sure your main scene is added to "Scenes in Build"
- The scene should contain your GameObject with both UnityMessageManager and EllahAIManager

### 3. Re-export Unity Project
- Go to Flutter > Export iOS
- This ensures the latest changes are exported to ios/UnityLibrary

## ✅ iOS Project Verification

### 1. Xcode Workspace Structure
Open `ios/Runner.xcworkspace` and verify:
- [x] Unity-iPhone.xcodeproj is in the workspace
- [x] UnityFramework.framework is in "Frameworks, Libraries, and Embedded Content"
- [x] UnityFramework is set to "Embed & Sign"

### 2. Build Settings Check
In Runner target > Build Settings:
- [x] Other Linker Flags includes: `-framework UnityFramework`
- [x] Framework Search Paths includes: `$(PROJECT_DIR)/UnityLibrary`
- [x] ONLY_ACTIVE_ARCH = NO
- [x] VALID_ARCHS = arm64

### 3. AppDelegate Configuration
Your `ios/Runner/AppDelegate.swift` should have:
```swift
import flutter_unity_widget
InitUnityIntegrationWithOptions(argc: CommandLine.argc, argv: CommandLine.unsafeArgv, launchOptions)
```

## 🔧 Final Build Steps

### 1. Clean Everything
```bash
# In your Flutter project root
flutter clean
rm -rf ios/build
cd ios && pod install && cd ..
```

### 2. Re-export Unity (if needed)
- Open Unity Editor
- Go to Flutter > Export iOS
- Wait for export to complete

### 3. Clean Xcode
- Open `ios/Runner.xcworkspace`
- Product > Clean Build Folder
- Close Xcode

### 4. Build iOS
```bash
flutter build ios --release
```

Or build directly in Xcode:
- Open `ios/Runner.xcworkspace`
- Select your target device
- Product > Build

## 🐛 If You Still Get "_SendMessageToFlutter" Error

### Check Native Bridge
The error means the native iOS bridge isn't properly linked. Verify:

1. **UnityFramework Linking:**
   - In Xcode, select Runner target
   - General tab > "Frameworks, Libraries, and Embedded Content"
   - UnityFramework.framework should be there and set to "Embed & Sign"

2. **Plugin Files:**
   ```bash
   # Check if flutter_unity_widget iOS files exist
   ls ios/.symlinks/plugins/flutter_unity_widget/ios/Classes/
   ```

3. **Manual Bridge Check:**
   If the plugin files are missing, you might need to:
   - Delete `ios/.symlinks`
   - Run `flutter pub get`
   - Run `cd ios && pod install`

## 🎯 Testing

### 1. Test Communication
In your Flutter app, try sending a message to Unity:
```dart
_unityWidgetController?.postMessage('EllahAIManager', 'ReceiveCommand', jsonMessage);
```

### 2. Test Unity to Flutter
Your Unity script should send messages like:
```csharp
SendMessageToFlutter("avatar_ready");
```

## 📱 Device Testing

- Test on a physical iOS device (not simulator)
- Simulator has limited Unity support
- Make sure your device is properly provisioned

## 🔍 Debug Tips

### Unity Console
- Check Unity Console for any initialization errors
- Look for "EllahAIManager initialized successfully" message

### Xcode Console
- Watch for any native bridge errors
- Look for UnityFramework loading messages

### Flutter Debug
- Use `onUnityMessage` callback to verify messages from Unity
- Add debug prints in your Flutter Unity widget callbacks

## ✅ Success Indicators

You'll know it's working when:
1. iOS build completes without linker errors
2. Unity scene loads in your Flutter app
3. You can send messages from Flutter to Unity
4. You receive messages from Unity in Flutter
5. No "_SendMessageToFlutter" undefined symbol errors

If you're still getting the error after all this, the issue might be with the specific flutter_unity_widget version or Unity version compatibility.